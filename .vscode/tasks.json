{
   "version": "2.0.0",
   "tasks": [
      {
         "label": "Watch and compile TypeScript",
         "type": "shell",
         "command": "npm run watch",
         "isBackground": true,
         "presentation": {
            "reveal": "never",
            "echo": false,
            "focus": false,
            "panel": "dedicated"
         },
         "runOptions": {
            "runOn": "folderOpen"
         },
         "problemMatcher": "$tsc-watch"
      },
      {
         "label": "Run Dev Server",
         "type": "shell",
         "command": "pnpm run dev",
         "isBackground": true,
         "presentation": {
            "reveal": "never",
            "echo": false,
            "focus": false,
            "panel": "dedicated"
         },
         "runOptions": {
            "runOn": "folderOpen"
         },
         "problemMatcher": []
      }
      {
         "label": "Run Server",
         "type": "shell",
         "command": "pnpm run start",
         "options": {
            "cwd": "server"
         },
         "isBackground": true,
         "presentation": {
            "reveal": "never",
            "echo": false,
            "focus": false,
            "panel": "dedicated"
         },
         "runOptions": {
            "runOn": "folderOpen"
         },
         "problemMatcher": []
      }
   ]
}