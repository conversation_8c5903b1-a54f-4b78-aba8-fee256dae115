export const FR = {
   About: "À propos de CivIdle",
   AbuSimbel: "Abu Simbel",
   AbuSimbelDesc: "Double l'effet de Ramsès II. Toutes les Merveilles adjacentes gagnent +1 Bonheur",
   AccountActiveTrade: "Échange actif",
   AccountChatBadge: "Badge de chat",
   AccountCustomColor: "Couleur personnalisée",
   AccountCustomColorDefault: "Par défaut",
   AccountGreatPeopleLevelRequirement: "Niveau de Personnages Illustres Requis",
   AccountLevel: "Rang de compte",
   AccountLevelAedile: "<PERSON>di<PERSON>",
   AccountLevelConsul: "Consul",
   AccountLevelMod: "Modérateur",
   AccountLevelPlayTime: "Temps de jeu en ligne > %{requiredTime} (Votre temps de jeu est de %{actualTime})",
   AccountLevelPraetor: "<PERSON><PERSON><PERSON><PERSON>",
   AccountLevelQuaestor: "Questeur",
   AccountLevelSupporterPack: "Posséder le Supporter Pack",
   AccountLevelTribune: "Tribun",
   AccountLevelUpgradeConditionAnyHTML: "Pour améliorer votre compte, vous devez seulement remplir l'<b>un des deux critères suivants</b> :",
   AccountPlayTimeRequirement: "Temps de Jeu Requis",
   AccountRankUp: "Mettre à niveau le Rang du Compte",
   AccountRankUpDesc: "Tout votre progrès sera transféré vers votre nouveau rang",
   AccountRankUpTip: "Félicitations, votre compte est éligible pour un rang supérieur - cliquez ici pour le mettre à niveau !",
   AccountSupporter: "Supporter Pack Owner",
   AccountTradePriceRange: "Fourchette de prix",
   AccountTradeTileReservationTime: "Réservation de votre case sur la carte commerciale",
   AccountTradeTileReservationTimeDesc: "Il s'agit de la durée pendant laquelle votre case sur la carte commerciale vous sera réservée depuis votre dernière connexion. Passé ce délai, votre case deviendra disponible pour les autres joueurs",
   AccountTradeValuePerMinute: "Valeur des échanges par minute",
   AccountTypeShowDetails: "Afficher les détails du compte",
   AccountUpgradeButton: "Passer au rang de Questeur",
   AccountUpgradeConfirm: "Amélioration du compte",
   AccountUpgradeConfirmDescV2: "Améliorer votre compte <b>réinitialisera votre partie actuelle</b> mais vos personnages illustres jusqu'aux niveaux autorisés seront conservés. Cette action est <b>irréversible</b>, voulez-vous vraiment continuer ?",
   Acknowledge: "Acknowledge",
   Acropolis: "Acropolis",
   ActorsGuild: "Guilde des acteurs",
   AdaLovelace: "Ada Lovelace",
   AdamSmith: "Adam Smith",
   AdjustBuildingCapacity: "Capacité de Production",
   AdvisorElectricityContent:
      " Les centrales électriques vous fournissent deux nouveaux systèmes. Le premier, 'Énergie', est indiqué par les tuiles en forme d’éclair adjacentes à la centrale. Certains bâtiments (à partir de la Radio pendant les Guerres mondiales) affichent un indicateur 'nécessite de l'énergie' dans leur liste d’entrées. <b>Cela signifie qu’ils doivent être construits sur une tuile en forme d’éclair pour fonctionner</b>. Les bâtiments nécessitant de l’énergie et en disposant transmettent également de l’énergie aux tuiles adjacentes à ce bâtiment, ce qui permet de les alimenter mutuellement tant qu’au moins l’un d’entre eux est connecté à une centrale.<br><br>L'autre système, 'électrification', peut être appliqué à <b>n'importe quel bâtiment, n'importe où</b> sur la carte, tant qu’il ne produit ni science ni travailleurs. Cela utilise l’énergie générée par la centrale pour augmenter à la fois la consommation et la production du bâtiment. Des niveaux d’électrification plus élevés nécessitent des quantités d’énergie de plus en plus importantes. Électrifier des bâtiments qui nécessitent déjà de l'énergie est plus efficace que d'électrifier ceux qui n'en ont pas besoin.",
   AdvisorElectricityTitle: "Énergie et Électrification",
   AdvisorGreatPeopleContent:
      "À chaque fois que vous entrez dans une nouvelle ère technologique, vous pourrez sélectionner une Grande Personne de cette ère, ainsi que de chaque ère précédente. Ces Grandes Personnes offrent des bonus globaux qui peuvent augmenter la production, la science, le bonheur et bien d'autres aspects.<br><br>Ces bonus sont permanents pour le reste de la renaissance. Lorsque vous effectuez une renaissance, toutes vos Grandes Personnes deviennent permanentes, et leurs bonus durent pour toujours.<br><br>Choisir la même Grande Personne lors d'une partie ultérieure cumulera votre bonus permanent et le bonus en cours de partie, et lorsque vous faites une renaissance avec des doublons, les extras sont stockés et peuvent être utilisés pour améliorer le bonus permanent. Cela est accessible dans le menu <b>Gérer les Grandes Personnes Permanentes</b> dans votre Bâtiment Principal.",
   AdvisorGreatPeopleTitle: "Personnages Illustres",
   AdvisorHappinessContent:
      "Le bonheur est la mécanique centrale dans CivIdle qui limite l'expansion. Vous gagnez du bonheur en débloquant de nouvelles technologies, en progressant dans de nouvelles ères, en construisant des merveilles, grâce aux Personnages Illustres qui en fournissent, et de quelques autres manières que vous découvrirez au fil du jeu. <b>Chaque nouveau bâtiment coûte 1 point de bonheur</b>. Pour chaque point au-dessus/en dessous de 0 en bonheur, vous obtenez un bonus ou une pénalité de 2 % sur votre total de travailleurs (limité à -50 et +50 en bonheur). Vous pouvez voir un aperçu détaillé de votre bonheur dans la <b>section Bonheur de votre Bâtiment Principal</b>.",
   AdvisorHappinessTitle: "Gardez votre peuple heureux",
   AdvisorOkay: "Compris, merci !",
   AdvisorScienceContent:
      "Vos travailleurs occupés génèrent de la science, ce qui vous permet de débloquer de nouvelles technologies et de faire progresser votre civilisation. Vous pouvez accéder au menu de recherche de plusieurs façons : en cliquant sur le compteur de science, en accédant aux technologies déblocables dans votre Bâtiment Principal, ou en utilisant le menu 'Voir'. Tous ces moyens vous amènent à l'arbre technologique, où vous verrez toutes les technologies ainsi que la quantité de science requise pour chacune. Si vous disposez de suffisamment de science pour apprendre une nouvelle technologie, il vous suffit de cliquer dessus et de presser 'débloquer' dans le menu latéral. <b>Chaque nouveau palier et chaque nouvelle ère technologique nécessitent de plus en plus de science, mais vous débloquerez également de nouvelles et meilleures façons d'en gagner.</b>",
   AdvisorScienceTitle: "Découverte Scientifique !",
   AdvisorSkipAllTutorials: "Passer Tous les Tutoriels",
   AdvisorStorageContent:
      "Bien que les bâtiments disposent d'une capacité de stockage raisonnable, ils peuvent se remplir, surtout s’ils restent inactifs pendant longtemps. <b>Lorsque les bâtiments sont pleins, ils ne peuvent plus produire</b>. Cela n'est pas toujours un problème, car vous avez visiblement un grand stock puisque le bâtiment est plein. Mais il est généralement préférable de maintenir la production.<br><br>Un moyen de gérer un stockage plein est d'utiliser un entrepôt. Lorsque vous construisez un entrepôt, vous accédez à un menu listant tous les produits découverts, et vous pouvez configurer l’entrepôt pour qu’il prenne des produits en quantités quelconques, tant que le total de tous les produits reste dans la limite de ce que l’entrepôt peut tirer en fonction de son niveau et de son multiplicateur de stockage.<br><br>Un moyen simple de configurer un entrepôt est de cocher chaque produit que vous souhaitez y importer, puis d’utiliser les boutons 'redistribuer parmi la sélection' pour répartir équitablement votre taux d’importation et de stockage. Si vous souhaitez que les bâtiments puissent également puiser dans l’entrepôt, assurez-vous d’activer l’option 'exporter sous le seuil maximum'.",
   AdvisorStorageTitle: "Stockage et Entrepôts",
   AdvisorTraditionContent:
      "Certaines merveilles (Chogha Zanbil, Temple de Louxor, Big Ben) offrent l'accès à une nouvelle série d'options, vous permettant de personnaliser le chemin de votre renaissance. Chacune permet de choisir parmi 1 des 4 options pour la tradition, la religion et l’idéologie de votre civilisation respectivement.<br><br>Une fois que vous avez fait votre choix, celui-ci est verrouillé pour cette renaissance, bien que vous puissiez en choisir d'autres lors de renaissances futures. Une fois sélectionnée, chaque option peut également être améliorée plusieurs fois en fournissant les ressources nécessaires. Les récompenses de chaque palier sont cumulatives, donc si le Palier 1 accorde +1 en production pour X et le Palier 2 accorde également +1 en production pour X, vous aurez un total de +2 en production pour X au Palier 2.",
   AdvisorTraditionTitle: "Choisir des chemins et merveilles améliorables",
   AdvisorWonderContent:
      "Les merveilles sont des bâtiments spéciaux qui fournissent des effets globaux pouvant avoir un impact significatif sur votre jeu. En plus de leurs fonctions listées, toutes les merveilles donnent également +1 en bonheur. Soyez toutefois vigilant, car <b>les merveilles nécessitent BEAUCOUP de matériaux et une capacité de bâtisseur plus élevée que la normale</b>. Cela signifie qu'elles peuvent facilement vider vos stocks des ressources nécessaires, laissant vos autres bâtiments en manque. <b>Vous pouvez activer et désactiver chaque ressource librement</b>, ce qui vous permet de construire la merveille par étapes tout en stockant suffisamment de matériaux pour que tout fonctionne.",
   AdvisorWonderTitle: "Merveilles du Monde ",
   AdvisorWorkerContent:
      "Chaque fois qu'un bâtiment produit ou transporte des biens, cela nécessite des travailleurs. Si vous n'avez pas assez de travailleurs disponibles, certains bâtiments ne pourront pas fonctionner lors de ce cycle. La solution évidente est d'augmenter votre nombre total de travailleurs disponibles en construisant ou en améliorant des structures qui produisent des travailleurs (Cabane/Maison/Appartement/Condo).<br><br><b>Sachez cependant que les bâtiments se désactivent pendant leur amélioration et ne peuvent fournir aucune de leurs ressources, y compris les travailleurs. Il peut donc être judicieux de n’améliorer qu’un bâtiment de logement à la fois.</b> Un bon objectif pour les premières phases du jeu est de maintenir environ 70 % de vos travailleurs occupés. Si plus de 70 % sont occupés, améliorez/construisez des logements. Si moins de 70 % sont occupés, étendez la production.",
   AdvisorWorkerTitle: "Gestion des Travailleurs",
   Aeschylus: "Eschyle",
   Agamemnon: "Agamemnon",
   AgeWisdom: "Sagesse des l'âges",
   AgeWisdomDescHTML: "Chaque niveau de Sagesse des Âges fournit <b>un niveau équivalent</b> de Personnages Illustres Permanents éligibles de cette ère – il peut être amélioré avec des fragments de Personnages Illustres Permanents éligibles.",
   AgeWisdomGreatPeopleShardsNeeded: "Il vous manque encore %{amount} fragments de personnages illustres pour la prochaine amélioration de Sagesse des Âges.",
   AgeWisdomGreatPeopleShardsSatisfied: "Vous avez suffisamment de fragments de personnages illustres pour la prochaine amélioration de Sagesse des Âges.",
   AgeWisdomNeedMoreGreatPeopleShards: "Besoin de plus de fragments de personnages illustres",
   AgeWisdomNotEligible: "Ce personnage illustre n'est pas éligible pour la Sagesse des Âges",
   AgeWisdomSource: "%{age} Sagesse: %{person}",
   AgeWisdomUpgradeWarningHTMLV3: "Age Wisdom <b>does not carry over</b> when upgrading from Tribune to Quaestor",
   AGreatPersonIsBorn: "Un personnage illustre apparait",
   AircraftCarrier: "Porte-avions",
   AircraftCarrierYard: "Chantier de porte-avions",
   Airplane: "Avion",
   AirplaneFactory: "Usine d'avions",
   Akitu: "Akitu : le Ziggurat d'Ur et le fleuve Euphrate s'appliquent aux bâtiments débloqués dans l'ère actuelle.",
   AlanTuring: "Alan Turing",
   AlanTuringDesc: "+%{value} Science provenant des travailleurs inactifs",
   AlbertEinstein: "Albert Einstein",
   Alcohol: "Alcool",
   AldersonDisk: "Alderson Disk",
   AldersonDiskDesc: "+25 Bonheur. Cette merveille peut être améliorée, et chaque amélioration supplémentaire procure +5 Bonheur.",
   Alloy: "Alliage",
   Alps: "Alpes",
   AlpsDesc: "À chaque fois que le niveau d'un bâtiment est un compte rond, il gagne +1 Capacité de Production (+1 Multiplicateur de Consommation, +1 Multiplicateur de Production)",
   Aluminum: "Aluminium",
   AluminumSmelter: "Aluminerie",
   AmeliaEarhart: "Amelia Earhart",
   American: "American",
   AndrewCarnegie: "Andrew Carnegie",
   AngkorWat: "Angkor Vat",
   AngkorWatDesc: "Tous les bâtiments adjacents gagnent +1 Multiplicateur de Capacité des Ouvriers. Octroie 1 000 ouvriers",
   AntiCheatFailure: "Le niveau de votre compte a été restreint en raison de l'<b>incapacité à passer le test anti-triche</b>. Contactez le développeur si vous souhaitez contester cette décision",
   AoiMatsuri: "Aoi Matsuri : le mont Fuji génère deux fois plus de distorsion.",
   Apartment: "Appartement",
   Aphrodite: "Aphrodite",
   AphroditeDescV2: " +1 multiplicateur de capacité de bâtisseur pour chaque niveau lors de l'amélioration des bâtiments au-delà du niveau 20. Tous les personnages illustres permanents de l'Âge Classique débloqués obtiennent +1 niveau lors de cette partie.",
   ApolloProgram: "Programme Apollo",
   ApolloProgramDesc: "Toutes les usines de fusées gagnent +2 Production, Capacité des Ouvriers et Multiplicateur de Stockage. Les usines de satellites, les usines de vaisseaux spatiaux et les silos à missiles nucléaires gagnent +1 Multiplicateur de Production pour chaque usine de fusées adjacente",
   ApplyToAll: "Appliquer à tous",
   ApplyToAllBuilding: "Appliquer aux %{building}",
   ApplyToBuildingInTile: "Appliquer à tou(te)s les %{building} dans un rayon de %{tile} case(s)",
   ApplyToBuildingsToastHTML: "Appliqué à <b>%{count} %{building}</b> avec succès",
   Aqueduct: "Aqueduc",
   ArcDeTriomphe: "Arc de Triomphe",
   ArcDeTriompheDescV2: "Every 1 happiness (capped) provides +1 builder capacity to all buildings",
   Archimedes: "Archimède",
   Architecture: "Architecture",
   Aristophanes: "Aristophane",
   AristophanesDesc: "+%{value} Bonheur",
   Aristotle: "Aristotle",
   Arithmetic: "Arithmétique",
   Armor: "Armure",
   Armory: "Armurerie",
   ArtificialIntelligence: "Intelligence artificielle",
   Artillery: "Artillerie",
   ArtilleryFactory: "Usine d'artillerie",
   AshokaTheGreat: "Ashoka",
   Ashurbanipal: "Assurbanipal",
   Assembly: "Assemblée",
   Astronomy: "Astronomie",
   AtomicBomb: "Bombe atomique",
   AtomicFacility: "Atomic Facility",
   AtomicTheory: "Théorie atomique",
   Atomium: "Atomium",
   AtomiumDescV2:
      "Tous les bâtiments qui produisent de la science dans un rayon de 2 cases gagnent +5 Multiplicateur de Production. Génère une quantité de science égale à la production de science dans un rayon de 2 cases. Une fois sa construction achevée, octroie une quantité de science unique équivalente au coût de la technologie débloquée la plus chère",
   Autocracy: "Autocratie",
   Aviation: "Aviation",
   Babylonian: "Babylonien",
   BackToCity: "Retourner à la ville",
   BackupRecovery: "Récupération de sauvegarde",
   Bakery: "Boulangerie",
   Ballistics: "Balistique",
   Bank: "Banque",
   Banking: "Gestion banquaire",
   BankingAdditionalUpgrade: "Tous les bâtiments de niveau 10 ou supérieur gagnent +1 Multiplicateur de Stockage",
   Banknote: "Billet",
   BaseCapacity: "Capacité de base",
   BaseConsumption: "Consommation de base",
   BaseMultiplier: "Multiplicateur de base",
   BaseProduction: "Production de base",
   BastilleDay: "Bastille Day: Doublez l'effet du Centre Pompidou et de l'Arc de Triomphe. double la génération culturelle du Mont Saint-Michel",
   BatchModeTooltip: "Vous avez actuellement sélectionné %{count} bâtiment(s). L'amélioration s'appliquera à tous les bâtiments sélectionnés",
   BatchSelectAllSameType: "Tous du même type",
   BatchSelectAnyType1Tile: "Tout type dans un rayon de 1 case",
   BatchSelectAnyType2Tile: "Tout type dans un rayon de 2 cases",
   BatchSelectAnyType3Tile: "Tout type dans un rayon de 3 cases",
   BatchSelectSameType1Tile: "Même type dans un rayon de 1 case",
   BatchSelectSameType2Tile: "Même type dans un rayon de 2 cases",
   BatchSelectSameType3Tile: "Même type dans un rayon de 3 cases",
   BatchSelectSameTypeSameLevel: "Même type, même niveau",
   BatchSelectThisBuilding: "Ce bâtiment",
   BatchStateSelectActive: "Active",
   BatchStateSelectAll: "Tous",
   BatchStateSelectTurnedFullStorage: "Stockage complet",
   BatchStateSelectTurnedOff: "Éteint",
   BatchUpgrade: "Amélioration groupée ",
   Battleship: "Navire de guerre",
   BattleshipBuilder: "Constructeur de navires de guerre",
   BigBen: "Big Ben",
   BigBenDesc: "+2 Science provenant des travailleurs occupés. Choisissez une idéologie d'empire, débloquez davantage de bonus avec chaque choix. ",
   Biplane: "Biplan",
   BiplaneFactory: "Usine de biplans",
   Bitcoin: "Bitcoin",
   BitcoinMiner: "Miner de Bitcoin",
   BlackForest: "Forêt Noire",
   BlackForestDesc: "Lorsqu'elle est découverte, révèle toutes les tuiles de bois sur la carte. Fait apparaître du bois sur les tuiles adjacentes. Tous les bâtiments consommant du bois ou du bois d'œuvre reçoivent +5 en Multiplicateur de Production.",
   Blacksmith: "Atelier d'outillage",
   Blockchain: "Blockchain",
   BlueMosque: "Blue Mosque",
   BlueMosqueDesc: "All wonders provide +1 Production, Worker Capacity and Storage Multiplier to adjacent buildings. When constructed next to Hagia Sophia, provide extra +1 Production, Worker Capacity and Storage Multiplier",
   BobHope: "Bob Hope",
   BobHopeDesc: "+%{value} Bonheur",
   Bond: "Obligation financière",
   BondMarket: "Marché obligatoire",
   Book: "Livre",
   BoostCyclesLeft: "Cycles de boost restants",
   BoostDescription: "+%{value} %{multipliers} pour %{buildings}",
   Borobudur: "Borobudur",
   BorobudurDesc: "Borobudur",
   BranCastle: "Château de Bran",
   BranCastleDesc: "Château de Bran",
   BrandenburgGate: "Porte de Brandebourg",
   BrandenburgGateDesc: "Toutes les mines de charbon et les puits de pétrole gagnent +1 Production, Stockage et Multiplicateur de Capacité des Ouvriers. Les raffineries de pétrole gagnent +1 Production, Stockage et Multiplicateur de Capacité des Ouvriers pour chaque case de pétrole adjacente",
   Bread: "Pain",
   Brewery: "Brasserie",
   Brick: "Brique",
   Brickworks: "Briqueterie",
   BritishMuseum: "British Museum",
   BritishMuseumChooseWonder: "Choisir une merveille",
   BritishMuseumDesc: "Une fois construit, peut se transformer en une merveille unique d'autres civilisations",
   BritishMuseumTransform: "Transformer",
   Broadway: "Broadway",
   BroadwayCurrentlySelected: "Actuellement sélectionné",
   BroadwayDesc: "Un personnage illustre de l'ère actuel et un autre de l'ère précèdente apparaissent. Une fois le choix entre les deux fait, son effet est doublé",
   BronzeAge: "Âge de bronze",
   BronzeTech: "Bronze",
   BuddhismLevelX: "Buddhism %{level}",
   Build: "Bâtir",
   BuilderCapacity: "Capacité de construction",
   BuildingColor: "Couleur des bâtiments",
   BuildingColorMatchBuilding: "Copier la couleur des bâtiments",
   BuildingColorMatchBuildingTooltip: "Les ressources seront automatiquement de la même couleur que le bâtiment qui les produit. Si plusieurs bâtiments produisent la même ressource, la couleur sera attribuée aléatoirement",
   BuildingDefaults: "Paramètres par défaut des bâtiments",
   BuildingDefaultsCount: "%{count} propriétés sont remplacées dans les paramètres par défaut du bâtiment",
   BuildingDefaultsRemove: "Supprimer toutes les restrictions de propriété",
   BuildingEmpireValue: "Valeur de l'Empire des Bâtiments / Valeur de l'Empire des Ressources",
   BuildingMultipliers: "Bonus",
   BuildingName: "Nom",
   BuildingNoMultiplier: "%{building} <b>n'est affecté(e)</b> par aucun multiplicateur (production, capacité des ouvriers, stockage, etc)",
   BuildingSearchText: "Entrez le nom du bâtiment que vous recherchez",
   BuildingTier: "Catégorie",
   Cable: "Câble",
   CableFactory: "Usine de câbles",
   Calendar: "Calendrier",
   CambridgeUniversity: "Université de Cambridge",
   CambridgeUniversityDesc: "+1 niveau de Sagesse des Âges pour la Renaissance et les ères suivantes",
   CambridgeUniversitySource: "Université de Cambridge (%{age})",
   Cancel: "Annuler",
   CancelAllUpgradeDesc: "Annuler toute %{building} les Améliorations",
   CancelUpgrade: "Annuler l'amélioration",
   CancelUpgradeDesc: "Toutes les ressources déjà été transportées resteront dans le stockage",
   Cannon: "Canon",
   CannonWorkshop: "Fabrique de canons",
   CannotEarnPermanentGreatPeopleDesc: "Vous ne pouvez pas obtenir de personnages illustres permanents car vous êtes en mode difficile",
   Capitalism: "Capitalisme",
   Cappadocia: "Cappadocia",
   CappadociaDesc: "All buildings within 3 tile range get +1 Production, Worker Capacity and Storage Multiplier for every level above Level 30",
   Car: "voiture",
   Caravansary: "Caravansérail",
   CaravansaryDesc: "Permet d'échanger des ressources avec d'autres joueurs et fournit un espace de stockage supplémetaire",
   Caravel: "Caravelle",
   CaravelBuilder: "Constructeur de caravelles",
   CarFactory: "Usine automobile",
   CarlFriedrichGauss: "Carl Friedrich Gauss",
   CarlFriedrichGaussDesc: "+%{idle} Science produite par les ouvriers inactifs. +%{busy} Science produite par les ouvriers actifs",
   CarlSagan: "Carl Sagan",
   Census: "Rescensement",
   CentrePompidou: "Centre Pompidou",
   CentrePompidouDesc:
      "Une fois construits, tous les bâtiments bénéficient d'un multiplicateur de production de +1 et d'un multiplicateur de stockage de +2. La merveille persistera si la manche en cours atteint l'âge de l'information et que la manche suivante est une civilisation différente. La merveille gagne +1 niveau à la renaissance pour chaque manche qui atteint l'âge de l'information avec une civilisation unique. Chaque niveau apporte un multiplicateur de production de +1 et de stockage de +2. La valeur de cette merveille est exclue de la valeur totale de l'empire et le British Museum ne peut pas se transformer en cette merveille.Une fois construits, tous les bâtiments bénéficient d'un multiplicateur de production de +1 et d'un multiplicateur de stockage de +2. La merveille persistera si la manche en cours atteint l'âge de l'information et que la manche suivante est une civilisation différente. La merveille gagne +1 niveau à la renaissance pour chaque manche qui atteint l'âge de l'information avec une civilisation unique. Chaque niveau apporte un multiplicateur de production de +1 et de stockage de +2. La valeur de cette merveille est exclue de la valeur totale de l'empire et le British Museum ne peut pas se transformer en cette merveille.",
   CentrePompidouWarningHTML: "Le Centre Pompidou disparaîtra si vous renaissez en tant que <b>%{civ}</b>",
   CerneAbbasGiant: "Cerne Abbas Giant",
   CerneAbbasGiantDesc: "Un personnage illustre de l'ère actuelle naît lorsqu'une merveille est construite",
   ChangePlayerHandle: "Modifier",
   ChangePlayerHandleCancel: "Annuler",
   ChangePlayerHandledDesc: "Votre pseudo ne peut contenir que 5 à 16 lettres et chiffres et doit être unique",
   Chariot: "Char",
   ChariotWorkshop: "Fabrique de chars",
   Charlemagne: "Charlemagne",
   CharlesDarwin: "Charles Darwin",
   CharlesDarwinDesc: "+%{value} Science produite par les ouvriers actifs",
   CharlesMartinHall: "Charles Martin Hall",
   CharlesParsons: "Charles Parsons",
   CharlieChaplin: "Charlie Chaplin",
   CharlieChaplinDesc: "+%{value} Bonheur",
   Chat: "Chat",
   ChatChannel: "Canal de discussion",
   ChatChannelLanguage: "Langue",
   ChatHideLatestMessage: "Masquer le contenu du dernier message",
   ChatNoMessage: "Aucun message dans le chat",
   ChatReconnect: "Déconnecté, reconnexion...",
   ChatSend: "Envoyer",
   CheckInAndExit: "Enregistrer et quitter",
   CheckInCloudSave: "Enregistrer la sauvegarde",
   CheckOutCloudSave: "Ouvrir la sauvegarde",
   Cheese: "Fromage",
   CheeseMaker: "Fromagerie",
   Chemistry: "Chimie",
   ChesterWNimitz: "Chester W. Nimitz",
   ChichenItza: "Chichén Itzá",
   ChichenItzaDesc: "Tous les bâtiments adjacents gagnent +1 Multiplicateur de Production, Stockage et Multiplicateur de Capacité des Ouvriers",
   Chinese: "chinois",
   ChoghaZanbil: "Chogha Zanbil",
   ChoghaZanbilDescV2: "Choisissez une tradition d'empire, débloquez davantage de bonus avec chaque choix.",
   ChooseGreatPersonChoicesLeft: "Encore %{count} choix",
   ChristianityLevelX: "Christianisme %{level}",
   Church: "Église",
   CircusMaximus: "Circus Maximus",
   CircusMaximusDescV2: "+5 Bonheur. Toutes les guildes de musiciens, guildes d'écrivains et guildes de peintres gagnent +1 Production et Multiplicateur de Stockage",
   CityState: "Cité-État",
   CityViewMap: "Ville",
   CivGPT: "CivGPT",
   CivIdle: "CivIdle",
   CivIdleInfo: "Fièrement présenté par Fish Pond Studio",
   Civilization: "Civilisation",
   CivilService: "Administration",
   CivOasis: "CivOasis",
   CivTok: "CivTok",
   ClaimedGreatPeople: "Personnage illustre collecté",
   ClaimedGreatPeopleTooltip: "Vous avez %{total} personnages illustres lors de la réincarnation, %{claimed} d'entre elles ont déjà collecté",
   ClassicalAge: "Antiquité",
   ClearAfterUpdate: "Effacer tous les échanges après la mise à jour du marché",
   ClearSelected: "Effacer la sélection",
   ClearSelection: "Supprimer",
   ClearTransportPlanCache: "Effacer le cache du plan de transport",
   Cleopatra: "Cléopâtre",
   CloneFactory: "Usine de Clonage",
   CloneFactoryDesc: " Cloner toutes les ressources",
   CloneFactoryInputDescHTML: "L'usine de clonage ne peut cloner que <b>%{res}</b> directement transportée depuis <b>%{buildings}</b>.",
   CloneLab: "Laboratoire de Clonage",
   CloneLabDesc: "Convertir toutes les ressources en science",
   CloneLabScienceMultiplierHTML: "Les multiplicateurs de production qui <b>s'appliquent uniquement aux bâtiments de production de science</b> (par exemple, les multiplicateurs de production provenant de l'Atomium) <b>ne s'appliquent pas</b> au Laboratoire de Clonage.",
   Cloth: "Tissu",
   CloudComputing: "Informatique dématérialisée",
   CloudSaveRefresh: "Rafraîchir",
   CloudSaveReturnToGame: "Retourner au jeu",
   CNTower: "Tour CN",
   CNTowerDesc:
      "Le fonctionnement de tous les studios de tournage, les stations de radio et les stations de télévision vous coûte 1 bonheur de moins que d'habitude. Tous les bâtiments débloqués appartenant aux ères Guerres mondiales et Guerre froide gagnent +N Production, Capacité des Ouvriers et Multiplicateur de Stockage. N = Différence entre la catégorie et l'ère du bâtiment",
   Coal: "Charbon",
   CoalMine: "Mine de charbon",
   CoalPowerPlant: "Centrale à charbon",
   Coin: "Pièce",
   CoinMint: "Hôtel des monnaies",
   ColdWarAge: "Guerre froide",
   CologneCathedral: "Cathédrale de Cologne",
   CologneCathedralDesc:
      "Lors de sa construction, génère une science unique équivalente au coût de la technologie la plus chère de l'ère actuelle. Tous les bâtiments produisant de la science (à l'exception du Laboratoire de Clonage) reçoivent +1 en Multiplicateur de Production. Cette merveille peut être améliorée, et chaque amélioration supplémentaire offre +1 en Multiplicateur de Production à tous les bâtiments produisant de la science (à l'exception du Laboratoire de Clonage).",
   Colonialism: "Colonialisme",
   Colosseum: "Colisée",
   ColosseumDescV2: "Les Ateliers de Chariots sont exempts du -1 en bonheur. Ils consomment 10 chariots et produisent 10 en bonheur. Chaque ère débloquée donne 2 points de bonheur supplémentaires.",
   ColossusOfRhodes: "Colosse de Rhodes",
   ColossusOfRhodesDesc: "Tous les bâtiments adjacents qui ne produisent pas d'ouvriers gagnent +1 Bonheur",
   Combustion: "Combustion",
   Commerce4UpgradeHTMLV2: "Une fois débloqué, toutes les <b>banques adjacentes</b> obtiennent une amélioration gratuite jusqu'au <b>niveau 30</b>",
   CommerceLevelX: "Commerce %{level}",
   Communism: "Communisme",
   CommunismLevel4DescHTML: "Un personnage illustre de l’<b>Ère Industrielle</b> et un personnage illustre de l’<b>Ère des Guerres Mondiales</b> sont nés.",
   CommunismLevel5DescHTML: "Un personnage illustre de l’<b>Ère de la Guerre Froide</b> est né. En entrant dans une nouvelle ère, obtenez <b>2 personnages illustres supplémentaires</b> de cette ère.",
   CommunismLevelX: "Niveau de Communisme %{level} ",
   Computer: "Ordinateur",
   ComputerFactory: "Usine d'ordinateurs",
   ComputerLab: "Laboratoire informatique",
   Concrete: "Béton",
   ConcretePlant: "Cimenterie",
   Condo: "Copropriété",
   ConfirmDestroyResourceContent: "Vous êtes sur le point de détruire %{amount} %{resource}. Cette action est irréversible",
   ConfirmNo: "Non",
   ConfirmYes: "Oui",
   Confucius: "Confucius",
   ConfuciusDescV2: "+%{value} Science produite par tous les ouvriers si plus de 50% d'entre eux sont actifs et que moins de 50% des ouvriers actifs transportent des ressources",
   ConnectToADevice: "Se connecter à un appareil",
   Conservatism: "Conservatisme",
   ConservatismLevelX: "Niveau de Conservatisme %{level}",
   Constitution: "Constitution",
   Construction: "Construction",
   ConstructionBuilderBaseCapacity: "Capacité de base",
   ConstructionBuilderCapacity: "Capacité de construction",
   ConstructionBuilderMultiplier: "Multiplicateur de Capacité",
   ConstructionBuilderMultiplierFull: "Multiplicateur de Capacité de construction",
   ConstructionCost: "Coût de construction : %{cost}",
   ConstructionDelivered: "Transporté",
   ConstructionPriority: "Prorité de construction",
   ConstructionProgress: "Progression",
   ConstructionResource: "Ressource",
   Consume: "Consommation",
   ConsumeResource: "Consomme : %{resource}",
   ConsumptionMultiplier: "Multiplicateur de Consommation",
   ContentInDevelopment: "Contenu en développement",
   ContentInDevelopmentDesc: "Le contenu de ce jeu est toujours en développement et sera disponible grâce à une nouvelle mise à jour. Restez à l'écoute !",
   Copper: "Cuivre",
   CopperMiningCamp: "Camp minier de cuivre",
   CosimoDeMedici: "Cosme de Médicis",
   Cotton: "Coton",
   CottonMill: "Filature de coton",
   CottonPlantation: "Plantation de coton",
   Counting: "Travail du bois",
   Courthouse: "Tribunal",
   CristoRedentor: "Christ Rédempteur",
   CristoRedentorDesc: "Tous les bâtiments dans un rayon de 2 cases vous coûtent 1 bonheur de moins que d'habitude",
   CrossPlatformAccount: "Compte de plateforme",
   CrossPlatformConnect: "Connecter",
   CrossPlatformSave: "Sauvegarde multiplateforme",
   CrossPlatformSaveLastCheckIn: "Derniere sauvgarde",
   CrossPlatformSaveStatus: "Statut actuel",
   CrossPlatformSaveStatusCheckedIn: "Enregistré",
   CrossPlatformSaveStatusCheckedOut: "Checked Out on %{platform}",
   CrossPlatformSaveStatusCheckedOutTooltip: "Votre sauvegarde multiplateforme a été ouverte sur une autre plateforme. Vous devez l'enregistrer sur cette plateforme avant de pouvoir l'ouvrir sur cette plateforme.",
   Cultivation4UpgradeHTML: "Un personnage illustre de la <b>Renaissance</b> apparait",
   CultivationLevelX: "Élégance %{level}",
   Culture: "Culture",
   Culus: "Cülus: Double the effect of Cappadocia. Mount Ararat's effect becomes based on square root of Effective Great People Level, instead of cubic root",
   CurrentLanguage: "Français",
   CurrentPlatform: "Plate-forme actuelle",
   CursorBigOldFashioned: "3D (Grand)",
   CursorOldFashioned: "3D",
   CursorStyle: "Style de curseur",
   CursorStyleDescHTML: "Modifie le style du curseur. <b>Ce changement nécessite de rédemarrer le jeu pour prendre effet</b>",
   CursorSystem: "Système",
   Cycle: "Cycle",
   CyrusII: "Cyrus II",
   DairyFarm: "Ferme laitière",
   DefaultBuildingLevel: "Niveau des bâtiments par défaut",
   DefaultConstructionPriority: "Priorité de construction par défaut",
   DefaultProductionPriority: "Priorité de production par défaut",
   DefaultStockpileMax: "Stockage maximal par défaut",
   DefaultStockpileSettings: "Capacité d'entrée du stock par défaut",
   DeficitResources: "Ressources déficitaires",
   Democracy: "Démocratie",
   DemolishAllBuilding: "Démolir tous les %{building} dans un rayon de %{tile} tuiles",
   DemolishAllBuildingConfirmContent: "Êtes-vous sûr de vouloir démolir ? %{count} %{name}?",
   DemolishAllBuildingConfirmTitle: "Êtes-vous sûr de vouloir démolir ? %{count} %{name} ?",
   DemolishBuilding: "Démolir le bâtiment",
   DennisRitchie: "Dennis Ritchie",
   Deposit: "Ressource",
   DepositTileCountDesc: "%{count} case(s) de %{deposit} seront révélées ici : %{city}",
   Dido: "Didon",
   Diplomacy: "Diplomatie",
   DistanceInfinity: "Illimitée",
   DistanceInTiles: "Distance (en cases)",
   DolmabahcePalace: "Dolmabahçe Palace",
   Drilling: "Forage",
   DukeOfZhou: "Duc de Zhou",
   DuneOfPilat: "Dune of Pilat",
   DuneOfPilatDesc: "Pour chaque âge, il faut doubler la sagesse de l'âge précédent.",
   DynamicMultiplierTooltip: "Ce multiplicateur est dynamique – il n'affectera pas les travailleurs ni le stockage.",
   Dynamite: "Dynamite",
   DynamiteWorkshop: "Fabrique de dynamite",
   DysonSphere: "Dyson Sphere",
   DysonSphereDesc: "Tous les bâtiments reçoivent un multiplicateur de production de +5. Cette merveille peut être améliorée, et chaque amélioration supplémentaire donne +1 multiplicateur de production à tous les bâtiments. ",
   EasterBunny: "Lapin de Pâques",
   EasterBunnyDesc: "Une fois construit, 10 % des personnages illustre de cette manche seront reportés à la manche suivante et naîtront après la construction du Lapin de Pâques dans la nouvelle manche. Cette merveille ne peut être construite que pendant le mois d'avril",
   EastIndiaCompany: "Compagnie des Indes Orientales",
   EastIndiaCompanyDescV2:
      "Cette merveille accumule la valeur totale des transactions commerciales effectuées par les joueurs. Pour chaque valeur commerciale de 2 000, tous les bâtiments adjacents aux caravanes reçoivent un multiplicateur de production de +0,5 pendant 1 cycle. Cette merveille peut être améliorée et chaque amélioration fournit un multiplicateur de production supplémentaire de +0,5. Une transaction commerciale est comptabilisée lorsque vous répondez à la demande de commerce d'un autre joueur ou lorsque votre propre demande de commerce est satisfaite. Les boosts multiples s'empilent en prolongeant la duréeCette merveille accumule la valeur totale des transactions commerciales effectuées par les joueurs. Pour chaque valeur commerciale de 2 000, tous les bâtiments adjacents aux caravanes reçoivent un multiplicateur de production de +0,5 pendant 1 cycle. Cette merveille peut être améliorée et chaque amélioration fournit un multiplicateur de production supplémentaire de +0,5. Une transaction commerciale est comptabilisée lorsque vous répondez à la demande de commerce d'un autre joueur ou lorsque votre propre demande de commerce est satisfaite. Les boosts multiples s'empilent en prolongeant la durée",
   Education: "Éducation",
   EffectiveGreatPeopleLevel: "Niveau effectif des personnages illustres",
   EffectiveGreatPeopleLevelDesc: "Le niveau effectif des personnages illustres est la somme de tous les niveaux de personnages illustres permanents et des niveaux de sagesse des âges. Il mesure l'augmentation des effets fournis par les personnages illustres et la sagesse des âges",
   Egyptian: "Égyptien",
   EiffelTower: "Tour Eiffel",
   EiffelTowerDesc: "Toutes les fonderies adjacentes gagnent +N Production, Stockage et Multiplicateur d'Ouvriers. N = Nombre de fonderies adjacentes",
   Elbphilharmonie: "Elbphilharmonie",
   ElbphilharmonieDesc: "Tous les bâtiments dans un rayon de 3 tuiles reçoivent +1 Multiplicateur de Production pour chaque bâtiment fonctionnel adjacent ayant un niveau différent.",
   Electricity: "Électricité",
   Electrification: "Électrification",
   ElectrificationPowerRequired: "Énergie requise",
   ElectrificationStatusActive: "Actif",
   ElectrificationStatusDesc: "Les bâtiments qui requièrent de l'énergie comme ceux qui n'en demandent pas peuvent être électrifiés. Cependant, les bâtiments nécessitant de l'énergie offrent une efficacité d'électrification plus élevée",
   ElectrificationStatusNoPowerV2: "Énergie insuffisante",
   ElectrificationStatusNotActive: "Inactif",
   ElectrificationStatusV2: "Statut de l'électrification",
   ElectrificationUpgrade: "Débloque l'éléctrification. Permet aux bâtiments de consommer de l'énergie pour booster leur production",
   Electrolysis: "Électrolyse",
   ElvisPresley: "Elvis Presley",
   ElyseePalace: "Élysée Palace",
   EmailDeveloper: "Email du développeur",
   Embassy: "Ambassade",
   EmperorWuOfHan: "Empereur Wu de Han",
   EmpireValue: "Valeur de l'empire",
   EmpireValueByHour: "Valeur de l'Empire par Heure",
   EmpireValueFromBuilding: "Valeur de l'Empire provenant des Bâtiments",
   EmpireValueFromBuildingsStat: "Provenant des Bâtiments",
   EmpireValueFromResources: "Provenant des Ressources",
   EmpireValueFromResourcesStat: "Provenant des Ressources",
   EmpireValueIncrease: "Augmentation de la Valeur de l'Empire",
   EmptyTilePageBuildLastBuilding: "Construire le dernier bâtiment",
   EndConstruction: "Abandonner la construction",
   EndConstructionDescHTML: "Lorsque vous abandonnez la construction d'un bâtiment, toutes les ressources déjà utilisées <b>ne seront pas restituées</b>",
   Engine: "Moteur",
   Engineering: "Ingénierie",
   English: "anglais",
   Enlightenment: "Pensées des Lumière",
   Enrichment: "Enrichissement",
   EnricoFermi: "Enrico Fermi",
   EstimatedTimeLeft: "Temps restant estimé",
   EuphratesRiver: "Fleuve Euphrate",
   EuphratesRiverDesc:
      "Chaque 10 % de travailleurs occupés dans la production (et non dans le transport) procure +1 multiplicateur de production à tous les bâtiments qui ne produisent pas de travailleurs (max = nombre d'ères débloquées / 2). Lorsque les Jardins Suspendus sont construits à côté, les Jardins Suspendus obtiennent +1 effet pour chaque ère après leur déblocage. Lorsqu'ils sont découverts, de l'eau apparaît sur toutes les tuiles adjacentes qui n'ont pas de dépôts.",
   ExpansionLevelX: "Expansion %{level}",
   Exploration: "Exploration",
   Explorer: "Explorateur",
   ExplorerRangeUpgradeDesc: "Augmenter la portée de l'explorateur à %{range} ",
   ExploreThisTile: "Envoyer un explorateur",
   ExploreThisTileHTML: "Un explorateur peut découvrir <b>cette case et celles adjacentes</b>. Il est possible d'en recruter dans un(e) %{name}. Il vous reste encore %{count} explorateur(s).",
   ExtraGreatPeople: "%{count} personnages illustres bonus",
   ExtraGreatPeopleAtReborn: "Personnages illustres bonus lors de la réincarnation",
   ExtraTileInfoType: "Information supplémentaire sur la case",
   ExtraTileInfoTypeDesc: "Sélectionnez l'information qui figurera sous chaque case",
   ExtraTileInfoTypeEmpireValue: "Valeur dans l'empire",
   ExtraTileInfoTypeNone: "Aucune",
   ExtraTileInfoTypeStoragePercentage: "Pourcentage de stockage",
   Faith: "Foi",
   Farming: "Agriculture",
   FavoriteBuildingAdd: "Ajouter aux favoris",
   FavoriteBuildingEmptyToast: "Vous n'avez aucun bâtiment favori",
   FavoriteBuildingRemove: "Retirer des favoris",
   FeatureRequireQuaestorOrAbove: "Cette fonctionnalité nécessite le rang de Questeur ou supérieur",
   Festival: "Festival",
   FestivalCycle: "Cycle des Festivals",
   FestivalTechTooltipV2: "Le bonheur positif (max. 50) est converti en points de festival. Pour chaque %{point} points de festival, votre empire entre dans un cycle de festival, accordant un boost significatif spécifique à la carte. Le festival sur cette carte est %{desc}",
   FestivalTechV2: "Débloquer le festival - le bonheur positif (max. 50) est converti en points de festival. Pour chaque %{point} points de festival, votre empire entre dans un cycle de festival, accordant un boost significatif spécifique à la carte",
   Feudalism: "Féodalité",
   Fibonacci: "Fibonacci",
   FibonacciDescV2: "+%{idle} Science produite par les ouvriers inactifs. +%{busy} Science produite par les ouvriers actifs. Le coût de l'amélioration permanente de Fibonacci est calculé en fonction de la suite de Fibonacci",
   FighterJet: "Avion de chasse",
   FighterJetPlant: "Usine d'avions de chasse",
   FilterByAge: " Filtrer par Ère",
   FinancialArbitrage: "Arbitrage Financier",
   FinancialLeverage: "Levier financier",
   Fire: "Feu",
   Firearm: "Arme à feu",
   FirstTimeGuideNext: "Suivant",
   FirstTimeTutorialWelcome: "Bienvenue dans CivIdle",
   FirstTimeTutorialWelcome1HTML:
      "Bienvenue dans CivIdle. Dans ce jeu, vous dirigerez votre propre empire : <b>gérez les productions, débloquez des technologies, échangez des ressources avec d'autres joueurs, créez des personnages illustres et construisez des merveilles du monde</b>.<br><br>Déplacez votre souris pour vous déplacer. Utilisez la molette de la souris pour zoomer avant ou arrière. Cliquez sur une tuile vide pour construire de nouveaux bâtiments, cliquez sur un bâtiment pour l'inspecter.<br><br>Certaines structures comme la Carrière de Pierre et le Camp de Bois doivent être construites sur une tuile de ressource. Je recommande de placer une Cabane, qui fournit des travailleurs, à côté du brouillard - le bâtiment prendra un certain temps à être construit. Une fois terminé, il révélera le brouillard aux alentours.",
   FirstTimeTutorialWelcome2HTML:
      "Les bâtiments peuvent être améliorés - cela coûte des ressources et prend du temps. Lorsqu'un bâtiment est en cours d'amélioration, <b>il ne produira plus</b>. Cela inclut les bâtiments qui fournissent des travailleurs, <b>alors ne mettez jamais tous vos bâtiments à jour en même temps !</b><br><br>Au fur et à mesure que votre empire se développe, vous obtiendrez plus de science et débloquerez de nouvelles technologies. Je vous en dirai plus à ce sujet lorsque nous y arriverons, mais vous pouvez aller dans Affichage -> Recherche pour jeter un coup d'œil rapide.",
   FirstTimeTutorialWelcome3HTML:
      "Maintenant que vous connaissez les bases du jeu, vous pouvez commencer à construire votre empire. Mais avant de vous laisser partir, vous devriez <b>choisir un pseudo</b> et dire bonjour dans le chat du jeu. Nous avons une communauté incroyablement utile : si vous vous perdez, n'ayez pas peur de demander !",
   Fish: "Poisson",
   FishPond: "Vivier",
   FlorenceNightingale: "Florence Nightingale",
   FlorenceNightingaleDesc: "+%{value} Bonheur",
   Flour: "Farine",
   FlourMill: "Moulin à farine",
   FontSizeScale: "Taille de la police",
   FontSizeScaleDescHTML: "Modifie la taille de la police de l'interface du jeu. <b>Une police supérieure à 1x pourrait endommager l'interface</b>",
   ForbiddenCity: "Cité interdite",
   ForbiddenCityDesc: "Toutes les papeteries, guildes d'écrivains et imprimeries gagnent +1 Multiplicateur de Production, Multiplicateur de Capacité des Ouvriers et Multiplicateur de Stockage",
   Forex: "Devise",
   ForexMarket: "Marché des changes",
   FrankLloydWright: "Frank Lloyd Wright",
   FrankLloydWrightDesc: "+%{value} Multiplicateur de Capacité des Ouvriers",
   FrankWhittle: "Frank Whittle",
   FreeThisWeek: "Gratuit cette semaine",
   FreeThisWeekDescHTMLV2: "<b>Chaque semaine</b>, l'une des civilisations premium est gratuite à jouer. La civilisation gratuite de cette semaine est <b>%{city}</b>",
   French: "Français",
   Frigate: "Frégate",
   FrigateBuilder: "Constructeur de frégates",
   Furniture: "Meuble",
   FurnitureWorkshop: "Fabrique de meubles",
   Future: "Future",
   GabrielGarciaMarquez: "Gabriel García Márquez",
   GabrielGarciaMarquezDesc: "+%{value} Bonheur",
   GalileoGalilei: "Galilée",
   GalileoGalileiDesc: "+%{value} Science produite par les ouvriers inactifs",
   Galleon: "Galion",
   GalleonBuilder: "Constructeur de galions",
   Gameplay: "Gameplay",
   Garment: "Vêtement",
   GarmentWorkshop: "Fabrique de vêtements",
   GasPipeline: "Gazoduc",
   GasPowerPlant: "Centrale à gaz",
   GatlingGun: "Mitraillette",
   GatlingGunFactory: "Usine de mitraillettes",
   Genetics: "Génétique",
   Geography: "Géographie",
   GeorgeCMarshall: "George C. Marshall",
   GeorgeWashington: "George Washington",
   GeorgiusAgricola: "Georgius Agricola",
   German: "Allemand",
   Glass: "Verre",
   Glassworks: "Verrerie",
   GlobalBuildingDefault: "Paramètres par défaut des bâtiments",
   Globalization: "Mondialisation",
   GoBack: "Retour",
   Gold: "Or",
   GoldenGateBridge: "Pont du Golden Gate",
   GoldenGateBridgeDesc: "Toutes les centrales gagnent +1 Multiplicateur de Production. Fournit de l'énergie à toutes les cases dans un rayon de 2 cases",
   GoldenPavilion: "Golden Pavilion",
   GoldenPavilionDesc: "Tous les bâtiments dans un rayon de 3 tuiles reçoivent +1 multiplicateur de production pour chaque bâtiment adjacent qui produit l'une de ses ressources consommées (à l'exception du Laboratoire de Clonage et de l'Usine de Clonage, et le bâtiment ne peut pas être éteint).",
   GoldMiningCamp: "Camp minier d'or",
   GordonMoore: "Gordon Moore",
   GrandBazaar: "Grand Bazar",
   GrandBazaarDesc: "Permet de gérer tous vos marchés. Les caravansérails adjacents gagnent +5 Production et Multiplicateur de Stockage. Les marchés adjacentes obtiennent différents échanges",
   GrandBazaarFilters: "Filtres",
   GrandBazaarFilterWarningHTML: "Vous devez sélectionner un filtre pour afficher les échanges",
   GrandBazaarFilterYouGet: "Vous obtenez :",
   GrandBazaarFilterYouPay: "Vous payez :",
   GrandBazaarSeach: "Recherche",
   GrandBazaarSearchGet: "Obtenir",
   GrandBazaarSearchPay: "Payer",
   GrandBazaarTabActive: "Actif",
   GrandBazaarTabTrades: "Échanges",
   GrandCanyon: "Grand Canyon",
   GrandCanyonDesc: "Les bâtiments débloqués dans l'ère actuelle reçoivent +2 en Multiplicateur de Production. Double l'effet de J.P. Morgan",
   GraphicsDriver: "Pilote graphique: %{driver}",
   GreatDagonPagoda: "Pagode Shwedagon",
   GreatDagonPagodaDescV2: " Toutes les pagodes sont exemptées du -1 en bonheur. Générez de la science en fonction de la production de foi de toutes les pagodes.",
   GreatMosqueOfSamarra: "Grande Mosquée de Samarra",
   GreatMosqueOfSamarraDescV2: "+1 champ de vision des bâtiments. Révèle 5 cases ressources inexplorées aléatoires et construit un bâtiment d'extraction de ressources de niveau 10 sur chacune d'entre elles",
   GreatPeople: "Personnage illustre",
   GreatPeopleEffect: "Effet",
   GreatPeopleFilter: "Tapez le nom ou l'ère pour filtrer les personnages illustres ",
   GreatPeopleName: "Nom",
   GreatPeoplePermanentColumn: "Permanent",
   GreatPeoplePermanentShort: "Permanent",
   GreatPeoplePickPerRoll: "Choix de personnages illustres par tirage ",
   GreatPeopleThisRun: "Personnages illustres dans cette partie",
   GreatPeopleThisRunColumn: "Cette partie",
   GreatPeopleThisRunShort: "Cette partie",
   GreatPersonLevelRequired: "Niveau de personnages illustres permanents requis",
   GreatPersonLevelRequiredDescV2: "La civilisation %{city} nécessite %{required} niveaux de personnages illustres permanents. Vous avez actuellement %{current}",
   GreatPersonPromotionPromote: "Promouvoir",
   GreatPersonThisRunEffectiveLevel: "Vous avez actuellement %{count} %{person} dans cette partie. %{person} supplémentaire produira seulement 1/%{effect} de l'effet",
   GreatPersonWildCardBirth: "Naissance ",
   GreatSphinx: "Sphinx",
   GreatSphinxDesc: "Tous les bâtiments de catégorie II ou plus dans un rayon de 2 cases gagnent +N Consommation et Multiplicateur de Production. N = nombre de bâtiments adjacents du même type",
   GreatWall: "Grande Muraille",
   GreatWallDesc: "Tous les bâtiments dans un rayon de 1 case gagnent +N Production, Capacité des Ouvriers et Multiplicateur de Stockage. N = nombre d'ères entre l'ère actuelle et celle où le bâtiment est débloquable. Lorsque cette Merveille est bâtie à côté de la Cité interdite, ce rayon s'étend à 2 cases",
   GreedyTransport: "Construction/Upgrade Greedy Transport",
   GreedyTransportDescHTML:
      "Cela permettra aux bâtiments de continuer à transporter des ressources même s'ils ont suffisamment de ressources pour l'amélioration en cours, ce qui peut rendre l'amélioration de plusieurs niveaux <b>plus rapide</b>, mais entraînera le transport de <b>plus de ressources que nécessaire</b>. ",
   Greek: "Grecque",
   GrottaAzzurra: "Grotte bleue",
   GrottaAzzurraDescV2: "Lorsqu'il est découvert, tous vos bâtiments de niveau I reçoivent +5 niveaux ainsi que +1 en Production, Capacité de Travailleurs et Multiplicateur de Stockage.",
   Gunpowder: "Poudre à canon",
   GunpowderMill: "Poudrerie",
   GuyFawkesNightV2: "Guy Fawkes Night: La Compagnie anglaise des Indes orientales octroie un multiplicateur de production doublé aux bâtiments adjacents aux caravansérails. Tower Bridge génère des Personnages Illustre 20% plus rapidement",
   HagiaSophia: "Sainte-Sophie",
   HagiaSophiaDescV2: "+5 Bonheur. Les bâtiments avec 0 % de capacité de production sont exemptés du -1 en bonheur. Pendant le démarrage du jeu, fournissez du bonheur supplémentaire pour éviter l'arrêt de la production. ",
   HallOfFame: "Temple de la renommée",
   HallOfSupremeHarmony: "Pavillon de l'Harmonie suprême",
   Hammurabi: "Hammurabi",
   HangingGarden: "Jardins suspendus",
   HangingGardenDesc: "+1 Multiplicateur de Capacité de construction. Les aqueducs adjacents gagnent +1 Production, Stockage et Multiplicateur de Capacité des Ouvriers",
   Happiness: "Bonheur",
   HappinessFromBuilding: "Produit par les bâtiments (Merveilles exclues)",
   HappinessFromBuildingTypes: "Produit par les bâtiments de stockage remplis",
   HappinessFromHighestTierBuilding: "Produit par le bâtiment ouvrier de la catégorie la plus élévée",
   HappinessFromUnlockedAge: "Produit par l'ère débloqué",
   HappinessFromUnlockedTech: "Produit par les technologies débloquées",
   HappinessFromWonders: "Produit par les Merveilles (Merveilles naturelles incluses)",
   HappinessUncapped: "Bonheur (sans limite)",
   HarryMarkowitz: "Harry Markowitz",
   HarunAlRashid: "Hâroun ar-Rachîd",
   Hatshepsut: "Hatchepsout",
   HatshepsutTemple: "Temple d'Hatchepsout",
   HatshepsutTempleDesc: "Révèle toutes les cases d'eau sur la carte. Les fermes à blé gagnent +1 Multiplicateur de Production pour chaque case d'eau adjacente",
   Headquarter: "Quartier générale",
   HedgeFund: "Fonds spéculatifs",
   HelpMenu: "Aide",
   HenryFord: "Henry Ford",
   Herding: "Élevage",
   Herodotus: "Hérodote",
   HighlightBuilding: "Highlight %{building}",
   HimejiCastle: "Château de Himeji",
   HimejiCastleDesc: "Tous les constructeurs de caravelles, constructeurs de galions et constructeurs de frégates gagnent +1 Multiplicateur de Production, Multiplicateur de Capacité des Ouvriers et Multiplicateur de Stockage",
   Hollywood: "Hollywood",
   HollywoodDesc: "+5 Bonheur. +1 Bonheur pour chaque bâtiment bien approvisionné qui consomme ou produit de la culture dans un rayon de 2 tuiles.",
   HolyEmpire: "Saint-Empire",
   Homer: "Homer",
   Honor4UpgradeHTML: "Double l'effet de <b>Zheng He</b> (personnage illustre)",
   HonorLevelX: "Honneur %{level}",
   Horse: "Cheval",
   HorsebackRiding: "Équitation",
   House: "Maison",
   Housing: "Habitation",
   Hut: "Hutte",
   HydroDam: "Barrage hydroélectrique",
   Hydroelectricity: "Hydroélectricité",
   HymanGRickover: "Hyman G. Rickover",
   IdeologyDescHTML: "Choisissez parmi <b>Liberalisme, Conservatisme, Socialisme ou Communisme</b> comme idéologie de votre empire. Vous <b>ne pouvez pas changer d'idéologie</b> une fois qu'elle est choisie. Vous pouvez débloquer davantage de bonus au sein de chaque idéologie.",
   IMPei: "I. M. Pei",
   IMPeiDesc: "+%{value} Multiplicateur de Capacité de Bâtisseur",
   Imperialism: "Impérialisme",
   ImperialPalace: "Palais Impérial",
   IndustrialAge: "Révolution industrielle",
   InformationAge: "Ère numérique",
   InputResourceForCloning: "Ressource d'entrée pour le clonage",
   InternationalSpaceStation: "Station Spatiale Internationale",
   InternationalSpaceStationDesc: "Tous les bâtiments reçoivent +5 Multiplicateur de Stockage. Cette merveille peut être améliorée, et chaque amélioration supplémentaire offre +1 Multiplicateur de Stockage à tous les bâtiments.",
   Internet: "Internet",
   InternetServiceProvider: "Fournisseur de services Internet",
   InverseSelection: "Inverser",
   Iron: "Fer",
   IronAge: "Âge de fer",
   Ironclad: "Cuirassé",
   IroncladBuilder: "Constructeur de cuirassés",
   IronForge: "Forge de fer",
   IronMiningCamp: "Camp minier de fer",
   IronTech: "Fer",
   IsaacNewton: "Isaac Newton",
   IsaacNewtonDescV2: "+%{value} Science de tous les travailleurs si plus de 50 % des travailleurs sont occupés et que moins de 50 % des travailleurs occupés travaillent dans le transport.",
   IsambardKingdomBrunel: "Isambard Kingdom Brunel",
   IsidoreOfMiletus: "Isidore de Milet",
   IsidoreOfMiletusDesc: "+%{value} Multiplicateur de Capacité de construction",
   Islam5UpgradeHTML: "Lorsqu'il est débloqué, génère une science unique équivalente au coût de la technologie <b>Industrielle</b> la plus chère.",
   IslamLevelX: "Islam %{level}",
   ItsukushimaShrine: "Sanctuaire d'Itsukushima",
   ItsukushimaShrineDescV2: "Lorsque toutes les technologies d'une ère sont débloquées, génère une science unique équivalente au coût de la technologie la moins chère dans l'ère suivante.",
   JamesWatson: "James Watson",
   JamesWatsonDesc: "+%{value} Science produite par les ouvriers actifs",
   JamesWatt: "James Watt",
   Japanese: "Japonais",
   JetPropulsion: "Propulsion à réaction",
   JohannesGutenberg: "Johannes Gutenberg",
   JohannesKepler: "Johannes Kepler",
   JohnCarmack: "John Carmack",
   JohnDRockefeller: "John D. Rockefeller",
   JohnMcCarthy: "John McCarthy",
   JohnVonNeumann: "John von Neumann",
   JohnVonNeumannDesc: "+%{value} Science provenant des travailleurs occupés",
   JoinDiscord: "Rejoindre notre Discord",
   JosephPulitzer: "Joseph Pulitzer",
   Journalism: "Journalisme",
   JPMorgan: "J.P. Morgan",
   JRobertOppenheimer: "J. Robert Oppenheimer",
   JuliusCaesar: "Julius Caesar",
   Justinian: "Justinien",
   Kanagawa: "Kanagawa",
   KanagawaDesc: "Tous les personnages illustres de l'ère actuelle reçoivent un niveau supplémentaire pour cette partie (à l'exception de Zenobia).",
   KarlMarx: "Karl Marx",
   Knight: "Chevalier",
   KnightCamp: "Campement de chevalier",
   Koti: "Koti",
   KotiInStorage: "Koti In Storage",
   KotiProduction: "Koti Production",
   LandTrade: "Échange parcellaire",
   Language: "Langue",
   Lapland: "Lapland",
   LaplandDesc: "Lorsqu'elle est découverte, révèle toute la carte. Tous les bâtiments dans un rayon de 2 tuiles reçoivent +5 en Multiplicateur de Production. Cette merveille naturelle ne peut être découverte qu'en décembre",
   LargeHadronCollider: "Large Hadron Collider",
   LargeHadronColliderDescV2: "Tous les personnages illustres de l'Âge de l'Information reçoivent +2 niveaux pour cette partie. Cette merveille peut être améliorée, et chaque amélioration supplémentaire offre +1 niveau à tous les personnages illustres de l'Âge de l'Information pour cette partie.",
   Law: "Loi",
   Lens: "Lentille",
   LensWorkshop: "Fabrique de lentilles",
   LeonardoDaVinci: "Léonard de Vinci",
   Level: "Niveau",
   LevelX: "Niveau %{level}",
   Liberalism: "Liberalism",
   LiberalismLevel3DescHTML: "Transport gratuit <b>depuis</b> et <b>vers</b> les entrepôts",
   LiberalismLevel5DescHTML: "<b>Double</b> l'effet d'électrification",
   LiberalismLevelX: "Niveau de Libéralisme %{level}",
   Library: "Bibliothèque",
   LighthouseOfAlexandria: "Phare d'Alexandrie",
   LighthouseOfAlexandriaDesc: "Tous les bâtiments adjacents gagnent +5 Multiplicateur de Stockage",
   LinusPauling: "Linus Pauling",
   LinusPaulingDesc: "+%{value} Science produite par les ouvriers inactifs",
   Literature: "Littérature",
   LiveData: "Live Value",
   LocomotiveFactory: "Usine de locomotives",
   Logging: "Sylviculture",
   LoggingCamp: "Camp de bûcherons",
   LouisSullivan: "Louis Sullivan",
   LouisSullivanDesc: "+%{value} Multiplicateur de Capacité de Bâtisseur",
   Louvre: "Louvre",
   LouvreDesc: "Pour chaque 10 Extra Personnages illustre à la Renaissance, un personnage illustre de tous les âges débloqués naît.",
   Lumber: "Bois d'œuvre",
   LumberMill: "Scierie",
   LunarNewYear: "Nouvel An Lunaire : La Grande Muraille double le bonus aux bâtiments. La Tour de Porcelaine donne +1 niveau à tous les personnages illustres de cette partie.",
   LuxorTemple: "Temple d'Amon",
   LuxorTempleDescV2: "+1 Science provenant des travailleurs occupés. Choisissez une religion d'empire, débloquez davantage de bonus avec chaque choix.",
   Machinery: "Machinerie",
   Magazine: "Revue",
   MagazinePublisher: "Éditeur de revues",
   Maglev: "Maglev",
   MaglevFactory: "Usine Maglev",
   MahatmaGandhi: "Mahatma Gandhi",
   ManageAgeWisdom: "Gérer la Sagesse des Âges",
   ManagedImport: "Importation gérée",
   ManagedImportDescV2: "Ce bâtiment importera automatiquement les ressources produites dans un rayon de %{range} tuiles. Les transports de ressources pour ce bâtiment ne peuvent pas être modifiés manuellement. La distance maximale de transport sera ignorée.",
   ManageGreatPeople: "Gérer les personnages illustres",
   ManagePermanentGreatPeople: "Gérer les personnages illustres permanents",
   ManageSave: "Gérer les sauvgardes",
   ManageWonders: "Gérer les Merveilles",
   Manhattan: "Manhattan",
   ManhattanProject: "Projet Manhattan",
   ManhattanProjectDesc:
      "Toutes les mines d'uranium reçoivent +2 en Production, Capacité de Travailleurs et Multiplicateur de Stockage. Les usines d'enrichissement de l'uranium et les installations atomiques reçoivent +1 en Multiplicateur de Production pour chaque mine d'uranium adjacente construite sur un dépôt d'uranium. ",
   Marble: "Marbre",
   Marbleworks: "Marbrerie",
   MarcoPolo: "Marco Polo",
   MarieCurie: "Marie Curie",
   MarinaBaySands: "Marina Bay Sands",
   MarinaBaySandsDesc: "Tous les bâtiments reçoivent +5 Multiplicateur de Capacité de Travailleurs. Cette merveille peut être améliorée, et chaque amélioration supplémentaire offre +1 Multiplicateur de Capacité de Travailleurs à tous les bâtiments.",
   Market: "Marché",
   MarketDesc: "Permet d'échanger une ressource contre une autre. Les ressources disponbiles sont mises à jour toutes les heures",
   MarketRefreshMessage: "Les échanges dans %{count} marché(s) ont été mis à jour",
   MarketSell: "Vendre",
   MarketSettings: "Paramètres du marché",
   MarketValueDesc: "%{value} par rapport au prix moyen",
   MarketYouGet: "Vous obtenez",
   MarketYouPay: "Vous payez",
   MartinLuther: "Martin Luther",
   MaryamMirzakhani: "Maryam Mirzakhani",
   MaryamMirzakhaniDesc: "+%{value} Science provenant des travailleurs inactifs",
   Masonry: "Maçonnerie",
   MatrioshkaBrain: "Matrioshka Brain",
   MatrioshkaBrainDescV2:
      "Autoriser la science à être comptabilisée lors du calcul de la valeur de l'empire (5 Science = 1 Valeur de l'Empire). +5 Science par travailleur occupé et inactif. Cette merveille peut être améliorée, et chaque amélioration supplémentaire offre +1 Science par travailleur occupé et inactif ainsi que +1 Multiplicateur de Production pour les bâtiments produisant de la Science.",
   MausoleumAtHalicarnassus: "Mausolée d'Halicarnasse",
   MausoleumAtHalicarnassusDescV2: "Transports from or to buildings within 2 tile range do not cost workers",
   MaxExplorers: "Nombre max. d'explorateurs",
   MaxTransportDistance: "Distance de transport maximale",
   Meat: "Viande",
   Metallurgy: "Métallurgie",
   Michelangelo: "Michelangelo",
   MiddleAge: "Moyen Âge",
   MilitaryTactics: "Tactiques Militaires",
   Milk: "Lait",
   Moai: "Moai",
   MoaiDesc: "Moaï",
   MobileOverride: "Mobile Override",
   MogaoCaves: "Grottes de Mogao",
   MogaoCavesDescV3: "+1 bonheur pour chaque 10 % de travailleurs occupés. Tous les bâtiments adjacents produisant de la foi sont exemptés du -1 en bonheur.",
   MonetarySystem: "Système monétaire",
   MontSaintMichel: "Mont Saint-Michel",
   MontSaintMichelDesc: "Génère de la culture à partir des travailleurs inactifs. Fournit un multiplicateur de stockage de +1 à tous les bâtiments dans un rayon de 2 tuiles. Cette merveille peut être améliorée en utilisant la culture générée et chaque niveau fournit un multiplicateur de stockage supplémentaire de +1.",
   Mosque: "Mosquée",
   MotionPicture: "Cinéma",
   MountArarat: "Mount Ararat",
   MountAraratDesc: "All buildings within 2 tile range get +X Production, Worker Capacity and Storage Multiplier. X = cubic root of Effective Great People Level",
   MountFuji: "Mount Fuji",
   MountFujiDescV2: "Lorsque Petra est construite à côté, Petra reçoit +8h de stockage de Distorsion. Lorsque le jeu est en cours, génère 20 Distorsions par minute dans Petra (non accéléré par Petra elle-même, ne génère pas lorsque le jeu est hors ligne).",
   MountSinai: "Mont Sinaï",
   MountSinaiDesc: "Une fois découvert, un personnage illustre de l'ère actuel apparait. Tous les bâtiments qui produisent de la foi gagnent +5 Multiplicateur de Stockage",
   MountTai: "Mont Tai",
   MountTaiDesc: "Tous les bâtiments qui produisent de la science gagnent +1 Multiplicateur de Production. Double l'effet de Confucious (personnage illustre). Une fois découvert, octroie une quantité de science unique équivalente au coût de la technologie débloquée la plus chère",
   MoveBuilding: "Déplacer le bâtiment",
   MoveBuildingFail: "La case sélectionnée n'est pas valide",
   MoveBuildingNoTeleport: "Vous n'avez pas assez de téléporteurs",
   MoveBuildingSelectTile: "Sélectionnez une case...",
   MoveBuildingSelectTileToastHTML: "Sélectionnez <b>une tuile explorée vide</b> sur la carte comme cible.",
   Movie: "Film",
   MovieStudio: "Studio de tournage",
   Museum: "Musée",
   Music: "Musique",
   MusiciansGuild: "Guilde des musiciens",
   MutualAssuredDestruction: "Destruction mutuelle garantie",
   MutualFund: "Fonds Commun",
   Name: "Nom",
   Nanotechnology: "Nanotechnologie",
   NapoleonBonaparte: "Napoléon Bonaparte",
   NaturalGas: "Gaz naturel",
   NaturalGasWell: "Puits de gaz naturel",
   NaturalWonderName: "Merveille naturelle : %{name}",
   NaturalWonders: "Merveilles naturelles",
   Navigation: "Navigation",
   NebuchadnezzarII: "Nabuchodonosor II",
   Neuschwanstein: "Neuschwanstein",
   NeuschwansteinDesc: "+10 Multiplicateur de Capacité de construction lorsque vous bâtissez des Merveilles",
   Newspaper: "Journal",
   NextExplorersIn: "Prochains explorateurs dans",
   NextMarketUpdateIn: "Prochaine mise à jour du marché dans",
   NiagaraFalls: "Chutes du Niagara",
   NiagaraFallsDescV2: "Tous les entrepôts, les marchés et les caravansérails gagnent +N Multiplicateur de Stockage. N = nombre d'ères débloquées. Albert Einstein confère +1 Multiplicateur de Production aux fonds de recherche (qui ne sont pas affectés par d'autres boosts tels que Broadway)",
   NielsBohr: "Niels Bohr",
   NielsBohrDescV2: "+%{value} Science produite par tous les ouvriers si plus de 50% d'entre eux sont actifs et que moins de 50% des ouvriers actifs transportent des ressources",
   NileRiver: "Nil",
   NileRiverDesc: "Double l'effet de Hatchepsout. Toutes les fermes à blé gagnent +1 Production et Multiplicateur de Stockage. Toutes les fermes à blé adjacentes gagnent +5 Production et Multiplicateur de Stockage",
   NoPowerRequired: "Ce bâtiment ne nécessite pas d'énergie",
   NothingHere: "Il n'y a rien ici",
   NotProducingBuildings: "Bâtiments inactifs",
   NuclearFission: "Fission nucléaire",
   NuclearFuelRod: "Combustible nucléaire",
   NuclearMissile: "Missile nucléaire",
   NuclearMissileSilo: "Silo à missile nucléaire",
   NuclearPowerPlant: "Centrale nucléaire",
   NuclearReactor: "Réacteur nucléaire",
   NuclearSubmarine: "Sous-marin nucléaire",
   NuclearSubmarineYard: "Chantier de Sous-marins Nucléaires",
   OdaNobunaga: "Oda Nobunaga",
   OfflineErrorMessage: "Vous êtes actuellement hors ligne, cette opération nécessite un accès à Internet",
   OfflineProduction: "Production hors ligne",
   OfflineProductionTime: "Offline Production Time",
   OfflineProductionTimeDescHTML: "Pour les <b>premières %{time} heures d'inactivité</b>, vous pouvez choisir soit la production hors ligne, soit la distorsion du temps - vous pouvez définir la répartition ici. Le <b>reste du temps d'inactivité</b> ne peut être converti qu'en distorsion du temps",
   OfflineTime: "Temps hors ligne",
   Oil: "Pétrole",
   OilPress: "Presse à huile",
   OilRefinery: "Raffinerie de pétrole",
   OilWell: "Puits de pétrole",
   Ok: "OK",
   Oktoberfest: "Oktoberfest : Double l'effet de Zugspitze",
   Olive: "Olive",
   OlivePlantation: "Oliveraie",
   Olympics: "Jeux olympiques",
   OnlyAvailableWhenPlaying: "Disponible uniquement lorsque vous jouez avec %{city}",
   OpenLogFolder: "Ouvrir le dossier des journaux",
   OpenSaveBackupFolder: "Ouvrier le dossier de récupération",
   OpenSaveFolder: "Ouvrir le dossier de sauvegarde",
   Opera: "Opéra",
   OperationNotAllowedError: "Opération impossible",
   Opet: "Opet : Le Grand Sphinx n'augmente plus le Multiplicateur de Consommation",
   OpticalFiber: "Fibre optique",
   OpticalFiberPlant: "Usine de fibre optique",
   Optics: "Optique",
   OptionsMenu: "Options",
   OptionsUseModernUIV2: "Utiliser une police lissée",
   OsakaCastle: "Osaka Castle",
   OsakaCastleDesc: "Fournit de l'énergie à toutes les tuiles dans un rayon de 2 tuiles. Permet l'électrification des bâtiments produisant de la science (y compris le Laboratoire de Clonage).",
   OtherPlatform: "Autre plate-forme",
   Ottoman: "Ottoman",
   OttoVonBismarck: "Otto von Bismarck",
   OxfordUniversity: "Université d'Oxford",
   OxfordUniversityDescV3: "+10% de production de science pour les bâtiments qui en produisent. Une fois sa construction achevée, octroie une quantité de science unique équivalente au coût de la technologie débloquée la plus chère",
   PabloPicasso: "Pablo Picasso",
   Pagoda: "Pagoda",
   PaintersGuild: "Guilde des peintres",
   Painting: "Peinture",
   PalmJumeirah: "Palm Jumeirah",
   PalmJumeirahDesc: "+10 Capacité de Bâtisseur. Cette merveille peut être améliorée, et chaque amélioration supplémentaire offre +2 Capacité de Bâtisseur.",
   Pamukkale: "Pamukkale",
   PamukkaleDesc: "When discovered, convert each one of the Permanent Great People Shards (except for Promotion and Wildcard) to the same Great Person From This Run",
   Panathenaea: "Panathénaea : Poséidon offre +1 Multiplicateur de Production à tous les bâtiments.",
   Pantheon: "Panthéon",
   PantheonDescV2: "Tous les bâtiments dans un rayon de 2 tuiles reçoivent +1 en Capacité de Travailleurs et en Multiplicateur de Stockage. Génère de la science en fonction de la production de foi de tous les sanctuaires.",
   Paper: "Papier",
   PaperMaker: "Papeterie",
   Parliament: "Parlement",
   Parthenon: "Parthénon",
   ParthenonDescV2: "Deux personnages illustres de l'Antiquité apparaissent et vous avez 4 choix pour chacun d'entre eux. Les guildes de musiciens et les guildes de peintres gagnent +1 Production, Capacité des Ouvriers et Multiplicateur de Stockage et leur fonctionnement vous coûte 1 bonheur de moins que d'habitude",
   Passcode: "Code d'accès",
   PasscodeToastHTML: "<b>%{code}</b> Voici votre code d'accès, il est valable pendant 30 minutes",
   PatchNotes: "Notes de mise à jour",
   Peace: "paix",
   Peacekeeper: "Garde de la paix",
   Penthouse: "Penthouse",
   PercentageOfProductionWorkers: "Pourcentage de travailleurs de production",
   Performance: "Performance",
   PermanentGreatPeople: "Personnages illustres permanents",
   PermanentGreatPeopleAcquired: "Personnages illustres permanents acquis",
   PermanentGreatPeopleUpgradeUndo: "Annuler l'amélioration des personnages illustres permanents : cela convertira le niveau amélioré en fragments - vous recevrez %{amount} fragments.",
   Persepolis: "Persépolis",
   PersepolisDesc: "Tous les camps miniers de cuivre, les camps de bûcherons et les carrières de pierre gagnent +1 Multiplicateur de Production, Multiplicateur de Capacité des Ouvriers et Multiplicateur de Stockage",
   PeterHiggs: "Peter Higgs",
   PeterHiggsDesc: "+%{value} Science provenant des travailleurs occupés",
   Petra: "Pétra",
   PetraDesc: "Génère une distorsion temporelle lorsque vous êtes hors ligne, que vous pouvez utiliser pour accélérer votre empire",
   PetraOfflineTimeReconciliation: "Vous avez été crédité de %{count} distorsion après la réconciliation du temps d'indisponibilité du serveur.",
   Petrol: "Essence",
   PhiloFarnsworth: "Philo Farnsworth",
   Philosophy: "Philosophie",
   Physics: "Physique",
   PierreDeCoubertin: "Pierre de Coubertin",
   Pizza: "Pizza",
   Pizzeria: "Pizzeria",
   PlanetaryRover: "Rover Planétaire",
   Plastics: "Plastique",
   PlasticsFactory: "Usine de plastique",
   PlatformAndroid: "Android",
   PlatformiOS: "iOS",
   PlatformSteam: "Steam",
   PlatformSyncInstructionHTML: "Si vous souhaitez synchroniser vos progrès sur cet appareil avec un nouvel appareil, cliquez sur <b>Synchroniser vers un nouvel appareil</b> et obtenez un code d'accès unique. Sur votre nouvel appareil, cliquez sur <b>Se connecter à un appareil</b> et saisissez le code d'accès unique",
   Plato: "Platon",
   PlayerHandle: "Profil",
   PlayerHandleOffline: "Vous êtes actuellement hors ligne",
   PlayerMapClaimThisTile: "Occuper cette case",
   PlayerMapClaimTileCondition2: "Vous n'avez pas été banni par l'anti-triche",
   PlayerMapClaimTileCondition3: "Vous avez débloqué la technologie requise : %{tech}",
   PlayerMapClaimTileCondition4: "Vous n'avez pas occupé de case ou vous avez dépassé le délai de récupération pour la déplacer",
   PlayerMapClaimTileCooldownLeft: "Délai de récupération restant : %{time}",
   PlayerMapClaimTileNoLongerReserved: "Cette case n'est plus réservée. Vous pouvez expulser %{name} et vous approprier cette case",
   PlayerMapEstablishedSince: "Fondé depuis le",
   PlayerMapLastSeenAt: "Vu pour la dernière fois le",
   PlayerMapMapTileBonus: "Bonus pour les tuiles de commerce",
   PlayerMapMenu: "Commerce",
   PlayerMapOccupyThisTile: "Occuper cette tuile",
   PlayerMapOccupyTileCondition1: "Cette tuile est adjacente à votre ville ou aux tuiles occupées",
   PlayerMapPageGoBackToCity: "Retourner à la ville",
   PlayerMapSetYourTariff: "Fixez votre taxe douanière",
   PlayerMapTariff: "Taxe douanière",
   PlayerMapTariffApply: "Appliquer la taxe douanière",
   PlayerMapTariffDesc: "Chaque échange qui nécessite de passer par votre case vous devra une taxe douanière. Il faut trouver un équilibre : si vous augmentez son prix, vous gagnerez plus à chaque échange, mais plus rares seront ceux qui passeront par votre case",
   PlayerMapTileAvailableTilePoint: "Available Tile Point",
   PlayerMapTileFromOccupying: "From Owned/Occupied Tiles",
   PlayerMapTileFromOccupyingTooltipHTML: "An owned/occupied tile generates <b>%{point}</b> tile point per hour (up to %{max} days from the first claimed tile)",
   PlayerMapTileFromRank: "From Account Rank",
   PlayerMapTileTilePoint: "Tile Point",
   PlayerMapTileUsedTilePoint: "Used Tile Point",
   PlayerMapTileUsedTilePointTooltipHTML: "You need <b>1 tile point</b> to own/occupy a tile",
   PlayerMapTradesFrom: "Échanges de %{name}",
   PlayerMapUnclaimedTile: "Case inoccupée",
   PlayerMapYourTile: "Votre case",
   PlayerTrade: "Échanger avec les joueurs",
   PlayerTradeAddSuccess: "L'échange a été ajouté avec succès",
   PlayerTradeAddTradeCancel: "Annuler",
   PlayerTradeAmount: "Quantité",
   PlayerTradeCancelDescHTML: "Vous récupererez <b>%{res}</b> après avoir annulé cet échange : <b>%{percent}</b> facturés pour remboursement et <b>%{discard}</b> jetés en raison du sur-stockage. <br><b>Voulez-vous vraiment annuler ?</b>",
   PlayerTradeCancelTrade: "Annuler l'échange",
   PlayerTradeClaim: "Récupérer",
   PlayerTradeClaimAll: "Tout récuperer",
   PlayerTradeClaimAllFailedMessageV2: "Impossible de récuperer les échanges - le stockage est-il plein ?",
   PlayerTradeClaimAllMessageV2: "Vous avez récupéré : <b>%{resources}</b>",
   PlayerTradeClaimAvailable: "%{count} échange(s) ont été finalisé(s) et peuvent être récupéré(s)",
   PlayerTradeClaimTileFirst: "Occuper votre case sur la carte commerciale",
   PlayerTradeClaimTileFirstWarning: "Vous ne pouvez échanger avec d'autres joueurs qu'après avoir occupé une case sur la carte commerciale",
   PlayerTradeClearAll: "Effacer toutes les zones remplies",
   PlayerTradeClearFilter: "Effacer les filtres",
   PlayerTradeDisabledBeta: "Vous ne pouvez créer des échanges entre joueurs qu'une fois la version bêta publiée.",
   PlayerTradeFill: "Vendre",
   PlayerTradeFill50: "Remplir 50%",
   PlayerTradeFill95: "Remplir 95%",
   PlayerTradeFillAmount: "Quantité en vente",
   PlayerTradeFillAmountMaxV2: "Remplir au maximum",
   PlayerTradeFillBy: "Joueur",
   PlayerTradeFillPercentage: "Remplir en pourcentage",
   PlayerTradeFillSuccessV2: "<b>%{success}/%{total}</b> échanges ont été remplis. Vous avez payé <b>%{fillAmount} %{fillResource}</b> et reçu <b>%{receivedAmount} %{receivedResource}</b>.",
   PlayerTradeFillTradeButton: "Confirmer l'échange",
   PlayerTradeFillTradeTitle: "Confirmer l'échange",
   PlayerTradeFilters: "Filtres",
   PlayerTradeFiltersApply: "Appliquer",
   PlayerTradeFiltersClear: "Réinitialiser",
   PlayerTradeFilterWhatIHave: "Filter By What I Have",
   PlayerTradeFrom: "De",
   PlayerTradeIOffer: "Je propose",
   PlayerTradeIWant: "Je veux",
   PlayerTradeMaxAll: "Maximiser toutes les zones remplies",
   PlayerTradeMaxTradeAmountFilter: "Montant maximal",
   PlayerTradeMaxTradeExceeded: "Vous avez dépassé le nombre maximum d'échanges actifs pour le rang de votre compte",
   PlayerTradeNewTrade: "Nouvel échange",
   PlayerTradeNoFillBecauseOfResources: "Aucun échange n'a été rempli en raison de ressources insuffisantes",
   PlayerTradeNoValidRoute: "Impossible de trouver une route commerciale valable entre vous et %{name}",
   PlayerTradeOffer: "Échangé",
   PlayerTradePlaceTrade: "Demander",
   PlayerTradePlayerNameFilter: "Pseudo",
   PlayerTradeResource: "Ressource",
   PlayerTradeStorageRequired: "Stockage requis",
   PlayerTradeTabImport: "Importer",
   PlayerTradeTabPendingTrades: " Échanges en attente",
   PlayerTradeTabTrades: "Échanges",
   PlayerTradeTariffTooltip: "Obtenu grâce à une taxe douanière",
   PlayerTradeWant: "Demandé",
   PlayerTradeYouGetGross: "Vous obtenez (avant la taxe douanière) : %{res}",
   PlayerTradeYouGetNet: "Vous obtenez (après la taxe douanière) : %{res}",
   PlayerTradeYouPay: "Vous payez : %{res}",
   Poem: "Poésie",
   PoetrySchool: "École de Poésie",
   Politics: "Politique",
   PolytheismLevelX: "Polytheism %{level}",
   PorcelainTower: "Pagode de porcelaine",
   PorcelainTowerDesc: "+5 Bonheur. Une fois construite, tous vos personnages illustres supplémentaires lors de la réincarnation deviendront disponibles dans cette partie (they are rolled following the same rule as permanent great people)",
   PorcelainTowerMaxPickPerRoll: "Préférer le maximum de choix par tirage",
   PorcelainTowerMaxPickPerRollDescHTML: "Lors du choix des personnages illustres après l'achèvement de la Tour de Porcelaine, privilégiez le maximum de choix par tirage pour le montant disponible.",
   Poseidon: "Poséidon",
   PoseidonDescV2: "Tous les bâtiments adjacents reçoivent des améliorations gratuites jusqu'au niveau 25 et +N en Production, Capacité de Travailleurs et Multiplicateur de Stockage. N = Niveau du bâtiment.",
   PoultryFarm: "Ferme Avicole",
   Power: "Énergie",
   PowerAvailable: "Énergie disponible",
   PowerUsed: "Énergie utilisée",
   PreciousMetal: "Métal précieux",
   Printing: "Impremerie",
   PrintingHouse: "Imprimerie",
   PrintingPress: "Presse d'imprimerie",
   PrivateOwnership: "Propriété privée",
   Produce: "Production",
   ProduceResource: "Produit : %{resource}",
   ProductionMultiplier: "Multiplicateur de Production",
   ProductionPriority: "Priorité de production",
   ProductionPriorityDescV4: "La priorité détermine l'ordre dans lequel les bâtiments transportent et produisent - un nombre plus élevé signifie qu'un bâtiment transporte et produit avant les autres bâtiments",
   ProductionWorkers: "Ouvriers occupés à produire des ressources",
   Progress: "Progression",
   ProgressTowardsNextGreatPerson: "Progression jusqu'au prochain personnage illustre lors de la réincarnation",
   ProgressTowardsTheNextGreatPerson: "Progrès vers le prochain personnage illustre",
   PromotionGreatPersonDescV2: "Lorsqu'il est consommé, promouvez tous les personnages illustres permanents de la même ère au niveau de l'ère suivante.",
   ProphetsMosque: "Mosquée du Prophète",
   ProphetsMosqueDesc: "Double l'effet de Harun al-Rashid. Génère de la science en fonction de la production de foi de toutes les mosquées.",
   Province: "Province",
   ProvinceAegyptus: "Égyptos",
   ProvinceAfrica: "Afrique",
   ProvinceAsia: "Asie",
   ProvinceBithynia: "Bithynie",
   ProvinceCantabri: "Cantabrie",
   ProvinceCappadocia: "Cappadoce",
   ProvinceCilicia: "Cilicie",
   ProvinceCommagene: "Commagène",
   ProvinceCreta: "Crète",
   ProvinceCyprus: "Chypre",
   ProvinceCyrene: "Cyrène",
   ProvinceGalatia: "Galatie",
   ProvinceGallia: "Gaule",
   ProvinceGalliaCisalpina: "Gaule cisalpine",
   ProvinceGalliaTransalpina: "Gaule transalpine",
   ProvinceHispania: "Hispanie",
   ProvinceIllyricum: "Illyricum",
   ProvinceItalia: "Italie",
   ProvinceJudia: "Judée",
   ProvinceLycia: "Lycie",
   ProvinceMacedonia: "Macédoine",
   ProvinceMauretania: "Maurétanie",
   ProvinceNumidia: "Numidie",
   ProvincePontus: "Pontus",
   ProvinceSardiniaAndCorsica: "Corse-Sardaigne",
   ProvinceSicillia: "Sicile",
   ProvinceSophene: "Sophène",
   ProvinceSyria: "Syrie",
   PublishingHouse: "Maison d'édition",
   PyramidOfGiza: "Pyramide de Gizeh",
   PyramidOfGizaDesc: "Tous les bâtiments qui produisent des ouvriers gagnent +1 Multiplicateur de Production",
   QinShiHuang: "Qin Shi Huang",
   Radio: "Radio",
   RadioStation: "Station de radio",
   Railway: "Voie ferrée",
   RamessesII: "Ramsès II",
   RamessesIIDesc: "+%{value} Multiplicateur de Capacité de construction",
   RandomColorScheme: "Schéma de couleurs aléatoire",
   RapidFire: "Rafale",
   ReadFullPatchNotes: "Lire les notes de mise à jour",
   RebirthHistory: "Historique de renaissance",
   RebirthTime: "temps de renaissance",
   Reborn: "Réincarnation",
   RebornModalDescV3:
      "Vous commencerez un nouvel empire, mais tous vos personnages illustres <b>de cette partie</b> deviennent des fragments permanents, qui peuvent être utilisés pour améliorer votre <b>niveau de personnages illustres permanents</b>. Vous recevrez également des fragments de personnages illustres supplémentaires en fonction de votre <b>valeur totale de l'empire</b>.",
   RebornOfflineWarning: "Vous êtes actuellement hors ligne. La réincarnation n'est possible que lorsque vous êtes connecté au serveur",
   RebornTradeWarning: "Vous avez des échanges actifs ou qui peuvent être récupérés. <b>La réincarnation les effacera</b> - vous devez d'abord les annuler ou les récupérer",
   RedistributeAmongSelected: "Redistribuer parmi la sélection",
   RedistributeAmongSelectedCap: "Plafond",
   RedistributeAmongSelectedImport: "Importer",
   Refinery: "Raffinerie",
   Reichstag: "Reichstag",
   Religion: "Religion",
   ReligionBuddhism: "Bouddhisme",
   ReligionChristianity: "Christianisme",
   ReligionDescHTML: "Choisissez parmi <b>le Christianisme, l'Islam, le Bouddhisme ou le Polythéisme</b> comme religion de votre empire. Vous <b>ne pouvez pas changer de religion</b> une fois choisie. Vous pouvez débloquer davantage de bonus au sein de chaque religion.",
   ReligionIslam: "Islam",
   ReligionPolytheism: "Polythéisme",
   Renaissance: "Renaissance",
   RenaissanceAge: "Renaissance",
   ReneDescartes: "René Descartes",
   RequiredDeposit: "Ressource requise",
   RequiredWorkersTooltipV2: "Le nombre de travailleurs requis pour la production est égal à la somme de toutes les ressources consommées et produites après les multiplicateurs (à l'exception des multiplicateurs dynamiques).",
   RequirePower: "Énergie requise",
   RequirePowerDesc: "Ce bâtiment nécessite d'être construit sur une case possédant de l'énergie et peut diffuser cette dernière aux cases adjacentes",
   Research: "Recherche",
   ResearchFund: "Fonds de recherche",
   ResearchLab: "Laboratoire de recherche",
   ResearchMenu: "Technologies",
   ResourceAmount: "Quantité",
   ResourceBar: "Barre des ressources",
   ResourceBarExcludeStorageFullHTML: "Exclure les bâtiments qui ont un <b>stockage plein</b> de la catégorie des bâtiments inactifs",
   ResourceBarExcludeTurnedOffOrNoActiveTransportHTML: "Exclure les bâtiments dont <b>vous avez désactivé la production</b> de la catégorie des bâtiments inactifs",
   ResourceBarShowUncappedHappiness: "Afficher le Bonheur Sans Limite",
   ResourceCloneTooltip: "Le multiplicateur de production ne s'applique qu'à la ressource clonée (c'est-à-dire à la copie supplémentaire).",
   ResourceColor: "Couleur des ressources",
   ResourceExportBelowCap: "Exporter en dessous du plafond",
   ResourceExportBelowCapTooltip: "Autoriser d'autres bâtiments à transporter une ressource depuis ce bâtiment même lorsque sa quantité est inférieure au plafond.",
   ResourceExportToSameType: "Exporter vers le même type",
   ResourceExportToSameTypeTooltip: "Autoriser les autres bâtiments du même type à transporter une ressource depuis ce bâtiment.",
   ResourceFromBuilding: "%{resource} de %{building}",
   ResourceImport: "Transport des ressources",
   ResourceImportCapacity: "Capacité de Transport de Ressources",
   ResourceImportImportCapV2: "Quantité max.",
   ResourceImportImportCapV2Tooltip: "Ce bâtiment arrêtera de transporter cette ressource lorsque la quantité maximale aura été atteinte",
   ResourceImportImportPerCycleV2: "Par cycle",
   ResourceImportImportPerCycleV2ToolTip: "La quantité de cette ressource qui est transportée par cycle",
   ResourceImportPartialWarningHTML: "La capacité totale de transport de ressources a dépassé la capacité maximale : <b>chaque transport de ressource ne transportera qu'une partie par cycle</b>.",
   ResourceImportResource: "Ressource",
   ResourceImportSettings: "Transport des ressources : %{res}",
   ResourceImportStorage: "Stockage",
   ResourceNeeded: "Extra %{resource} x%{amount} Demander",
   ResourceTransportPreference: "Préférence de transport",
   RevealDeposit: "Révéler",
   Revolution: "Révolution",
   RhineGorge: "Gorge du Rhin",
   RhineGorgeDesc: "+2 Bonheur pour chaque merveille dans un rayon de 2 tuiles.",
   RichardFeynman: "Richard Feynman",
   RichardFeynmanDesc: "+%{value} Science produite par tous les ouvriers si plus de 50% d'entre eux sont actifs et que moins de 50% des ouvriers actifs transportent des ressources",
   RichardJordanGatling: "Richard Jordan Gatling",
   Rifle: "Fusil",
   RifleFactory: "Usine de fusils",
   Rifling: "Canon rayé",
   Rijksmuseum: "Rijksmuseum",
   RijksmuseumDesc: "+5 Bonheur. Tous les bâtiments qui consomment ou produisent de la culture gagnent +1 Production, Stockage and Capacité des Ouvriers",
   RoadAndWheel: "Route & Roue",
   RobertNoyce: "Robert Noyce",
   Robocar: "Robocar",
   RobocarFactory: "Usine de Robocar",
   Robotics: "Robotique",
   RockefellerCenterChristmasTree: "Rockefeller Center Christmas Tree",
   RockefellerCenterChristmasTreeDesc: "+3 Bonheur pour chaque ère débloquée. Cette merveille naturelle ne peut être découverte qu'en décembre",
   Rocket: "Fusée",
   RocketFactory: "Usine de fusées",
   Rocketry: "Fuséologie",
   Roman: "Roman",
   RomanForum: "Forum Romain",
   RudolfDiesel: "Rudolf Diesel",
   Rurik: "Riourik",
   RurikDesc: "+%{value} Bonheur",
   SagradaFamilia: "Sagrada Família",
   SagradaFamiliaDesc: "Tous les bâtiments dans un rayon de 2 cases gagnent +N Production, Capacité des Ouvriers et Multiplicateur de Stockage. N = différence maximale de catégorie parmi les bâtiments adjacents (dans un rayon de 1 case) à la Sagrada Família",
   SaintBasilsCathedral: "Cathédrale Saint-Basile",
   SaintBasilsCathedralDescV2: "Permet aux bâtiment d'extraction de ressources de fonctionner en étant adjacents à une ressource. Tous les bâtiments de catégorie I gagnent +1 Multiplicateur de Production, Multiplicateur de Capacité des Ouvriers et Multiplicateur de Stockage",
   Saladin: "Saladin",
   Samsuiluna: "Samsu-iluna",
   Sand: "Sable",
   Sandpit: "Carrière de sable",
   SantaClausVillage: "Village du Père Noël",
   SantaClausVillageDesc:
      " Lorsqu'elle est terminée, un personnage illustre de l'ère actuelle est né. Cette merveille peut être améliorée, et chaque amélioration supplémentaire offre un personnage illustre supplémentaire. Lors du choix des personnages illustres de cette merveille, 4 choix sont proposés. Cette merveille ne peut être construite qu'en décembre",
   SargonOfAkkad: "Sargon d'Akkad",
   Satellite: "Satellite",
   SatelliteFactory: "Usine de satellites",
   SatoshiNakamoto: "Satoshi Nakamoto",
   Saturnalia: "Saturnalia : Les Alpes n'augmentent plus le Multiplicateur de Consommation.",
   SaveAndExit: "Sauvegarder et quitter",
   School: "École",
   Science: "Science",
   ScienceFromBusyWorkers: "Science produite par les ouvriers actifs",
   ScienceFromIdleWorkers: "Science produite par les ouvriers inactifs",
   SciencePerBusyWorker: "Par ouvrier actif",
   SciencePerIdleWorker: "Par ouvrier inactif",
   ScrollSensitivity: "Sensibilité du défilement",
   ScrollSensitivityDescHTML: "Ajustez la sensibilité lors du défilement de la molette de la souris. <b>Doit être compris entre 0,01 et 100. La valeur par défaut est 1.</b>",
   ScrollWheelAdjustLevelTooltip: "Vous pouvez utiliser la molette de votre souris pour ajuster le niveau lorsque votre curseur se trouve ici",
   SeaTradeCost: "Coût du commerce maritime",
   SeaTradeUpgrade: "Permet de commercer avec des joueurs par-délà la mer. Taxe pour chaque case de mer : %{tariff}",
   SelectCivilization: "Sélectionner la civilisation",
   SelectedAll: "Tout sélectionner",
   SelectedCount: "%{count} sélectionné",
   Semiconductor: "Semi-conducteur",
   SemiconductorFab: "Usine de semi-conducteurs",
   SendExplorer: "Envoyer un explorateur",
   SergeiKorolev: "Sergueï Korolev",
   SetAsDefault: "Définir par défaut",
   SetAsDefaultBuilding: "Définir par défaut pour les %{building}",
   Shamanism: "Chamanisme",
   Shelter: "Abri",
   Shortcut: "Raccourcis",
   ShortcutBuildingPageSellBuildingV2: "Démolir un bâtiment",
   ShortcutBuildingPageToggleBuilding: "Basculer la production",
   ShortcutBuildingPageToggleBuildingSetAllSimilar: "Basculer la production et appliquer à tous",
   ShortcutBuildingPageUpgrade1: "Bouton d'amélioration 1 (+1)",
   ShortcutBuildingPageUpgrade2: "Bouton d'amélioration 2 (+5)",
   ShortcutBuildingPageUpgrade3: "Bouton d'amélioration 3 (+10)",
   ShortcutBuildingPageUpgrade4: "Bouton d'amélioration 4 (+15)",
   ShortcutBuildingPageUpgrade5: "Bouton d'amélioration 5 (+20)",
   ShortcutClear: "Supprimer",
   ShortcutConflict: "Ce raccourci existe déjà pour %{name}",
   ShortcutNone: "Non assigné",
   ShortcutPressShortcut: "Appuyez sur une touche...",
   ShortcutSave: "Sauvegarder",
   ShortcutScopeBuildingPage: "Page des bâtiments",
   ShortcutScopeConstructionPage: "Page de la constructions/amélioration",
   ShortcutScopeEmptyTilePage: "Page des cases vierges",
   ShortcutScopePlayerMapPage: "Page de la carte commerciale",
   ShortcutScopeTechPage: "Pages des technologies",
   ShortcutScopeUnexploredPage: "Page des cases inexplorées",
   ShortcutTechPageGoBackToCity: "Retourner à la ville",
   ShortcutTechPageUnlockTech: "Débloquer la technologie sélectionnée",
   ShortcutUpgradePageCancelAllUpgrades: "Cancel All Upgrades",
   ShortcutUpgradePageCancelUpgrade: "Annuler l'amélioration",
   ShortcutUpgradePageDecreaseLevel: "Diminuer le niveau d'amélioration",
   ShortcutUpgradePageEndConstruction: "Abandonner la construction",
   ShortcutUpgradePageIncreaseLevel: "Augmenter le niveau d'amélioration",
   ShowTransportArrow: "Afficher la flèche de transport",
   ShowTransportArrowDescHTML: "Désactiver cette option masquera les flèches de transport. Cela pourrait <i>légèrement</i> améliorer les performances sur les appareils de faible puissance. L'amélioration des performances prendra effet <b>après avoir redémarré votre jeu</b>. ",
   ShowUnbuiltOnly: "Afficher uniquement les bâtiments qui n'ont pas encore été construits",
   Shrine: "Sanctuaire",
   SidePanelWidth: "Largeur du menu latéral",
   SidePanelWidthDescHTML: "Modifie la largeur du menu latéral. <b>Ce changement nécessite de rédemarrer le jeu pour prendre effet</b>",
   SiegeRam: "Bélier",
   SiegeWorkshop: "Atelier de siège",
   Silicon: "Silicium",
   SiliconSmelter: "Fonderie de silicium",
   Skyscraper: "Gratte-ciel",
   Socialism: "Socialisme",
   SocialismLevel4DescHTMLV2: "Génère une science unique équivalente au coût de la technologie la moins chère de l'<b>Ère des Guerres Mondiales</b>.",
   SocialismLevel5DescHTMLV2: "Génère une science unique équivalente au coût de la technologie la moins chère de l'<b>Ère de la Guerre Froide</b>.",
   SocialismLevelX: "Niveau de Socialisme %{level}",
   SocialNetwork: "Réseau social",
   Socrates: "Socrate",
   SocratesDesc: "+%{value} Science produite par les ouvriers actifs",
   Software: "Logiciel",
   SoftwareCompany: "Entreprise de Logiciels",
   Sound: "Son",
   SoundEffect: "Effets sonores",
   SourceGreatPerson: "Personnage illustre : %{person}",
   SourceGreatPersonPermanent: "Personnage illustre permanent : %{person}",
   SourceIdeology: "Ideology: %{ideology}",
   SourceReligion: "Religion: %{religion}",
   SourceResearch: "Recherche : %{tech}",
   SourceTradition: "Tradition: %{tradition}",
   SpaceCenter: "Centre Spatial",
   Spacecraft: "Vaisseau spatial",
   SpacecraftFactory: "Usine de vaisseaux spatiaux",
   SpaceNeedle: "Space Needle",
   SpaceNeedleDesc: "+1 Bonheur pour chaque Merveille bâtie",
   SpaceProgram: "Programme spatial",
   Sports: "Sport",
   Stable: "Écurie",
   Stadium: "Stade",
   StartFestival: "Que le festival commence !",
   Stateship: "Meunerie",
   StatisticsBuildings: "Bâtiments",
   StatisticsBuildingsSearchText: "Entrez le nom d'un bâtiment pour rechercher",
   StatisticsEmpire: "Empire",
   StatisticsExploration: "Exploration",
   StatisticsOffice: "Bureau des statistiques",
   StatisticsOfficeDesc: "Fournit les statistiques de votre empire. Génère des explorateurs afin de découvrir la carte",
   StatisticsResources: "Ressources",
   StatisticsResourcesDeficit: "Profit",
   StatisticsResourcesDeficitDesc: "Entrée : %{output} - Sortie : %{input}",
   StatisticsResourcesRunOut: "Épuisée dans",
   StatisticsResourcesSearchText: "Entrez le nom d'un bâtiment pour rechercher",
   StatisticsScience: "Science",
   StatisticsScienceFromBuildings: "Science produite par les bâtiments",
   StatisticsScienceFromWorkers: "Science produite par les ouvriers",
   StatisticsScienceProduction: "Production de science",
   StatisticsStalledTransportation: "Transport en pause",
   StatisticsTotalTransportation: "Transport total",
   StatisticsTransportation: "Transport",
   StatisticsTransportationPercentage: "Pourcentage d'ouvriers occupés à transporter des ressources",
   StatueOfLiberty: "Statue de la Liberté",
   StatueOfLibertyDesc: "Tous les bâtiments adjacents gagnent +N Production, Stockage et Multiplicateur de Capacité des Ouvriers. N = Nombre de bâtiments adjacents du même type",
   StatueOfZeus: "Statue de Zeus",
   StatueOfZeusDesc: "Fait apparaître sur les cases vierges adjacentes des ressources aléatoires déjà révélées. Tous les bâtiments adjacents de catégorie I gagnent +5 Production et Multiplicateur de Stockage",
   SteamAchievement: "Succès Steam",
   SteamAchievementDetails: "Voir les succès Steam",
   SteamEngine: "Machine à vapeur",
   Steamworks: "Usine de moteurs",
   Steel: "Acier",
   SteelMill: "Fonderie",
   StephenHawking: "Stephen Hawking",
   Stock: "Action",
   StockExchange: "Marché boursier",
   StockMarket: "Bourse",
   StockpileDesc: "Ce bâtiment transportera %{capacity}x les ressources d'entrée par cycle de production jusqu'à ce que le maximum soit atteint",
   StockpileMax: "Stockage maximal",
   StockpileMaxDesc: "Ce bâtiment cessera de transporter une ressource lorsqu'il y en aura suffisamment pour %{cycle} cycles de production",
   StockpileMaxUnlimited: "Illimité",
   StockpileMaxUnlimitedDesc: "Ce bâtiment ne cessera jamais de transporter des ressources jusqu'à ce que le stockage soit plein",
   StockpileSettings: "Capacité d'entrée du stock",
   Stone: "Pierre",
   StoneAge: "Âge de pierre",
   Stonehenge: "Stonehenge",
   StonehengeDesc: "Tous les bâtiments qui consomment ou produisent de la pierre gagnent +1 Multiplicateur de Production",
   StoneQuarry: "Carrière de pierre",
   StoneTool: "Outil en pierre",
   StoneTools: "Outils en pierre",
   Storage: "Stockage",
   StorageBaseCapacity: "Capacité de base",
   StorageMultiplier: "Multiplicateur de Stockage",
   StorageUsed: "Stockage utilisé",
   StPetersBasilica: "Basilique Saint-Pierre",
   StPetersBasilicaDescV2: "Toutes les églises reçoivent +5 Multiplicateur de Stockage. Génère de la science en fonction de la production de foi de toutes les églises.",
   Submarine: "Sous-marin",
   SubmarineYard: "Chantier naval",
   SuleimanI: "Suleiman I",
   SummerPalace: "Palais d'Été",
   SummerPalaceDesc: "Le fonctionnement de tous les bâtiments adjacents qui consomment ou produisent de la poudre à canon vous coûte 1 bonheur de moins que d'habitude. Tous les bâtiments qui consomment ou produisent de la poudre à canon gagnent +1 Production, Stockage et Capacité des Ouvriers",
   Supercomputer: "Superordinateur",
   SupercomputerLab: "Laboratoire de Superordinateur",
   SupporterPackRequired: "Supporter Pack requis",
   SupporterThankYou: "CivIdle est maintenu grâce à la générosité des propriétaires des packs de soutien suivants.",
   SwissBank: "Swiss Bank",
   SwissBankDescV2: "Convert a chosen resource from adjacent warehouses to Koti (10 million in Sanskrit) - a tradeable resource that is worth 10M value. Each level of Swiss Bank add 1 Koti conversion per cycle, which is affected by Production Multiplier. Swiss Bank can store unlimited amount of Koti",
   Sword: "Épée",
   SwordForge: "Forge d'épée",
   SydneyOperaHouse: "Opéra de Sydney",
   SydneyOperaHouseDescV2: "Opéra de Sydney",
   SyncToANewDevice: "Synchroniser vers un nouvel appareil",
   Synthetics: "Matières synthétiques",
   TajMahal: "Taj Mahal",
   TajMahalDescV2: "Un personnage illustre de l'Antiquité et un personnage illustre du Moyen Âge apparaissent. +5 Multiplicateur de Capacité de construction dès qu'un bâtiment est amélioré au-délà du niveau 20",
   TangOfShang: "Cheng Tang",
   TangOfShangDesc: "+%{value} Science produite par les ouvriers inactifs",
   Tank: "Tank",
   TankFactory: "Usine de tanks",
   TechAge: "Ère",
   TechGlobalMultiplier: "Boost",
   TechHasBeenUnlocked: "%{tech} a été débloqué",
   TechProductionPriority: "Débloque les priorités - permet de définir la priorité de production pour chaque bâtiment",
   TechResourceTransportPreference: "Débloque la préférence de transport - permet de définir la manière dont un bâtiment transporte les ressources nécessaires à sa production",
   TechResourceTransportPreferenceAmount: "Quantité",
   TechResourceTransportPreferenceAmountTooltip: "Ce bâtiment transportera des ressources provenant de bâtiments qui en ont une plus grande quantité dans leur stockage",
   TechResourceTransportPreferenceDefault: "Par défaut",
   TechResourceTransportPreferenceDefaultTooltip: "Ne pas modifier la préférence de transport pour cette ressource, mais utiliser la préférence de transport du bâtiment",
   TechResourceTransportPreferenceDistance: "Distance",
   TechResourceTransportPreferenceDistanceTooltip: "Ce bâtiment transportera des ressources provenant de bâtiments plus proches",
   TechResourceTransportPreferenceOverrideTooltip: "Préférence de transport pour cette ressource : %{mode}",
   TechResourceTransportPreferenceStorage: "Stockage",
   TechResourceTransportPreferenceStorageTooltip: "Ce bâtiment transportera des ressources provenant de bâtiments qui ont un pourcentage plus élevé de stockage utilisé",
   TechStockpileMode: "Débloque le mode de stockage - permet d'ajuster le stockage pour chaque bâtiment",
   Teleport: "Téléporteur",
   TeleportDescHTML: "Un téléporteur est généré <b>toutes les %{time} secondes</b>. Il peut être utilisé pour <b>déplacer un bâtiment (sauf les Merveilles)</b> une fois",
   Television: "Télévision",
   TempleOfArtemis: "Temple d'Artémis",
   TempleOfArtemisDesc: "Toutes les forges d'épées et armureries gagnent +5 Niveau. Elles gagnent également +1 Multiplicateur de Production, Multiplicateur de Capacité des Ouvriers et Multiplicateur de Stockage",
   TempleOfHeaven: "Temple du Ciel",
   TempleOfHeavenDesc: "Tous les bâtiments de niveau 10 ou supérieur gagnent +1 Multiplicateur de Capacité des Ouvriers",
   TempleOfPtah: "Temple de Ptah",
   TerracottaArmy: "Armée de terre cuite",
   TerracottaArmyDesc: "Tous les camps miniers de fer gagnent +1 Multiplicateur de Production, Multiplicateur de Capacité des Ouvriers et Multiplicateur de Stockage. Les forges de fer gagnent +1 Multiplicateur de Production pour chaque camp minier de fer adjacent",
   Thanksgiving: "Thanksgiving : Wall Street double le bonus aux bâtiments et s'applique aux Fonds Communs de Placement, Fonds spéculatifs et Mineurs de Bitcoin. Les Fonds de Recherche reçoivent +5 Multiplicateur de Production.",
   Theater: "Théâtre",
   Theme: "Thème",
   ThemeColor: "Couleur du thème",
   ThemeColorResearchBackground: "Arrière-plan de l'arbre des technologies",
   ThemeColorReset: "Réinitialiser",
   ThemeColorResetBuildingColors: "Réinitialiser la couleur des bâtiments",
   ThemeColorResetResourceColors: "Réinitialiser la couleur des ressources",
   ThemeInactiveBuildingAlpha: "Épaisseur des bâtiments inactifs",
   ThemePremiumTile: "This tile is only available for Supporter Pack owners",
   ThemeResearchHighlightColor: "Couleur de la recherche selectionnée",
   ThemeResearchLockedColor: "Couleur des recherches non debloquées",
   ThemeResearchUnlockedColor: "Couleur des recherches débloquées",
   ThemeTransportIndicatorAlpha: "Épaisseur de l'indicateur de transport",
   Theocracy: "Théocratie",
   TheoreticalData: "Données théoriques",
   ThePentagon: "Le Pentagone",
   ThePentagonDesc: "Une fois construit, génère des téléportations qui peuvent être utilisées pour déplacer des bâtiments. Tous les bâtiments dans un rayon de 2 tuiles reçoivent +1 en Production, Capacité de Travailleurs et Multiplicateur de Stockage",
   TheWhiteHouse: "La Maison Blanche",
   ThomasEdison: "Thomas Edison",
   ThomasGresham: "Thomas Gresham",
   Tile: "Case",
   TileBonusRefreshIn: "Tile bonus will refresh in <b>%{time}</b>",
   TimBernersLee: "Tim Berners-Lee",
   TimeWarp: "Distorsion temporelle",
   TimeWarpWarning: "L'accélération à une vitesse supérieure à celle que votre ordinateur peut supporter peut entraîner une perte de données : C'EST À VOS RISQUES ET PÉRILS",
   ToggleWonderEffect: "Activer/Désactiver l'effet de la merveille",
   Tool: "Outil",
   TopkapiPalace: "Topkapı Palace",
   TopkapiPalaceDesc: "All buildings within 2 tile range get +X Storage Multiplier. X = 50% of its Production Multiplier (excluding Dynamic)",
   TotalEmpireValue: "Valeur totale de l'empire",
   TotalEmpireValuePerCycle: "Valeur totale de l'empire par cycle",
   TotalEmpireValuePerCyclePerGreatPeopleLevel: "Valeur totale de l'empire par cycle par personnage illustre",
   TotalEmpireValuePerWallSecond: "Valeur totale de l'Empire par seconde",
   TotalEmpireValuePerWallSecondPerGreatPeopleLevel: "Total Empire Value Per Wall Second Per Great People Level",
   TotalGameTimeThisRun: "Temps total de jeu pour cette partie",
   TotalScienceRequired: "Science totale requise",
   TotalStorage: "Stockage total",
   TotalWallTimeThisRun: "Temps total pour cette partie",
   TotalWallTimeThisRunTooltip: " Le temps réel (également appelé temps réel écoulé) mesure le temps réel pris pour cette partie. Il diffère du temps de jeu en ce que la Distorsion dans Petra et la Production hors ligne n'affectent pas le temps de mur, mais elles affectent le temps de jeu",
   TotalWorkers: "Nombre total d'ouvriers",
   TowerBridge: "Tower Bridge",
   TowerBridgeDesc: "Une fois construit, un personnage illustre des ères débloquées naît tous les 3600 cycles (1 heure de temps de jeu).",
   TowerOfBabel: "Tour de Babel",
   TowerOfBabelDesc: "Offre +2 Multiplicateur de Production à tous les bâtiments ayant au moins un bâtiment fonctionnel situé à côté de la merveille.",
   TradeFillSound: "'echange rempli' Son",
   TradeValue: "Valeur commerciale",
   TraditionCommerce: "Négoce",
   TraditionCultivation: "Érudition",
   TraditionDescHTML: "Choisissez une tradition au sein de votre empire entre <b>Érudition, Négoce, Expansion et Vertu</b>. Il est <b>impossible de la modifier</b> une fois votre choix fait. Vous pourrez débloquer davantage de bonus dans chaque tradition",
   TraditionExpansion: "Expansion",
   TraditionHonor: "Vertu",
   Train: "Train",
   TranslationPercentage: "Ce jeu a été traduit à %{percentage} en %{language}. Vous pouvez apporter votre contribution sur GitHub",
   TranslatorCredit: "Théo Protche.vergiii.linna",
   Translators: "Traduction",
   TransportAllocatedCapacityTooltip: "Capacité de construction accordée au transport de cette ressource",
   TransportationWorkers: "Ouvriers occupés à transporter des ressources",
   TransportCapacity: "Capacité de Transport",
   TransportCapacityMultiplier: "Multiplicateur de Capacité de Transport",
   TransportManualControlTooltip: "Transporter cette ressource pour la construction/l'amélioration",
   TransportPlanCache: "Cache du plan de transport",
   TransportPlanCacheDescHTML:
      "À chaque cycle, chaque bâtiment calcule le meilleur plan de transport en fonction de ses paramètres - ce processus nécessite une grande puissance CPU. L'activation de cette option tentera de mettre en cache le résultat du plan de transport s'il est encore valide, réduisant ainsi l'utilisation du CPU et la chute du taux de rafraîchissement. <b>Fonctionnalité expérimentale</b>.",
   TribuneUpgradeDescGreatPeopleWarning: "Votre partie actuelle contient des personnages illustres. Vous devez <b>d'abord vous réincarner</b>. Passer au rang de Questeur réinitialisera votre partie actuelle",
   TribuneUpgradeDescGreatPeopleWarningTitle: "Veuillez d'abord vous réincarner",
   TribuneUpgradeDescV4:
      "Vous pouvez jouer à la version complète du jeu en tant que Tribune si vous ne prévoyez pas de participer aux fonctionnalités <b>optionnelles</b> en ligne. Pour obtenir un accès illimité aux fonctionnalités en ligne, vous devrez passer au rang de Questeur. <b>Ceci est une mesure anti-bot pour maintenir le jeu gratuit pour tous.</b> Cependant, <b>lors de la mise à niveau vers Questeur</b>, vous pouvez conserver les personnages illustres : <ul><li>Jusqu'au niveau <b>3</b> pour l'Âge du Bronze, l'Âge du Fer et l'Âge Classique</li><li>Jusqu'au niveau <b>2</b> pour l'Âge Moyen, la Renaissance et l'Âge Industriel</li><li>Jusqu'au niveau <b>1</b> pour l'Âge des Guerres Mondiales, l'Âge de la Guerre Froide et l'Âge de l'Information</li></ul>Les fragments de personnages illustres au-dessus de ce niveau et les niveaux de <b>Sagesse des Âges</b> <b>ne peuvent pas</b> être transférés.",
   TurnOffFullBuildings: "Désactiver tout %{building} stockage complet",
   TurnOnTimeWarpDesc: "Coûte %{speed} distorsion(s) temporelle(s) pour chaque seconde et accélère votre empire afin qu'il fonctionne en vitesse %{speed}x.",
   Tutorial: "Tutoriel",
   TutorialPlayerFlag: "Choisissez votre drapeau",
   TutorialPlayerHandle: "Choisissez votre pseudo",
   TV: "TV",
   TVStation: "Station de télévision",
   UnclaimedGreatPersonPermanent: "Il vous reste des <b>personnages illustres permanents</b> à collecter, cliquez ici pour les récuperer",
   UnclaimedGreatPersonThisRun: "Il vous reste des <b>personnages illustres</b> à collecter <b>dans cette partie</b>, cliquez ici pour les récuperer",
   UnexploredTile: "Case inexplorée",
   UNGeneralAssemblyCurrent: "Assemblée générale des Nations unies en cours #%{id}",
   UNGeneralAssemblyMultipliers: "<b>+%{count}</b> Production, Capacité des Ouvriers et Multiplicateurs de Stockage pour <b>%{buildings}</b>",
   UNGeneralAssemblyNext: "Assemblée générale des Nations unies à venir #%{id}",
   UNGeneralAssemblyVoteEndIn: "Vous pouvez modifier votre vote à tout moment avant la fin du scrutin dans <b>%{time}</b>",
   UniqueBuildings: "Bâtiments uniques",
   UniqueTechMultipliers: "Multiplicateurs technologiques uniques",
   UnitedNations: "Nations unies",
   UnitedNationsDesc: "Tous les bâtiments de catégorie IV, V et VI gagnent +1 Production, Capacité des Ouvriers et Multiplicateur de Stockage. Permet de participer aux assemblées générales des Nations unies et de voter pour un boost supplémentaire chaque semaine",
   University: "Université",
   UnlockableResearch: "Recherche débloquable",
   UnlockBuilding: "Débloquer",
   UnlockTechProgress: "Progression",
   UnlockXHTML: "Déverrouiller <b>%{name}</b>",
   Upgrade: "Améliorer",
   UpgradeBuilding: "Améliorer",
   UpgradeBuildingNotProducingDescV2: "Ce bâtiment est en train d'être amélioré - <b>la production sera interrompue jusqu'à ce l'amélioration soit terminée</b>",
   UpgradeTo: "Améliorer au niveau %{level}",
   Uranium: "Uranium",
   UraniumEnrichmentPlant: "Usine d'enrichissement d'uranium",
   UraniumMine: "Mine d'uranium",
   Urbanization: "Urbanisation",
   UserAgent: "User agent : %{driver}",
   View: "Voir",
   ViewMenu: "Affichage",
   ViewTechnology: "Voir",
   Vineyard: "Vignoble",
   VirtualReality: "Réalité virtuelle",
   Voltaire: "Voltaire",
   WallOfBabylon: "Mur de Babylone",
   WallOfBabylonDesc: "Tous les bâtiments reçoivent +N en Multiplicateur de Stockage. N = nombre d'ères débloquées / 2",
   WallStreet: "Wall Street",
   WallStreetDesc:
      "Tous les bâtiments qui produisent des pièces, des billets, des obligations financières, des actions et des devises dans un rayon de 2 cases gagnent +N Multiplicateur de Production. N = valeur aléatoire entre 1 et 5 qui diffère selon les bâtiments et qui change à chaque mise à jour du marché. Double l'effet de John D. Rockefeller",
   WaltDisney: "Walt Disney",
   Warehouse: "Entrepôt",
   WarehouseAutopilotSettings: "Paramètres de l'autopilote",
   WarehouseAutopilotSettingsEnable: "Activer l'autopilote",
   WarehouseAutopilotSettingsRespectCapSetting: "Stockage requis < plafond",
   WarehouseAutopilotSettingsRespectCapSettingTooltip: "L'autopilote ne transporte que les ressources dont la quantité stockée est inférieure au plafond",
   WarehouseDesc: "Permet de transporter des ressources spécifiques et fournit un espace de stockage supplémentaire",
   WarehouseExtension: "Débloquer le mode d'extension du caravansérail pour les entrepôts. Permet aux entrepôts adjacents aux caravansérails d'être inclus dans les échanges entre joueurs",
   WarehouseSettingsAutopilotDesc: "Cet entrepôt utilisera sa capacité inutilisée pour transporter les ressources des bâtiments dont le stockage est plein. Capacité inutilisée actuelle : %{capacity}",
   WarehouseUpgrade: "Débloque le mode autopilote pour les entrepôts. Permet le transport gratuit entre un entrepôt et les bâtiments adjacents",
   WarehouseUpgradeDesc: "Permet le transport gratuit entre cet entrepôt et les cases adjacents",
   Warp: "Distorsion temporelle",
   WarpSpeed: "Vitesse de distorsion",
   Water: "Eau",
   WellStockedTooltip: "Des bâtiment bien approvisionnés disposent de suffisamment de ressources pour leur production, ce qui inclut les bâtiments en production, dont le stockage est plein ou qui ne produisent rien en raison d'un manque d'ouvriers",
   WernherVonBraun: "Wernher von Braun",
   Westminster: "Westminster",
   Wheat: "Blé",
   WheatFarm: "Ferme à blé",
   WildCardGreatPersonDescV2: "Lorsqu'il est consommé, devient n'importe quel personnage illustre de la même ère.",
   WilliamShakespeare: "William Shakespeare",
   Wine: "Vin",
   Winery: "Domaine viticole",
   WishlistSpaceshipIdle: "Wishlist Spaceship Idle",
   Wonder: "Merveille",
   WonderBuilderCapacityDescHTML: "<b>La capacité de construction</b> lorsque vous bâtissez des Merveilles dépend de l'<b>ère</b> et la <b>technologie</b> qui vous a permis de la débloquer",
   WondersBuilt: "Merveilles du monde bâties",
   WondersUnlocked: "Merveilles du monde débloquées",
   WonderUpgradeLevel: "Niveau de la merveille",
   Wood: "Bois",
   Worker: "Ouvrier",
   WorkerCapacityMultiplier: "Multiplicateur de Capacité des Ouvriers",
   WorkerHappinessPercentage: "Multiplicateur de Bonheur",
   WorkerMultiplier: "Capacité des Ouvriers",
   WorkerPercentagePerHappiness: " %{value}% Multiplicateur pour chaque bonheur",
   Workers: "Ouvriers",
   WorkersAvailableAfterHappinessMultiplier: "Ouvriers après le Multiplicateur de Bonheur",
   WorkersAvailableBeforeHappinessMultiplier: "Ouvriers avant le Multiplicateur de Bonheur",
   WorkersBusy: "Ouvriers actifs",
   WorkerScienceProduction: "Production de science par les ouvriers",
   WorkersRequiredAfterMultiplier: "Ouvriers requis",
   WorkersRequiredBeforeMultiplier: "Capacité des Ouvriers requise",
   WorkersRequiredForProductionMultiplier: "Capacité de Production par Ouvrier",
   WorkersRequiredForTransportationMultiplier: "Capacité de Transport par Ouvrier",
   WorkersRequiredInput: "Transport",
   WorkersRequiredOutput: "Production",
   WorldWarAge: "Guerres mondiales",
   WorldWideWeb: "Web mondial",
   WritersGuild: "Guilde des écrivains",
   Writing: "Écriture",
   WuZetian: "Impératrice Wu Zetian",
   WuZetianDesc: "+%{value} Multiplicateur de Capacité de Transport",
   Xuanzang: "Xuanzang",
   YangtzeRiver: "Yangtsé",
   YangtzeRiverDesc:
      "Tous les bâtiments qui consomment de l'eau gagnent +1 Production, Capacité des Ouvriers et Multiplicateur de Stockage. Double l'effet de Zheng He (personnage illustre). Chaque niveau de l'Impératrice Wu Zetian (personnage illustre permanent) confère +1 Multiplicateur de Stockage à tous les bâtiments",
   YearOfTheSnake: "Année du Serpent",
   YearOfTheSnakeDesc:
      "Une fois complétée, lors de l'entrée dans une nouvelle ère, au lieu d'obtenir un personnage illustre de chaque ère débloquée, vous obtenez le même nombre de personnages illustres dans l'ère actuelle. Tous les bâtiments dans un rayon de 2 tuiles reçoivent +1 Multiplicateur de Production. Cette merveille peut être améliorée, et chaque amélioration supplémentaire offre +1 Multiplicateur de Production aux bâtiments dans un rayon de 2 tuiles. Cette merveille ne peut être construite que pendant la période du Nouvel An lunaire (du 1er décembre au 10 février)",
   YellowCraneTower: "Tour de la Grue jaune",
   YellowCraneTowerDesc: "+1 choix lors de la sélection d'un personnage illustre. Tous les bâtiments dans un rayon de 1 case gagnent +1 Production, Capacité des Ouvriers et Multiplicateur de Stockage. Lorsque cette Merveille est bâtie à côté du fleuve Yangtsé, ce rayon s'étend à 2 cases",
   YuriGagarin: "Yuri Gagarin",
   ZagrosMountains: "Zagros Mountains",
   ZagrosMountainsDesc: " Tous les bâtiments adjacents ayant un multiplicateur de production inférieur à 5 reçoivent +2 en Multiplicateur de Production. Double l'effet de Nabuchodonosor II (Personnage Illustre).",
   ZahaHadid: "Zaha Hadid",
   ZahaHadidDesc: "+%{value} Multiplicateur de Capacité de Bâtisseur",
   Zenobia: "Zénobie",
   ZenobiaDesc: "+%{value}h de stockage de Distorsion de Petra",
   ZhengHe: "Zheng He",
   ZigguratOfUr: "Ziggurat of Ur",
   ZigguratOfUrDescV2:
      "Chaque 10 bonheur (plafonné) procure +1 Multiplicateur de Production à tous les bâtiments qui ne produisent pas de travailleurs et qui sont débloqués dans les ères précédentes (max = nombre d'ères débloquées / 2). Les merveilles (y compris naturelles) ne fournissent plus de +1 Bonheur. L'effet peut être désactivé.",
   Zoroaster: "Zoroastre",
   Zugspitze: "Zugspitze",
   ZugspitzeDesc: "Pour chaque ère débloquée, obtenez un point qui peut être utilisé pour offrir un niveau supplémentaire à tout personnage illustre né lors de cette partie.",
};
