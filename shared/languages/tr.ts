export const TR = {
   About: "CivIdle Hakkında",
   AbuSimbel: "Abu Simbel",
   AbuSimbelDesc: "Ramesses II'nin etkisini iki kat artırır. Tüm Bitişik Harikalara +1 Mutluluk eklenir",
   AccountActiveTrade: "Aktif Ticaret",
   AccountChatBadge: "Sohbet Rozeti",
   AccountCustomColor: "Custom Color",
   AccountCustomColorDefault: "Default",
   AccountGreatPeopleLevelRequirement: "Required Great People Level",
   AccountLevel: "Hesap Derecesi",
   AccountLevelAedile: "Aedile",
   AccountLevelConsul: "Consul",
   AccountLevelMod: "Moderator",
   AccountLevelPlayTime: "Oynama Süresi > %{requiredTime} (Your play time is %{actualTime})",
   AccountLevelPraetor: "Praetor",
   AccountLevelQuaestor: "Quaestor",
   AccountLevelSupporterPack: "Owns Supporter Pack",
   AccountLevelTribune: "Tribune",
   AccountLevelUpgradeConditionAnyHTML: "Hesabınızı yükseltmek için, sadece <b>aşağıdaki kriterlerden birini</b> karşılamanız yeterlidir:",
   AccountPlayTimeRequirement: "Required Play Time",
   AccountRankUp: "Upgrade Account Rank",
   AccountRankUpDesc: "All your progress will be carried over to your new rank",
   AccountRankUpTip: "Congratulations, your account is eligible for a higher rank - click here to upgrade!",
   AccountSupporter: "Supporter Pack Owner",
   AccountTradePriceRange: "Ticaret Fiyat Aralığı",
   AccountTradeTileReservationTime: "Ticaret Karosu Rezervasyonu",
   AccountTradeTileReservationTimeDesc: "Bu, son çevrimiçi olduğunuzdan bu yana takas karonuzun sizin için ayrılacağı zamandır. Rezervasyon süresi sona erdikten sonra karonuz diğer oyuncuların kullanımına açık olacak",
   AccountTradeValuePerMinute: "Dakika Başına Ticaret Değeri",
   AccountTypeShowDetails: "Hesap Detaylarını Göster",
   AccountUpgradeButton: "Quaestor Derecesine Yükselt",
   AccountUpgradeConfirm: "Hesap Yükselt",
   AccountUpgradeConfirmDescV2: "Hesabınızı yükseltmek <b>mevcut oyununuzu sıfırlayacak</b> ve izin verilen seviyelerdeki kalıcı büyük kişileri aktaracaktır. Bu işlem <b>geri alınamaz</b> devam etmek istediğinizden emin misiniz?",
   Acknowledge: "Acknowledge",
   Acropolis: "Akropolis",
   ActorsGuild: "Aktörler Loncası",
   AdaLovelace: "Ada Lovelace",
   AdamSmith: "Adam Smith",
   AdjustBuildingCapacity: "Üretim Kapasitesi",
   AdvisorElectricityContent:
      "Power Plants provide two new systems to you. The first, 'Power' is indicated by the lightning bolt tiles adjacent to the power plant. Some buildings (starting with Radio in World Wars) have a 'requires power' indicator in their list of inputs. <b>This means they must be built on a lightning bolt tile to function</b>. Buildings that require power and have it, will also transmit power to the tiles adjacent to that building, so you can power them from each other as long as at least one is touching a power plant.<br><br>The other system 'electrification' can be applied to <b>any building anywhere</b> on the map as long as it doesn't produce science or workers. This uses up the power generated by the power plant to increase both the consumption and production of the building. More levels of electrification require larger and larger amounts of power. Electrifying buildings that also have 'requires power' is more efficient than electrifying the ones that don't.",
   AdvisorElectricityTitle: "Power and Electrification",
   AdvisorGreatPeopleContent:
      "Each time you enter a new age of technology, you will be able to select a Great Person from that age, and each previous age. These Great People give global bonuses that can increase production, science, happiness, and many other things.<br><br>These bonuses are permanent for the rest of the rebirth. When you rebirth, all of your Great People become permanent, and their bonus lasts forever.<br><br>Picking the same one in a later run will stack your permanent and in-run bonus, and when you rebirth with duplicates, the extras are stored and can be used to upgrade the permanent bonus. That is accessed in the <b>Manage Permanent Great People</b> menu in your Home Building.",
   AdvisorGreatPeopleTitle: "Great People",
   AdvisorHappinessContent:
      "Happiness is the core mechanic in CivIdle that limits expansion. You gain happiness by unlocking new technology, advancing to new ages, building wonders, from Great People who provide it, and a few other ways you can discover as you learn. <b>Each new building costs 1 happiness</b>. For each point above/below 0 happiness, you get a 2% bonus or penalty to your total workers (Capping at -50 and +50 Happiness). You can see a detailed breakdown of your happiness in your <b>Home Building's Happiness section</b>.",
   AdvisorHappinessTitle: "Keep Your People Happy",
   AdvisorOkay: "Got it, thanks!",
   AdvisorScienceContent:
      "Your busy workers generate science, which allows you to unlock new technology and advance your civilization. You can access the research menu a number of ways. By clicking on the science meter, by accessing your unlockable technologies in your Home Building, or by using the 'View' menu. These will all bring you to the tech tree, showing you all the technologies, as well as how much science is required for each. If you have enough science to learn a new technology, simply click on it and press 'unlock' in the sidebar menu. <b>Each new tier and age of technology requires more and more science, but you will unlock new and better ways to gain science as well.</b>",
   AdvisorScienceTitle: "Scientific Discovery!",
   AdvisorSkipAllTutorials: "Skip All Tutorials",
   AdvisorStorageContent:
      "While buildings have a decent amount of storage, they can fill up especially if left idle for a long time. <b>When buildings are full, they can no longer produce</b>. This isn't always an issue, since you clearly have a large stockpile since the building is full. But keeping things producing is generally better.<br><br>One way to address full storage is via a warehouse. When you build a warehouse, you get a menu of every product you've discovered, and you can set the warehouse to pull any products in any amounts as long as the total for all products is within what the warehouse can pull based on its level and storage multiplier.<br><br>An easy way to set up a warehouse is to check off each product you want to import into the warehouse, and use the 'redistribute among selected' buttons to split your import rate and storage equally. If you want buildings to also be able to pull out of the warehouse, make sure to turn on the 'export below max amount' option as well.",
   AdvisorStorageTitle: "Storage and Warehouses",
   AdvisorTraditionContent:
      "Some wonders (Chogha Zanbil, Luxor Temple, Big Ben) provide access to a new set of options, allowing you to customize the path of your rebirth. Each one allows you to choose from 1 of 4 options for your civilization's tradtion, religion and ideology respectively.<br><br>Once you choose one, that choice is locked in for that rebirth, though you can pick others in future rebirths. Once chosen, each one can also be upgraded a number of times by providing the necessary resources. The rewards in each tier are cumulative, so Tier 1 giving +1 production to X and Tier 2 giving +1 production to X means at Tier 2 you will have +2 production to X in total.",
   AdvisorTraditionTitle: "Choosing Paths and Upgradeable Wonders",
   AdvisorWonderContent:
      "Wonders are special buildings that provide global effects which can have a significant impact on your gameplay. In addition to their listed functions, all Wonders give +1 Happiness as well. You need to be careful though, as <b>Wonders require a LOT of materials, and have a higher than normal Builder Capacity as well</b>. This means that they can easily clear out your stockpiles of needed inputs, leaving your other buildings starving. <b>You can turn each input on and off freely</b>, allowing you to build it in stages while you stockpile enough materials to keep everything running.",
   AdvisorWonderTitle: "Wonders Of The World",
   AdvisorWorkerContent:
      "Every time a building produces or transports goods, this requires workers. If you don't have enough workers available, some buildings will fail to run that cycle. The obvious fix for this is to increase your total available workers by building or upgrading structures that make workers (Hut/House/Apartment/Condo).<br><br><b>Be aware though, that buildings turn off while upgrading, and can't provide any of their resources, which includes workers, so you might want to only upgrade one housing building at a time.</b> A good goal for the early stages of the game is to keep aboput 70% of your workers busy. If more than 70% are busy, upgrade/build housing. If fewer than 70% are busy, expand production.",
   AdvisorWorkerTitle: "Worker Management",
   Aeschylus: "Eshilos",
   Agamemnon: "Agamemnon",
   AgeWisdom: "Age Wisdom",
   AgeWisdomDescHTML: "Each level of Age Wisdom provides <b>an equivalent level</b> of eligible Permanent Great People of that age - it can be upgraded with eligible Permanent Great People shards",
   AgeWisdomGreatPeopleShardsNeeded: "You need %{amount} more great people shards for the next Age Wisdom upgrade",
   AgeWisdomGreatPeopleShardsSatisfied: "You have enough great people shards for the next Age Wisdom upgrade",
   AgeWisdomNeedMoreGreatPeopleShards: "Need More Great People Shards",
   AgeWisdomNotEligible: "This Great Person is not eligible for Age Wisdom",
   AgeWisdomSource: "%{age} Wisdom: %{person}",
   AgeWisdomUpgradeWarningHTMLV3: "Age Wisdom <b>does not carry over</b> when upgrading from Tribune to Quaestor",
   AGreatPersonIsBorn: "Bir Büyük Kişi Doğdu",
   AircraftCarrier: "Aircraft Carrier",
   AircraftCarrierYard: "Aircraft Carrier Yard",
   Airplane: "Airplane",
   AirplaneFactory: "Airplane Factory",
   Akitu: "Akitu: Ziggurat Of Ur and Euphrates River apply to buildings unlocked in the current age",
   AlanTuring: "Alan Turing",
   AlanTuringDesc: "Çalışmayan işçilerden +%{value} bilim",
   AlbertEinstein: "Albert Einstein",
   Alcohol: "Alkol",
   AldersonDisk: "Alderson Disk",
   AldersonDiskDesc: "+25 Happiness. This wonder can be upgraded and each additional upgrade provides +5 Happiness",
   Alloy: "Alaşım",
   Alps: "Alpler",
   AlpsDesc: "Binalara her 10 seviyede +1 Üretim Kapasitesi eklenir. (+1 Tüketim Çarpanı, +1 Üretim Çarpanı)",
   Aluminum: "Alüminyum",
   AluminumSmelter: "Alüminyum Eritme Tesisi",
   AmeliaEarhart: "Amelia Earhart",
   American: "American",
   AndrewCarnegie: "Andrew Carnegie",
   AngkorWat: "Angkor Wat",
   AngkorWatDesc: "Bitişiğindeki tüm binalar +1 İşçi Kapasite Çarpanı alır. 1000 İşçi sağlar",
   AntiCheatFailure: "<b>Hile Karşıtı Kontolü Geçememeniz</b> sebebi ile hesap seviyeniz kısıtlandı. Buna itiraz etmek istiyorsanız geliştiriciyle iletişime geçin",
   AoiMatsuri: "Aoi Matsuri: Mount Fuji generates double the warp",
   Apartment: "Apartman",
   Aphrodite: "Afrodit",
   AphroditeDescV2: "+1 Builder Capacity Multiplier for each level when upgrading buildings over Level 20. All unlocked Classical Age permanent great people get +1 level this run",
   ApolloProgram: "Apollo Program",
   ApolloProgramDesc: "All rocket factories get +2 Production, Worker Capacity and Storage Multiplier. Satellite factories, spaceship factories and nuclear missile silos get +1 Production Multiplier for each adjacent rocket factory",
   ApplyToAll: "Hepsi İçin Yap",
   ApplyToAllBuilding: "Tüm %{building} için Yap",
   ApplyToBuildingInTile: "Tüm %{tile} karo içindeki %{building} için yap",
   ApplyToBuildingsToastHTML: "Başarıyla uygulandı <b>%{count} %{building}</b>",
   Aqueduct: "Su Kemeri",
   ArcDeTriomphe: "Zafer Takı",
   ArcDeTriompheDescV2: "Every 1 happiness (capped) provides +1 builder capacity to all buildings",
   Archimedes: "Arşimet",
   Architecture: "Mimarlık",
   Aristophanes: "Aristofanes",
   AristophanesDesc: "+%{value} Mutluluk",
   Aristotle: "Aristotle",
   Arithmetic: "Aritmetik",
   Armor: "Zırh",
   Armory: "Zırh Atölyesi",
   ArtificialIntelligence: "Yapay Zeka",
   Artillery: "Topçu",
   ArtilleryFactory: "Topçu Fabrikası",
   AshokaTheGreat: "Ashoka the Great",
   Ashurbanipal: "Asurbanipal",
   Assembly: "Montaj",
   Astronomy: "Astronomi",
   AtomicBomb: "Atom Bombaso",
   AtomicFacility: "Atom Tesisi",
   AtomicTheory: "Atom Teorisi",
   Atomium: "Atomium",
   AtomiumDescV2: "All buildings that produce science within 2 tile range get +5 Production Multiplier. Generate science that is equal to the science production within 2 tile range. When completed, generate one-time science equivalent to the cost of the most expensive unlocked technology",
   Autocracy: "Otokrasi",
   Aviation: "Havacılık",
   Babylonian: "Babylonian",
   BackToCity: "Şehre Geri Dön",
   BackupRecovery: "Yedek Geri Yükleme",
   Bakery: "Fırın",
   Ballistics: "Balistik",
   Bank: "Banka",
   Banking: "Bankacılık",
   BankingAdditionalUpgrade: "10 Seviye ve üzeri tüm binalara +1 Depolama Çarpanı eklenir",
   Banknote: "Banknot",
   BaseCapacity: "Base Capacity",
   BaseConsumption: "Temel Tüketim",
   BaseMultiplier: "Temel Çarpan",
   BaseProduction: "Temel Üretim",
   BastilleDay: "Bastille Day: Double the effect of Centre Pompidou and Arc de Triomphe. Double the Culture generation from Mont Saint-Michel",
   BatchModeTooltip: "%{count} bina seçili. Geliştirme tüm seçili binalara yapılacak",
   BatchSelectAllSameType: "Aynı Tipten",
   BatchSelectAnyType1Tile: "1 Karo içerisinde herhangi tipten",
   BatchSelectAnyType2Tile: "2 Karo içerisinde herhangi tipten",
   BatchSelectAnyType3Tile: "3 Karo içerisinde herhangi tipten",
   BatchSelectSameType1Tile: "1 Karo içerisinde Aynı Tipten",
   BatchSelectSameType2Tile: "2 Karo içerisinde Aynı Tipten",
   BatchSelectSameType3Tile: "3 Karo içerisinde Aynı Tipten",
   BatchSelectSameTypeSameLevel: "Same Type Same Level",
   BatchSelectThisBuilding: "Bu bina",
   BatchStateSelectActive: "Active",
   BatchStateSelectAll: "All",
   BatchStateSelectTurnedFullStorage: "Full Storage",
   BatchStateSelectTurnedOff: "Turned Off",
   BatchUpgrade: "Toplu Yükseltme",
   Battleship: "Savaş gemisi",
   BattleshipBuilder: "Savaş gemisi inşaatçısı",
   BigBen: "Big Ben",
   BigBenDesc: "+2 Science From Busy Workers. Choose an empire ideology, unlock more boost with each choice",
   Biplane: "Çift kanatlı uçak",
   BiplaneFactory: "Çift kanatlı uçak Fabrikası",
   Bitcoin: "Bitcoin",
   BitcoinMiner: "Bitcoin Miner",
   BlackForest: "Black Forest",
   BlackForestDesc: "When discovered, reveals all wood tiles on the map. Spawn wood on adjacent tiles. All buildings that consume Wood or Lumber get +5 Production Multiplier",
   Blacksmith: "Demirci",
   Blockchain: "Blockchain",
   BlueMosque: "Blue Mosque",
   BlueMosqueDesc: "All wonders provide +1 Production, Worker Capacity and Storage Multiplier to adjacent buildings. When constructed next to Hagia Sophia, provide extra +1 Production, Worker Capacity and Storage Multiplier",
   BobHope: "Bob Hope",
   BobHopeDesc: "+%{value} Happiness",
   Bond: "Tahvil",
   BondMarket: "Tahvil Piyasası",
   Book: "Kitap",
   BoostCyclesLeft: "Boost Cycles Left",
   BoostDescription: "+%{value} %{multipliers} için: %{buildings}",
   Borobudur: "Borobudur",
   BorobudurDesc: "Borobudur",
   BranCastle: "Bran Kalesi",
   BranCastleDesc: "Bran Kalesi",
   BrandenburgGate: "Brandenburg Kapısı",
   BrandenburgGateDesc: "Tüm kömür madenlerine ve petrol kuyularına +1 Üretim, Depolama ve İşçi Kapasite Çarpanı eklenir. Oil refineries get +1 Production, Storage and Worker Capacity Multiplier for each adjacent oil tile",
   Bread: "Ekmek",
   Brewery: "Brewery",
   Brick: "Tuğla",
   Brickworks: "Tuğla Fabrikası",
   BritishMuseum: "British Museum",
   BritishMuseumChooseWonder: "Choose a Wonder",
   BritishMuseumDesc: "After constructed, can transform into to a unique wonder from other civilizations",
   BritishMuseumTransform: "Transform",
   Broadway: "Broadway",
   BroadwayCurrentlySelected: "Currently selected",
   BroadwayDesc: "A great person of the current age and a great person of the previous age are born. Select a great person and double his/her effect",
   BronzeAge: "Bronz Çağı",
   BronzeTech: "Bronz",
   BuddhismLevelX: "Buddhism %{level}",
   Build: "İnşa et",
   BuilderCapacity: "İnşaatçı Kapasitesi",
   BuildingColor: "Bina Rengi",
   BuildingColorMatchBuilding: "Binanın Rengini Kopyala",
   BuildingColorMatchBuildingTooltip: "Bu kaynağı üreten binadan kaynağın rengini kopyala. Eğer birden fazla bina bu kaynağı üretiyorsa, rastgele bir tanesi seçilecek",
   BuildingDefaults: "Bina Varsayılanları",
   BuildingDefaultsCount: "%{count} özellikler bina varsayılanında geçersiz kılındı",
   BuildingDefaultsRemove: "Üzerine yazılan özellikleri temizle",
   BuildingEmpireValue: "Building Empire Value / Resource Empire Value",
   BuildingMultipliers: "Güçlendirme",
   BuildingName: "İsim",
   BuildingNoMultiplier: "%{building}  herhangi bir çarpandan <b>etkilenmez</b> (Üretim, İşçi kapasitesi, Depolama, vb)",
   BuildingSearchText: "Aramak için bir bina adı yazın",
   BuildingTier: "Aşama",
   Cable: "Kablo",
   CableFactory: "Kablo Fabrikası",
   Calendar: "Takvim",
   CambridgeUniversity: "Cambridge University",
   CambridgeUniversityDesc: "+1 Age Wisdom level for Renaissance and ages after",
   CambridgeUniversitySource: "Cambridge University (%{age})",
   Cancel: "İptal Et",
   CancelAllUpgradeDesc: "Cancel all %{building} upgrades",
   CancelUpgrade: "Yükseltmeyi İptal Et",
   CancelUpgradeDesc: "Halihazırda taşınmış olan tüm kaynaklar depoda kalacaktır",
   Cannon: "Top",
   CannonWorkshop: "Top Atölyesi",
   CannotEarnPermanentGreatPeopleDesc: "Because this is a trial run, permanent great people cannot be earned",
   Capitalism: "Kapitalizm",
   Cappadocia: "Cappadocia",
   CappadociaDesc: "All buildings within 3 tile range get +1 Production, Worker Capacity and Storage Multiplier for every level above Level 30",
   Car: "Otomobil",
   Caravansary: "Kervansaray",
   CaravansaryDesc: "Diğer oyuncularla kaynak ticareti yapın ve ekstra depolama alanı sağlayın",
   Caravel: "Karavel",
   CaravelBuilder: "Karavel Tersanesi",
   CarFactory: "Otomobil Fabrikası",
   CarlFriedrichGauss: "Carl Friedrich Gauss",
   CarlFriedrichGaussDesc: "+%{idle} Science from Idle Workers. +%{busy} Science from Busy Workers",
   CarlSagan: "Carl Sagan",
   Census: "Nüfus Sayımı",
   CentrePompidou: "Centre Pompidou",
   CentrePompidouDesc:
      "Once constructed, all buildings get +1 Production and +2 Storage Multiplier. The wonder will persist if the current run reaches Information Age and the next run is a different civilization. The wonder gets +1 level at rebirth for each run that reaches Information Age with a unique civilization. Each level provides +1 Production and +2 Storage Multiplier. The value of this wonder is excluded from total empire value and British Museum cannot transform into this wonder",
   CentrePompidouWarningHTML: "Centre Pompidou will disappear if you rebirth as <b>%{civ}</b>",
   CerneAbbasGiant: "Cerne Abbas Giant",
   CerneAbbasGiantDesc: "A great person of the current age is born when a wonder is constructed",
   ChangePlayerHandle: "Değiştir",
   ChangePlayerHandleCancel: "İptal Et",
   ChangePlayerHandledDesc: "Your player handle can only contain 5 ~ 16 alphabets and numbers and has to be unique",
   Chariot: "Atlı Savaş Arabası",
   ChariotWorkshop: "Atlı Savaş Arabası Atölyesi",
   Charlemagne: "Şarlman",
   CharlesDarwin: "Charles Darwin",
   CharlesDarwinDesc: "+%{value} Meşgul Çalışanlardan Bilim",
   CharlesMartinHall: "Charles Martin Hall",
   CharlesParsons: "Charles Parsons",
   CharlieChaplin: "Charlie Chaplin",
   CharlieChaplinDesc: "+%{value} Happiness",
   Chat: "Sohbet",
   ChatChannel: "Sohbet Kanalı",
   ChatChannelLanguage: "Dil",
   ChatHideLatestMessage: "En Son Mesaj İçeriğini Gizle",
   ChatNoMessage: "Sohbet Mesajı Yok",
   ChatReconnect: "Bağlantı kesildi, yeniden bağlanılıyor...",
   ChatSend: "Gönder",
   CheckInAndExit: "Check In And Exit",
   CheckInCloudSave: "Check In Save",
   CheckOutCloudSave: "Check Out Save",
   Cheese: "Peynir",
   CheeseMaker: "Peynir Üreticisi",
   Chemistry: "Kimya",
   ChesterWNimitz: "Chester W. Nimitz",
   ChichenItza: "Chichen Itza",
   ChichenItzaDesc: "Bitişiğindeki tüm binalara +1 Üretim Çarpanı, Depolama ve İşçi Kapasite Çarpanı eklenir",
   Chinese: "Chinese",
   ChoghaZanbil: "Çoğa Zenbil",
   ChoghaZanbilDescV2: "Choose an empire tradition, unlock more boost with each choice",
   ChooseGreatPersonChoicesLeft: "%{count} seçim hakkın kaldı",
   ChristianityLevelX: "Christianity %{level}",
   Church: "Kilise",
   CircusMaximus: "Circus Maximus",
   CircusMaximusDescV2: "+5 Mutluluk ekler. Müzisyen Loncalarına, Yazar Loncalarına ve Ressam Loncalarına +1 Üretim ve Depolama Çarpanı eklenir",
   CityState: "Şehir Devleti",
   CityViewMap: "Şehir",
   CivGPT: "CivGPT",
   CivIdle: "CivIdle",
   CivIdleInfo: "Fish Pond Studio gururla sunar",
   Civilization: "Civilization",
   CivilService: "Kamu Hizmeti",
   CivOasis: "CivOasis",
   CivTok: "CivTok",
   ClaimedGreatPeople: "Alınan Büyük Kişi",
   ClaimedGreatPeopleTooltip: "You have %{total} great people at rebirth, %{claimed} of them are already claimed",
   ClassicalAge: "Klasik Çağ",
   ClearAfterUpdate: "Market Güncellemesinden Sonra Tüm İşlemleri Temizle",
   ClearSelected: "Seçileni Temizle",
   ClearSelection: "Temizle",
   ClearTransportPlanCache: "Clear Transport Plan Cache",
   Cleopatra: "Cleopatra",
   CloneFactory: "Clone Factory",
   CloneFactoryDesc: "Clone any resources",
   CloneFactoryInputDescHTML: "Clone Factory can only clone <b>%{res}</b> directly transported from <b>%{buildings}</b>",
   CloneLab: "Clone Lab",
   CloneLabDesc: "Convert any resources into Science",
   CloneLabScienceMultiplierHTML: "Production multipliers that <b>only apply to science production buildings</b> (e.g. production multipliers from Atomium) <b>do not apply</b> to Clone Lab",
   Cloth: "Kumaş",
   CloudComputing: "Bulut İşlemleri",
   CloudSaveRefresh: "Refresh",
   CloudSaveReturnToGame: "Return To Game",
   CNTower: "CN Tower",
   CNTowerDesc: "All movie studios, radio stations and TV stations are exempt from -1 Happiness. All buildings unlocked in World Wars and Cold War get +N Production, Worker Capacity and Storage Multiplier. N = Difference between the tier and the age of the building",
   Coal: "Kömür",
   CoalMine: "Kömür Madeni",
   CoalPowerPlant: "Kömür Enerji Santrali",
   Coin: "Madeni Para",
   CoinMint: "Madeni Para Darphanesi",
   ColdWarAge: "Soğuk Savaş",
   CologneCathedral: "Cologne Cathedral",
   CologneCathedralDesc:
      "When constructed, generate one-time science equivalent to the cost of the most expensive technology in the current age. All buildings that produce science (excluding Clone Lab) get +1 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to all buildings that produce science (excluding Clone Lab)",
   Colonialism: "Sömürgecilik",
   Colosseum: "Kolezyum",
   ColosseumDescV2: "Chariot Workshops are exempt from -1 happiness. Consumes 10 chariots and produce 10 happiness. Each unlocked age gives 2 extra happiness",
   ColossusOfRhodes: "Rodos Heykeli",
   ColossusOfRhodesDesc: "İşçi üretmeyen tüm bitişik binalara +1 Mutluluk verir",
   Combustion: "Yanma",
   Commerce4UpgradeHTMLV2: "When unlocked, all <b>adjacent banks</b> get free upgrade to <b>level 30</b>",
   CommerceLevelX: "Commerce %{level}",
   Communism: "Communism",
   CommunismLevel4DescHTML: "A great person of <b>Industrial Age</b> and a great person of <b>World Wars Age</b> are born",
   CommunismLevel5DescHTML: "A great person of <b>Cold War Age</b> is born. When entering a new age, get <b>2 additional</b> great people of that age",
   CommunismLevelX: "Communism Level %{level}",
   Computer: "Bilgisayar",
   ComputerFactory: "Computer Factory",
   ComputerLab: "Computer Lab",
   Concrete: "Beton",
   ConcretePlant: "Çimento Fabrikası",
   Condo: "Condo",
   ConfirmDestroyResourceContent: " %{amount} %{resource} yok etmek üzeresin. Bu işlem geri alınamaz",
   ConfirmNo: "Hayır",
   ConfirmYes: "Evet",
   Confucius: "Konfüçyüs",
   ConfuciusDescV2: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   ConnectToADevice: "Connect To A Device",
   Conservatism: "Conservatism",
   ConservatismLevelX: "Conservatism Level %{level}",
   Constitution: "Anayasa",
   Construction: "İnşaat",
   ConstructionBuilderBaseCapacity: "Temel Kapasite",
   ConstructionBuilderCapacity: "İnşaatçı Kapasitesi",
   ConstructionBuilderMultiplier: "Kapasite Çarpanı",
   ConstructionBuilderMultiplierFull: "İnşaat Kapasitesi Çarpanı",
   ConstructionCost: "İnşaat Maliyeti: %{cost}",
   ConstructionDelivered: "Delivered",
   ConstructionPriority: "İnşaat Önceliği",
   ConstructionProgress: "İlerleme",
   ConstructionResource: "Resource",
   Consume: "Tüketim",
   ConsumeResource: "Tüketim: %{resource}",
   ConsumptionMultiplier: "Tüketim Çarpanı",
   ContentInDevelopment: "Content In Development",
   ContentInDevelopmentDesc: "This game content is still in development and will be available in a future game update, stay tuned!",
   Copper: "Bakır",
   CopperMiningCamp: "Bakır Madeni Kampı",
   CosimoDeMedici: "Cosimo de' Medici",
   Cotton: "Pamuk",
   CottonMill: "Pamuk Fabrikası",
   CottonPlantation: "Pamuk Plantasyonu",
   Counting: "Sayma",
   Courthouse: "Adliye Sarayı",
   CristoRedentor: "Cristo Redentor",
   CristoRedentorDesc: "Cristo Redentor",
   CrossPlatformAccount: "Platform Account",
   CrossPlatformConnect: "Connect",
   CrossPlatformSave: "Cross Platform Save",
   CrossPlatformSaveLastCheckIn: "Last Check In",
   CrossPlatformSaveStatus: "Current Status",
   CrossPlatformSaveStatusCheckedIn: "Checked In",
   CrossPlatformSaveStatusCheckedOut: "Checked Out on %{platform}",
   CrossPlatformSaveStatusCheckedOutTooltip: "Your cross platform save has been checked out on another platform, you have to check in on that platform before you can check out on this platform",
   Cultivation4UpgradeHTML: "A great person of <b>Renaissance Age</b> is born",
   CultivationLevelX: "Cultivation %{level}",
   Culture: "Kültür",
   Culus: "Cülus: Double the effect of Cappadocia. Mount Ararat's effect becomes based on square root of Effective Great People Level, instead of cubic root",
   CurrentLanguage: "Türkçe",
   CurrentPlatform: "Current Platform",
   CursorBigOldFashioned: "3D (Big)",
   CursorOldFashioned: "3D",
   CursorStyle: "Cursor Style",
   CursorStyleDescHTML: "Change the style of the cursor. <b>Require restarting your game to take effect</b>",
   CursorSystem: "System",
   Cycle: "Cycle",
   CyrusII: "Cyrus II",
   DairyFarm: "Dairy Farm",
   DefaultBuildingLevel: "Default Builing Level",
   DefaultConstructionPriority: "Varsayılan İnşaat Önceliği",
   DefaultProductionPriority: "Varsayılan Üretim Önceliği",
   DefaultStockpileMax: "Varsayılan Maksimum Stok",
   DefaultStockpileSettings: "Varsayılan Stok Giriş Kapasitesi",
   DeficitResources: "Deficit Resources",
   Democracy: "Demokrasi",
   DemolishAllBuilding: "Demolish All %{building} Within %{tile} Tile",
   DemolishAllBuildingConfirmContent: "Are you sure about demolishing %{count} %{name}?",
   DemolishAllBuildingConfirmTitle: "Demolish %{count} Building(s)?",
   DemolishBuilding: "Demolish Building",
   DennisRitchie: "Dennis Ritchie",
   Deposit: "Kaynak",
   DepositTileCountDesc: "%{count} tile(s) of %{deposit} can be found in %{city}",
   Dido: "Dido",
   Diplomacy: "Diplomasi",
   DistanceInfinity: "Sınırsız",
   DistanceInTiles: "Distance (In Tiles)",
   DolmabahcePalace: "Dolmabahçe Palace",
   Drilling: "Sondaj",
   DukeOfZhou: "Zhou Dükü",
   DuneOfPilat: "Dune of Pilat",
   DuneOfPilatDesc: "In each age, double the age wisdom for the previous age",
   DynamicMultiplierTooltip: "This multiplier is dynamic - it will not affect workers and storage",
   Dynamite: "Dinamit",
   DynamiteWorkshop: "Dinamit Atölyesi",
   DysonSphere: "Dyson Sphere",
   DysonSphereDesc: "All buildings get +5 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to all buildings",
   EasterBunny: "Easter Bunny",
   EasterBunnyDesc: "Once constructed, 10% of the extra Great People at Rebirth from this run will carry forward to the next run and are born after building the Easter Bunny in the new run. This wonder can only be constructed during April",
   EastIndiaCompany: "East India Company",
   EastIndiaCompanyDescV2:
      "This wonder accumulates the total value of your completed player trade transactions. For every 2,000 trade value, all buildings adjacent to caravansaries get a +0.5 Production Multiplier for 1 cycle. This wonder can be upgraded and each upgrade provides an additional +0.5 Production Multiplier. A trade transaction counts when you either fulfill another player's trade request or when your own trade request is fulfilled. Multiple boosts stack by extending the duration",
   Education: "Eğitim",
   EffectiveGreatPeopleLevel: "Effective Great People Level",
   EffectiveGreatPeopleLevelDesc: "Effective great people level is the sum of all permanent great people level and age wisdom level. It measures the effect boost provided by great people and age wisdom",
   Egyptian: "Egyptian",
   EiffelTower: "Eyfel Kulesi",
   EiffelTowerDesc: "Tüm bitişik çelik fabrikalarına +N Üretim, Depolama ve İşçi Çarpanı verir. N = Bitişik çelik fabrikalarının sayısı",
   Elbphilharmonie: "Elbphilharmonie",
   ElbphilharmonieDesc: "All buildings within 3 tile range get +1 Production Multiplier for each adjacent working building that has different tier",
   Electricity: "Elektrik",
   Electrification: "Elektriklendirme",
   ElectrificationPowerRequired: "Güç Gerekli",
   ElectrificationStatusActive: "Aktif",
   ElectrificationStatusDesc: "Both buildings that require power and buildings that do not require power can be electrified. However, buildings that require power provides higher electrification efficiency",
   ElectrificationStatusNoPowerV2: "Not Enough Power",
   ElectrificationStatusNotActive: "Aktif Değil",
   ElectrificationStatusV2: "Electrification Status",
   ElectrificationUpgrade: "Elektrifikasyonun kilidini açın. Binaların üretimi artırmak için güç tüketmesine izin verin",
   Electrolysis: "Elektroliz",
   ElvisPresley: "Elvis Presley",
   ElyseePalace: "Élysée Palace",
   EmailDeveloper: "Email Developer",
   Embassy: "Elçilik",
   EmperorWuOfHan: "Çin imparatoru Wu of Han",
   EmpireValue: "Empire Value",
   EmpireValueByHour: "Empire Value By Hour",
   EmpireValueFromBuilding: "Empire Value from Building",
   EmpireValueFromBuildingsStat: "From Buildings",
   EmpireValueFromResources: "Kaynaklardan",
   EmpireValueFromResourcesStat: "From Resources",
   EmpireValueIncrease: "Empire Value Increase",
   EmptyTilePageBuildLastBuilding: "Son Binayı İnşa Et",
   EndConstruction: "İnşaatı Sonlandır",
   EndConstructionDescHTML: "İnşaatı sonlandırdığınızda, halihazırda kullanılmış olan tüm kaynaklar <b>iade edilmeyecektir</b>",
   Engine: "Motor",
   Engineering: "Mühendislik",
   English: "English",
   Enlightenment: "Aydınlanma",
   Enrichment: "Zenginleştirme",
   EnricoFermi: "Enrico Fermi",
   EstimatedTimeLeft: "Kalan Tahmini Süre",
   EuphratesRiver: "Euphrates River",
   EuphratesRiverDesc:
      "Every 10% of busy workers that in production (not transporting) provides +1 Production Multiplier to all buildings that do not produce workers (max = number of unlocked ages / 2). When the Hanging Garden is built next to it, the Hanging Garden gets +1 effect for each age after the Hanging Garden is unlocked. When discovered, spawn water on all adjacent tiles that do not have deposits",
   ExpansionLevelX: "Expansion %{level}",
   Exploration: "Keşif",
   Explorer: "Kaşif",
   ExplorerRangeUpgradeDesc: "Increase the explorer's range to %{range}",
   ExploreThisTile: "Bir Kaşif Gönderin",
   ExploreThisTileHTML: "Bir kaşif <b>bu karoyu ve komşu karoları</b> keşfedecektir. Kaşifler %{name} içinde oluşturulur. Geriye %{count} kaşifiniz kaldı",
   ExtraGreatPeople: "%{count} Ekstra Büyük Kişi",
   ExtraGreatPeopleAtReborn: "Rebirth'te Ekstra Büyük Kişiler",
   ExtraTileInfoType: "Extra Tile Info",
   ExtraTileInfoTypeDesc: "Choose what information is shown below each tile",
   ExtraTileInfoTypeEmpireValue: "Empire Value",
   ExtraTileInfoTypeNone: "None",
   ExtraTileInfoTypeStoragePercentage: "Storage Percentage",
   Faith: "İnanç",
   Farming: "Çiftçilik",
   FavoriteBuildingAdd: "Favorilere ekle",
   FavoriteBuildingEmptyToast: "Hiç favori binanız yok",
   FavoriteBuildingRemove: "Favorilerden Kaldır",
   FeatureRequireQuaestorOrAbove: "This feature requires Quaestor rank or above",
   Festival: "Festival",
   FestivalCycle: "Festival Cycle",
   FestivalTechTooltipV2: "Positive Happiness (max. 50) is converted into festival points. For every %{point} festival points, your empire enters a festival cycle, granting a significant map-specific boost. The festival on this map is %{desc}",
   FestivalTechV2: "Unlock festival - positive Happiness (max. 50) is converted into festival points. For every %{point} festival points, your empire enters a festival cycle, granting a significant map-specific boost",
   Feudalism: "Feodalizm",
   Fibonacci: "Fibonacci",
   FibonacciDescV2: "+%{idle} Boşta Çalışan İşçilerden Bilim. Meşgul İşçilerden +%{busy} Bilim. Fibonacci'nin kalıcı yükseltme maliyeti Fibonacci dizisini takip eder",
   FighterJet: "Fighter Jet",
   FighterJetPlant: "Fighter Jet Plant",
   FilterByAge: "Filter by Age",
   FinancialArbitrage: "Financial Arbitrage",
   FinancialLeverage: "Financial Leverage",
   Fire: "Ateş",
   Firearm: "Ateşli Silah",
   FirstTimeGuideNext: "Devam",
   FirstTimeTutorialWelcome: "Welcome to CivIdle",
   FirstTimeTutorialWelcome1HTML:
      "Welcome to CivIdle. In this game, you will run your own empire: <b>manage productions, unlock technologies, trade resources with other players, create great people and build world wonders</b>.<br><br>Drag your mouse to move around. Use the scroll wheel to zoom in or out. Click an empty tile to build new buildings, click a building to inspect it.<br><br>Certain buildings like Stone Quarry and Logging Camp need to be built on top of the resource tile. I recommend placing a Hut, which provides worker, next to the fog - the building will take some time to build. After the completion, it will reveal the fog nearby.",
   FirstTimeTutorialWelcome2HTML:
      "Buildings can be upgraded - it costs resources and takes time. When a buildings is being upgraded, <b>it will no longer produce</b>. This includes buildings that provide workers, <b>so never upgrade all your buildings at the same time!</b><br><br>As your empire grows, you will get more science and unlock new technologies. I will tell you more about it when we get there but you can go to View -> Research to take a quick look<br><br>",
   FirstTimeTutorialWelcome3HTML: "Now you know all the basics of the game, you can start building your empire. But before I let you go, you should <b>choose yourself a player handle</b> and say hi in the in-game chat. We have an amazingly helpful community: if you get lost, don't be afraid to ask!",
   Fish: "Balık",
   FishPond: "Balık Göleti",
   FlorenceNightingale: "Florence Nightingale",
   FlorenceNightingaleDesc: "+%{value} Mutluluk",
   Flour: "Un",
   FlourMill: "Un Değirmeni",
   FontSizeScale: "Yazı Tipi Boyutu Ölçeği",
   FontSizeScaleDescHTML: "Oyunun kullanıcı arayüzünün yazı tipi boyutu ölçeğini değiştirin. <b>Ölçeği 1x'ten daha büyük ayarlamak bazı kullanıcı arayüzü düzenlerini bozabilir</b>",
   ForbiddenCity: "Yasak Şehir",
   ForbiddenCityDesc: "Tüm Kağıt Üreticilerine, Yazar Loncalarına ve Basımevlerine +1 Üretim Çarpanı, İşçi Kapasite Çarpanı ve Depolama Çarpanı eklenir",
   Forex: "Forex",
   ForexMarket: "Forex Market",
   FrankLloydWright: "Frank Lloyd Wright",
   FrankLloydWrightDesc: "+%{value} Builder Capacity Multiplier",
   FrankWhittle: "Frank Whittle",
   FreeThisWeek: "Free This Week",
   FreeThisWeekDescHTMLV2: "<b>Every week</b>, one of the premium civilizations is free to play. This week's free civilization is <b>%{city}</b>",
   French: "French",
   Frigate: "Fırkateyn",
   FrigateBuilder: "Fırkateyn Tersanesi",
   Furniture: "Mobilya",
   FurnitureWorkshop: "Mobilya Atölyesi",
   Future: "Future",
   GabrielGarciaMarquez: "Gabriel García Márquez",
   GabrielGarciaMarquezDesc: "+%{value} Happiness",
   GalileoGalilei: "Galileo Galilei",
   GalileoGalileiDesc: "+%{value} Boşta Çalışan İşçilerden Bilim",
   Galleon: "Kalyon",
   GalleonBuilder: "Kalyon Tersanesi",
   Gameplay: "Oynanış",
   Garment: "Giysi",
   GarmentWorkshop: "Giysi Atölyesi",
   GasPipeline: "Gaz Boru Hattı",
   GasPowerPlant: "Gaz Enerji Santrali",
   GatlingGun: "Gatling Silahı",
   GatlingGunFactory: "Gatling Silah Fabrikası",
   Genetics: "Genetik",
   Geography: "Coğrafya",
   GeorgeCMarshall: "George C. Marshall",
   GeorgeWashington: "George Washington",
   GeorgiusAgricola: "Georgius Agricola",
   German: "German",
   Glass: "Cam",
   Glassworks: "Cam Fabrikası",
   GlobalBuildingDefault: "Global Builing Default",
   Globalization: "Küreselleşme",
   GoBack: "Geri Dön",
   Gold: "Altın",
   GoldenGateBridge: "Golden Gate  Köprüsü",
   GoldenGateBridgeDesc: "Golden Gate  Köprüsü",
   GoldenPavilion: "Golden Pavilion",
   GoldenPavilionDesc: "All buildings within 3 tile range get +1 Production Multiplier for each adjacent building that produces any of its consumed resources (excluding Clone Lab and Clone Factory and the building cannot be turned off)",
   GoldMiningCamp: "Altın Madeni Kampı",
   GordonMoore: "Gordon Moore",
   GrandBazaar: "Kapalıçarşı",
   GrandBazaarDesc: "Tüm pazarları tek bir yerden kontrol edin! Tüm bitişiğindeki binalara +5 Depolama Çarpanı verir",
   GrandBazaarFilters: "Filtre",
   GrandBazaarFilterWarningHTML: "Herhangi bir işlem gösterilmeden önce bir filtre seçmelisiniz",
   GrandBazaarFilterYouGet: "Al",
   GrandBazaarFilterYouPay: "Ver",
   GrandBazaarSeach: "Search",
   GrandBazaarSearchGet: "Get",
   GrandBazaarSearchPay: "Pay",
   GrandBazaarTabActive: "Aktif",
   GrandBazaarTabTrades: "Ticaret",
   GrandCanyon: "Grand Canyon",
   GrandCanyonDesc: "Buildings unlocked in the current age get +2 Production Multiplier. Double the effect of J.P. Morgan",
   GraphicsDriver: "Grafik Sürücüsü: %{driver}",
   GreatDagonPagoda: "Great Dagon Pagoda",
   GreatDagonPagodaDescV2: "All pagodas are exempt from -1 happiness. Generate science based on faith production of all pagodas",
   GreatMosqueOfSamarra: "Samarra Ulu Camii",
   GreatMosqueOfSamarraDescV2: "+1 bina görüş mesafesi. 5 rastgele keşfedilmemiş yatak karosunu ortaya çıkarın ve her birinin üzerine 10. seviye bir kaynak çıkarma binası inşa edin",
   GreatPeople: "Büyük Kişi",
   GreatPeopleEffect: "Effect",
   GreatPeopleFilter: "Type name or age to filter great people",
   GreatPeopleName: "Name",
   GreatPeoplePermanentColumn: "Permanent",
   GreatPeoplePermanentShort: "Permanent",
   GreatPeoplePickPerRoll: "Great People Pick Per Roll",
   GreatPeopleThisRun: "Bu Turdaki Büyük Kişiler",
   GreatPeopleThisRunColumn: "Bu Tur",
   GreatPeopleThisRunShort: "Bu Tur",
   GreatPersonLevelRequired: "Permanent Great People Level Required",
   GreatPersonLevelRequiredDescV2: "%{city} civilization requires %{required} permanent great people levels. You currently have %{current}",
   GreatPersonPromotionPromote: "Promote",
   GreatPersonThisRunEffectiveLevel: "You currently have %{count} %{person} from this run. An additional %{person} will have 1/%{effect} of the effect",
   GreatPersonWildCardBirth: "Birth",
   GreatSphinx: "Büyük Gize Sfenksi",
   GreatSphinxDesc: "2 karo içindeki tüm aşama II veya üzeri binalara +N Tüketim, Üretim Çarpanı verir. N = Aynı tipteki bitişik binalarının sayısı",
   GreatWall: "Çin Seddi",
   GreatWallDesc: "1 kare aralığındaki tüm binalar +N Üretim, İşçi Kapasitesi ve Depolama Çarpanı alır. N = mevcut çağ ile binanın kilidinin ilk açıldığı çağ arasındaki farklı çağların sayısı. Yasak Şehir'in yanına inşa edildiğinde menzil 2 karoya çıkar",
   GreedyTransport: "Construction/Upgrade Greedy Transport",
   GreedyTransportDescHTML: "This will make buildings keep transporting resources even if it has enough resources for the current upgrade, which can make upgrading multiple levels <b>faster</b> but end up transport <b>more resources than needed</b>",
   Greek: "Greek",
   GrottaAzzurra: "Mavi Mağara",
   GrottaAzzurraDescV2: "When discovered, all your Tier I buildings get +5 Level and +1 Production, Worker Capacity and Storage Multiplier",
   Gunpowder: "Barut",
   GunpowderMill: "Barut Fabrikası",
   GuyFawkesNightV2: "Guy Fawkes Night: East India Company provides double the Production Multiplier to buildings adjacent to caravansaries. Tower Bridge generates great people 20% faster",
   HagiaSophia: "Ayasofya",
   HagiaSophiaDescV2: "+5 Happiness. Buildings with 0% Production Capacity are exempt from -1 happiness. During the game bootstrap, provide extra happiness to avoid production halt",
   HallOfFame: "Hall of Fame",
   HallOfSupremeHarmony: "Hall of Supreme Harmony",
   Hammurabi: "Hammurabi",
   HangingGarden: "Babil'in Asma Bahçeleri",
   HangingGardenDesc: "+1 İnşaatçı Kapasite Çarpanı. Bitişik su kemerlerine +1 Üretim, Depolama ve İşçi Kapasitesi Çarpanı verir",
   Happiness: "Mutluluk",
   HappinessFromBuilding: "Binalardan (Harikalar hariç)",
   HappinessFromBuildingTypes: "İyi Stoklanmış Bina Tiplerinden",
   HappinessFromHighestTierBuilding: "En Yüksek Aşamadaki Çalışan Binalardan",
   HappinessFromUnlockedAge: "Kilidi Açılmış Çağlardan",
   HappinessFromUnlockedTech: "Kilidi Açılmış Tekn",
   HappinessFromWonders: "Harikalardan (Doğal dahil)",
   HappinessUncapped: "Mutluluk (Sınırlandırılmamış)",
   HarryMarkowitz: "Harry Markowitz",
   HarunAlRashid: "Harun Reşid",
   Hatshepsut: "Hatşepsut",
   HatshepsutTemple: "Hatşepsut Tapınağı",
   HatshepsutTempleDesc: "Tamamlandığında haritadaki tüm su karolarını ortaya çıkarın. Buğday çiftliklerine, bitişik her su karosu için +1 Üretim Çarpanı verir",
   Headquarter: "Merkez",
   HedgeFund: "Hedge Fund",
   HelpMenu: "Yardım",
   HenryFord: "Henry Ford",
   Herding: "Çobanlık",
   Herodotus: "Herodot",
   HighlightBuilding: "Highlight %{building}",
   HimejiCastle: "Himeji Kalesi",
   HimejiCastleDesc: "Tüm Karavel Tersanelerine, Kalyon Tersanelerine ve Fırkateyn Tersanelerine +1 Üretim Çarpanı, İşçi Kapasitesi Çarpanı ve Depolama Çarpanı verir",
   Hollywood: "Hollywood",
   HollywoodDesc: "+5 Happiness. +1 Happiness for each well-stocked building that consumes or produces culture within 2 tile range",
   HolyEmpire: "Kutsal İmparatorluk",
   Homer: "Homer",
   Honor4UpgradeHTML: "Double the effect of <b>Zheng He</b> (Great Person)",
   HonorLevelX: "Honor %{level}",
   Horse: "At",
   HorsebackRiding: "At Binme",
   House: "Ev",
   Housing: "Konut",
   Hut: "Kulübe",
   HydroDam: "Su barajı",
   Hydroelectricity: "Hidroelektrik",
   HymanGRickover: "Hyman G. Rickover",
   IdeologyDescHTML: "Choose from <b>Liberalism, Conservatism, Socialism or Communism</b> as your empire ideology. You <b>cannot switch ideology</b> after it is chosen. You can unlock more boost within each ideology",
   IMPei: "I. M. Pei",
   IMPeiDesc: "+%{value} Builder Capacity Multiplier",
   Imperialism: "Emperyalizm",
   ImperialPalace: "Imperial Palace",
   IndustrialAge: "Endüstriyel",
   InformationAge: "Bilgi Çağı",
   InputResourceForCloning: "Input Resource For Cloning",
   InternationalSpaceStation: "International Space Station",
   InternationalSpaceStationDesc: "All buildings get +5 Storage Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Storage Multiplier to all buildings",
   Internet: "İnternet",
   InternetServiceProvider: "Internet Service Provider",
   InverseSelection: "Tersine",
   Iron: "Demir",
   IronAge: "Demir Çağı",
   Ironclad: "Demir zırhlı savaş gemisi",
   IroncladBuilder: "Demir zırhlı savaş gemisi Tersanesi",
   IronForge: "Demir Ocağı",
   IronMiningCamp: "Demir Madeni Kampı",
   IronTech: "Demir",
   IsaacNewton: "Isaac Newton",
   IsaacNewtonDescV2: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   IsambardKingdomBrunel: "Isambard Kingdom Brunel",
   IsidoreOfMiletus: "Isidore of Miletus",
   IsidoreOfMiletusDesc: "+%{value} İnşaatçı Kapasite Çarpanı",
   Islam5UpgradeHTML: "When unlocked, generate one-time science equivalent to the cost of the most expensive <b>Industrial</b> technology",
   IslamLevelX: "Islam %{level}",
   ItsukushimaShrine: "Itsukuşima Tapınağı",
   ItsukushimaShrineDescV2: "When all technologies within an age are unlocked, generate one-time science equivalent to the cost of the cheapest technology in the next age",
   JamesWatson: "James Watson",
   JamesWatsonDesc: "+%{value} Science From Busy Workers",
   JamesWatt: "James Watt",
   Japanese: "Japanese",
   JetPropulsion: "Jet Propulsion",
   JohannesGutenberg: "Johannes Gutenberg",
   JohannesKepler: "Johannes Kepler",
   JohnCarmack: "John Carmack",
   JohnDRockefeller: "John D. Rockefeller",
   JohnMcCarthy: "John McCarthy",
   JohnVonNeumann: "John von Neumann",
   JohnVonNeumannDesc: "+%{value} Science From Busy Workers",
   JoinDiscord: "Discorda Katıl",
   JosephPulitzer: "Joseph Pulitzer",
   Journalism: "Gazetecilik",
   JPMorgan: "J.P. Morgan",
   JRobertOppenheimer: "J. Robert Oppenheimer",
   JuliusCaesar: "Julius Caesar",
   Justinian: "I. Justinianus",
   Kanagawa: "Kanagawa",
   KanagawaDesc: "All great people of the current age get an additional level for this run (excluding Zenobia)",
   KarlMarx: "Karl Marx",
   Knight: "Şövalye",
   KnightCamp: "Şövalye Kampı",
   Koti: "Koti",
   KotiInStorage: "Koti In Storage",
   KotiProduction: "Koti Production",
   LandTrade: "Arazi Ticareti",
   Language: "Dil",
   Lapland: "Lapland",
   LaplandDesc: "When discovered, reveal the whole map. All buildings within 2-tile range get +5 Production Multiplier. This natural wonder can only be discovered in December",
   LargeHadronCollider: "Large Hadron Collider",
   LargeHadronColliderDescV2: "All Information Age great people get +2 level for this run. This wonder can be upgraded and each additional upgrade provides +1 level to all Information Age great people for this run",
   Law: "Hukuk",
   Lens: "Lens",
   LensWorkshop: "Lens Atölyesi",
   LeonardoDaVinci: "Leonardo da Vinci",
   Level: "Seviye",
   LevelX: "Seviye %{level}",
   Liberalism: "Liberalism",
   LiberalismLevel3DescHTML: "Free transport <b>from</b> and <b>to</b> warehouses",
   LiberalismLevel5DescHTML: "<b>Double</b> the electrification effect",
   LiberalismLevelX: "Liberalism Level %{level}",
   Library: "Kütüphane",
   LighthouseOfAlexandria: "İskenderiye'nin Deniz Feneri",
   LighthouseOfAlexandriaDesc: "Bitişiğindeki tüm binalara +5 Depolama Çarpanı verir",
   LinusPauling: "Linus Pauling",
   LinusPaulingDesc: "+%{value} Science From Idle Workers",
   Literature: "Edebiyat",
   LiveData: "Live Value",
   LocomotiveFactory: "Lokomotif Fabrikası",
   Logging: "Oduncu",
   LoggingCamp: "Odun Kampı",
   LouisSullivan: "Louis Sullivan",
   LouisSullivanDesc: "+%{value} İnşaatçı Kapasite Çarpanı",
   Louvre: "Louvre",
   LouvreDesc: "For every 10 Extra Great People at Rebirth, one great person from all unlocked ages is born",
   Lumber: "Kereste",
   LumberMill: "Kereste Fabrikası",
   LunarNewYear: "Lunar New Year: Great Wall provides double the boost to buildings. Porcelain Tower provides +1 level to all great people from this run",
   LuxorTemple: "Luksor Tapınağı",
   LuxorTempleDescV2: "+1 Science From Busy Workers. Choose an empire religion, unlock more boost with each choice",
   Machinery: "Makine",
   Magazine: "Dergi",
   MagazinePublisher: "Dergi Yayıncısı",
   Maglev: "Maglev",
   MaglevFactory: "Maglev Factory",
   MahatmaGandhi: "Mahatma Gandhi",
   ManageAgeWisdom: "Manage Age Wisdom",
   ManagedImport: "Managed Import",
   ManagedImportDescV2: "This building will automatically import resources produced within %{range} tile range. Resource transports for this building cannot be manually changed. Max transport distance will be ignored",
   ManageGreatPeople: "Büyük Kişileri Yönet",
   ManagePermanentGreatPeople: "Kalıcı Büyük Kişileri Yönet",
   ManageSave: "Manage Save",
   ManageWonders: "Harikaları Yönet",
   Manhattan: "Manhattan",
   ManhattanProject: "Manhattan Projesi",
   ManhattanProjectDesc: "Manhattan Projesi",
   Marble: "Mermer",
   Marbleworks: "Mermer İşleri",
   MarcoPolo: "Marco Polo",
   MarieCurie: "Marie Curie",
   MarinaBaySands: "Marina Bay Sands",
   MarinaBaySandsDesc: "All buildings get +5 Worker Capacity Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Worker Capacity Multiplier to all buildings",
   Market: "Market",
   MarketDesc: "Bir kaynağı diğeriyle değiştirin, mevcut kaynaklar her saat güncellenir",
   MarketRefreshMessage: "Trades in %{count} markets has been refreshed",
   MarketSell: "Sat",
   MarketSettings: "Market Ayarı",
   MarketValueDesc: "%{value} ortalama fiyata kıyasla",
   MarketYouGet: "Al",
   MarketYouPay: "Ver",
   MartinLuther: "Martin Luther",
   MaryamMirzakhani: "Maryam Mirzakhani",
   MaryamMirzakhaniDesc: "+%{value} Science From Idle Workers",
   Masonry: "Duvarcılık",
   MatrioshkaBrain: "Matrioshka Brain",
   MatrioshkaBrainDescV2: "Allow Science to be counted when calculating empire value (5 Science = 1 Empire Value). +5 Science Per Busy and Idle Worker. This wonder can be upgraded and each additional upgrade provides +1 Science Per Busy and Idle Worker and +1 Production Multiplier for buildings that produce Science",
   MausoleumAtHalicarnassus: "Halikarnas Mozolesi",
   MausoleumAtHalicarnassusDescV2: "Transports from or to buildings within 2 tile range do not cost workers",
   MaxExplorers: "Max Kaşifler",
   MaxTransportDistance: "Maksimum Taşıma Mesafesi",
   Meat: "Et",
   Metallurgy: "Metalurji",
   Michelangelo: "Michelangelo",
   MiddleAge: "Orta Çağ",
   MilitaryTactics: "Military Tactics",
   Milk: "Süt",
   Moai: "Moai",
   MoaiDesc: "Moai",
   MobileOverride: "Mobile Override",
   MogaoCaves: "Mogao Mağaraları",
   MogaoCavesDescV3: "+1 happiness for every 10% of busy workers. All adjacent buildings that produce faith are exempt from -1 happiness",
   MonetarySystem: "Monetary System",
   MontSaintMichel: "Mont Saint-Michel",
   MontSaintMichelDesc: "Generate Culture from Idle Workers. Provide +1 Storage Multiplier to all buildings within 2-tile range. This wonder can be upgraded using the generated Culture and each level provides addtional +1 Storage Multiplier",
   Mosque: "Cami",
   MotionPicture: "Sinema Filmi",
   MountArarat: "Mount Ararat",
   MountAraratDesc: "All buildings within 2 tile range get +X Production, Worker Capacity and Storage Multiplier. X = cubic root of Effective Great People Level",
   MountFuji: "Mount Fuji",
   MountFujiDescV2: "When Petra is built next to it, Petra gets +8h Warp storage. When the game is running, generate 20 warp every minute in Petra (not accelerated by Petra itself, not generating when the game is offline)",
   MountSinai: "Sina Dağı",
   MountSinaiDesc: "Keşfedildiğinde, mevcut çağın büyük bir kişisi doğar. İnanç üreten tüm binalara +5 Depolama Çarpanı verir",
   MountTai: "Tai Dağı",
   MountTaiDesc: "All buildings that produce science get +1 Production Multiplier. Double the effect of Confucious (Great Person). When discovered, generate one-time science equivalent to the cost of the most expensive unlocked technology",
   MoveBuilding: "Move Building",
   MoveBuildingFail: "Selected tile is not valid",
   MoveBuildingNoTeleport: "You don't have enough teleport",
   MoveBuildingSelectTile: "Select An Tile...",
   MoveBuildingSelectTileToastHTML: "Select <b>an empty explored tile</b> on the map as the target",
   Movie: "Film",
   MovieStudio: "Film Stüdyosu",
   Museum: "Müze",
   Music: "Müzik",
   MusiciansGuild: "Müzisyen Loncası",
   MutualAssuredDestruction: "Mutual Assured Destruction",
   MutualFund: "Mutual Fund",
   Name: "İsim",
   Nanotechnology: "Nanoteknoloji",
   NapoleonBonaparte: "Napoleon Bonaparte",
   NaturalGas: "Doğal Gaz",
   NaturalGasWell: "Doğal Gaz Kuyusu",
   NaturalWonderName: "Doğal Harika: %{name}",
   NaturalWonders: "Doğal Harikalar",
   Navigation: "Navigasyon",
   NebuchadnezzarII: "II.Nebukadnezar",
   Neuschwanstein: "Neuschwanstein Şatosu",
   NeuschwansteinDesc: "Harikaları inşa ederken +10 İnşaatçı Kapasite Çarpanı",
   Newspaper: "Gazete",
   NextExplorersIn: "Next Explorers In",
   NextMarketUpdateIn: "Next Market Update In",
   NiagaraFalls: "Niagara Falls",
   NiagaraFallsDescV2: "All warehouses, markets and caravansaries get +N storage multiplier. N = number of unlocked ages. Albert Einstein provides +1 Production Multiplier to Research Fund (not affected by other boosts like Broadway)",
   NielsBohr: "Niels Bohr",
   NielsBohrDescV2: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   NileRiver: "Nil Nehri",
   NileRiverDesc: "Hatşepsut'un etkisini iki katına çıkarın. Tüm buğday çiftliklerine +1 Üretim ve Depolama Çarpanı verir. Tüm komşu buğday çiftliklerine +5 Üretim ve Depolama Çarpanı verir",
   NoPowerRequired: "This building does not require power",
   NothingHere: "Nothing here",
   NotProducingBuildings: "Üretim Yapmayan Binalar",
   NuclearFission: "Nükleer Fisyon",
   NuclearFuelRod: "Nükleer Yakıt Çubuğu",
   NuclearMissile: "Nuclear Missile",
   NuclearMissileSilo: "Nuclear Missile Silo",
   NuclearPowerPlant: "Nuclear Power Plant",
   NuclearReactor: "Nuclear Reactor",
   NuclearSubmarine: "Nuclear Submarine",
   NuclearSubmarineYard: "Nuclear Submarine Yard",
   OdaNobunaga: "Oda Nobunaga",
   OfflineErrorMessage: "Şu anda çevrimdışısınız, bu işlem için İnternet bağlantısı gerekir",
   OfflineProduction: "Çevrimdışı Üretim",
   OfflineProductionTime: "Offline Production Time",
   OfflineProductionTimeDescHTML: "For the <b>first %{time} offline time</b>, you can choose either offline production or time warp - you can set the split here. The <b>rest of the offline time</b> can only be converted to time warp",
   OfflineTime: "Çevrimdışı Zaman",
   Oil: "Petrol",
   OilPress: "Petrol Presi",
   OilRefinery: "Petrol Rafinerisi",
   OilWell: "Petrol Kuyusu",
   Ok: "OK",
   Oktoberfest: "Oktoberfest: Double the effect of Zugspitze",
   Olive: "Zeytin",
   OlivePlantation: "Zeytin Plantasyonu",
   Olympics: "Olimpiyatlar",
   OnlyAvailableWhenPlaying: "Only available when playing %{city}",
   OpenLogFolder: "Open Log Folder",
   OpenSaveBackupFolder: "Open Backup Folder",
   OpenSaveFolder: "Open Save Folder",
   Opera: "Opera",
   OperationNotAllowedError: "Bu işleme izin verilmez",
   Opet: "Opet: Great Sphinx no longer increases Consumption Multiplier",
   OpticalFiber: "OpticalFiber",
   OpticalFiberPlant: "Optical Fiber Factory",
   Optics: "Optik",
   OptionsMenu: "Ayarlar",
   OptionsUseModernUIV2: "Use Anti-Aliased Font",
   OsakaCastle: "Osaka Castle",
   OsakaCastleDesc: "Provide power to all tiles within 2 tile range. Allow electrification of science producing buildings (including Clone Lab)",
   OtherPlatform: "Other Platform",
   Ottoman: "Ottoman",
   OttoVonBismarck: "Otto von Bismarck",
   OxfordUniversity: "Oxford University",
   OxfordUniversityDescV3: "Bilim üreten binalar için +%10 bilim çıktısı. Tamamlandığında, kilidi açılmış en pahalı teknolojinin maliyetine eşdeğer tek seferlik bilim üretir",
   PabloPicasso: "Pablo Picasso",
   Pagoda: "Pagoda",
   PaintersGuild: "Ressamlar Loncası",
   Painting: "Boyama",
   PalmJumeirah: "Palm Jumeirah",
   PalmJumeirahDesc: "+10 Builder Capacity. This wonder can be upgraded and each additional upgrade provides +2 Builder Capacity",
   Pamukkale: "Pamukkale",
   PamukkaleDesc: "When discovered, convert each one of the Permanent Great People Shards (except for Promotion and Wildcard) to the same Great Person From This Run",
   Panathenaea: "Panathenaea: Poseidon provides +1 Production Multiplier to all buildings",
   Pantheon: "Pantheon",
   PantheonDescV2: "All buildings within 2 tile range get +1 Worker Capaicity and Storage Multiplier. Generate science based on faith production of all shrines",
   Paper: "Kağıt",
   PaperMaker: "Kağıt Üreticisi",
   Parliament: "Parlamento",
   Parthenon: "Parthenon",
   ParthenonDescV2: "Klasik Çağın iki büyük insanı doğar ve her biri için 4 seçenek elde edersiniz. Müzisyen Loncalarına ve Ressam Loncalarına +1 Üretim, İşçi Kapasitesi ve Depolama Çarpanı verir ve -1 Mutluluktan muaftır",
   Passcode: "Passcode",
   PasscodeToastHTML: "<b>%{code}</b> is your passcode and it's valid for 30 minutes",
   PatchNotes: "Yama Notları",
   Peace: "Peace",
   Peacekeeper: "Peacekeeper",
   Penthouse: "Penthouse",
   PercentageOfProductionWorkers: "Percentage of Production Workers",
   Performance: "Performance",
   PermanentGreatPeople: "Kalıcı Büyük Kişi",
   PermanentGreatPeopleAcquired: "Kalıcı Büyük Kişi Kazanıldı",
   PermanentGreatPeopleUpgradeUndo: "Undo permanent great people upgrade: this will convert upgraded level back to shards - you will get %{amount} shards",
   Persepolis: "Persepolis",
   PersepolisDesc: "Tüm Bakır Madenciliği kamplarına, tomrukçuluk kamplarına ve taş ocaklarına +1 Üretim Çarpanı, İşçi Kapasitesi Çarpanı ve Depolama Çarpanı verir",
   PeterHiggs: "Peter Higgs",
   PeterHiggsDesc: "+%{value} Science From Busy Workers",
   Petra: "Petra",
   PetraDesc: "Çevrimdışı olduğunuzda imparatorluğunuzu hızlandırmak için kullanabileceğiniz zaman atlaması oluşturun",
   PetraOfflineTimeReconciliation: "You have been credited %{count} warp after server offline time reconciliation",
   Petrol: "Benzin",
   PhiloFarnsworth: "Philo Farnsworth",
   Philosophy: "Felsefe",
   Physics: "Fizik",
   PierreDeCoubertin: "Pierre de Coubertin",
   Pizza: "Pizza",
   Pizzeria: "Pizzacı",
   PlanetaryRover: "Planetary Rover",
   Plastics: "Plastik",
   PlasticsFactory: "Plastik Fabrikası",
   PlatformAndroid: "Android",
   PlatformiOS: "iOS",
   PlatformSteam: "Steam",
   PlatformSyncInstructionHTML: "If you want to sync your progress on this device to a new device, click <b>Sync To A New Device</b> and get a one-time passcode. On your new device, click <b>Connect To A Device</b> and type in the one-time passcode",
   Plato: "Plato",
   PlayerHandle: "Player Handle",
   PlayerHandleOffline: "Şu anda çevrimdışı durumdasınız",
   PlayerMapClaimThisTile: "Bu Karoyu Sahiplen",
   PlayerMapClaimTileCondition2: "Hile karşıtı tarafından yasaklanmadınız",
   PlayerMapClaimTileCondition3: "Gerekli teknolojinin kilidini açtınız: %{tech}",
   PlayerMapClaimTileCondition4: "Bir karo talep etmediniz veya karonuzu taşıma bekleme süresini geçtiniz",
   PlayerMapClaimTileCooldownLeft: "Kalan bekleme süresi: %{time}",
   PlayerMapClaimTileNoLongerReserved: "Bu karo artık rezerve edilmemiştir. <b>%{name}</b>'i tahliye edebilir ve bu kareyi kendiniz için talep edebilirsiniz",
   PlayerMapEstablishedSince: "Kurulduğundan beri",
   PlayerMapLastSeenAt: "Son Görülme",
   PlayerMapMapTileBonus: "Trade Tile Bonus",
   PlayerMapMenu: "Ticaret",
   PlayerMapOccupyThisTile: "Occupy This Tile",
   PlayerMapOccupyTileCondition1: "This tile is adjacent to your home or occupied tiles",
   PlayerMapPageGoBackToCity: "Şehre Dön",
   PlayerMapSetYourTariff: "Tarifenizi Belirleyin",
   PlayerMapTariff: "Tarife",
   PlayerMapTariffApply: "Tarife Oranını Uygula",
   PlayerMapTariffDesc: "Karonuzdan geçen her ticaret size tarife ödeyecektir. Bu bir dengedir: tarifeyi artırırsanız, her ticaretten daha fazla kazanırsınız, ancak karonuzdan daha az ticaret geçecektir.",
   PlayerMapTileAvailableTilePoint: "Available Tile Point",
   PlayerMapTileFromOccupying: "From Owned/Occupied Tiles",
   PlayerMapTileFromOccupyingTooltipHTML: "An owned/occupied tile generates <b>%{point}</b> tile point per hour (up to %{max} days from the first claimed tile)",
   PlayerMapTileFromRank: "From Account Rank",
   PlayerMapTileTilePoint: "Tile Point",
   PlayerMapTileUsedTilePoint: "Used Tile Point",
   PlayerMapTileUsedTilePointTooltipHTML: "You need <b>1 tile point</b> to own/occupy a tile",
   PlayerMapTradesFrom: "Şuradan takas %{name}",
   PlayerMapUnclaimedTile: "Sahipsiz Karo",
   PlayerMapYourTile: "Karonuz",
   PlayerTrade: "Oyuncularla Takas",
   PlayerTradeAddSuccess: "Ticaret başarıyla eklendi",
   PlayerTradeAddTradeCancel: "İptal Et",
   PlayerTradeAmount: "Miktar",
   PlayerTradeCancelDescHTML: "You will get <b>%{res}</b> back after cancelling this trade: <b>%{percent}</b> charged for refund and <b>%{discard}</b> discarded due to storage overflow<br><b>Are you sure you want to cancel?</b>",
   PlayerTradeCancelTrade: "Takası İptal Et",
   PlayerTradeClaim: "Talep Et",
   PlayerTradeClaimAll: "Hepsini Talep Et",
   PlayerTradeClaimAllFailedMessageV2: "Herhangi bir işlem talep edilemedi - depo dolu mu?",
   PlayerTradeClaimAllMessageV2: "Talebiniz: <b>%{resources}</b>",
   PlayerTradeClaimAvailable: "%{count} trade(s) has been filled available for claim",
   PlayerTradeClaimTileFirst: "Karonuzu Ticaret Haritasında Talep Edin",
   PlayerTradeClaimTileFirstWarning: "Diğer oyuncularla sadece ticaret haritasındaki karenizi talep ettikten sonra ticaret yapabilirsiniz",
   PlayerTradeClearAll: "Clear All Fills",
   PlayerTradeClearFilter: "Clear Filters",
   PlayerTradeDisabledBeta: "You can ony create player trades once the beta version is released",
   PlayerTradeFill: "Doldur",
   PlayerTradeFill50: "Fill 50%",
   PlayerTradeFill95: "Fill 95%",
   PlayerTradeFillAmount: "Doldurma miktarı",
   PlayerTradeFillAmountMaxV2: "Maksimum Doldur",
   PlayerTradeFillBy: "Tarafından doldur",
   PlayerTradeFillPercentage: "Doldurma Yüzdesi",
   PlayerTradeFillSuccessV2: "<b>%{success}/%{total}</b> trades have been filled. You paid <b>%{fillAmount} %{fillResource}</b> and received <b>%{receivedAmount} %{receivedResource}</b>",
   PlayerTradeFillTradeButton: "Fill Trade",
   PlayerTradeFillTradeTitle: "Fill Trade",
   PlayerTradeFilters: "Filtreler",
   PlayerTradeFiltersApply: "Uygula",
   PlayerTradeFiltersClear: "Temizle",
   PlayerTradeFilterWhatIHave: "Filter By What I Have",
   PlayerTradeFrom: "Gönderen",
   PlayerTradeIOffer: "Teklifim",
   PlayerTradeIWant: "İstediğim",
   PlayerTradeMaxAll: "Max All Fills",
   PlayerTradeMaxTradeAmountFilter: "Max Amount",
   PlayerTradeMaxTradeExceeded: "Hesap seviyeniz için maksimum aktif takas sayısını aştınız",
   PlayerTradeNewTrade: "Yeni Takas",
   PlayerTradeNoFillBecauseOfResources: "No trade has been filled due to insufficient resources",
   PlayerTradeNoValidRoute: "Sizinle %{name} arasında geçerli bir ticaret rotası bulunamıyor",
   PlayerTradeOffer: "Teklif",
   PlayerTradePlaceTrade: "Place Trade",
   PlayerTradePlayerNameFilter: "Player Name",
   PlayerTradeResource: "Kaynak",
   PlayerTradeStorageRequired: "Gerekli Depolama",
   PlayerTradeTabImport: "Import",
   PlayerTradeTabPendingTrades: "Pending Trades",
   PlayerTradeTabTrades: "Trades",
   PlayerTradeTariffTooltip: "Ticaret Tarifesinden Toplanan",
   PlayerTradeWant: "Want",
   PlayerTradeYouGetGross: "Alırsın (Before Tariff): %{res}",
   PlayerTradeYouGetNet: "Alırsın (After Tariff): %{res}",
   PlayerTradeYouPay: "Ödersin: %{res}",
   Poem: "Şiir",
   PoetrySchool: "Poetry School",
   Politics: "Politika",
   PolytheismLevelX: "Polytheism %{level}",
   PorcelainTower: "Nanjing Porselen Kulesi",
   PorcelainTowerDesc: "+5 Happiness. When constructed, all your extra great people at rebirth will become available for this run (they are rolled following the same rule as permanent great people)",
   PorcelainTowerMaxPickPerRoll: "Prefer Max Pick Per Roll",
   PorcelainTowerMaxPickPerRollDescHTML: "When choosing great people after Porcelain Tower completed, prefer max pick per roll for the available amount",
   Poseidon: "Poseidon",
   PoseidonDescV2: "All adjacent buildings get free upgrades to Level 25 and +N Production, Worker Capacity and Storage Multiplier. N = Tier of the building",
   PoultryFarm: "Poultry Farm",
   Power: "Güç",
   PowerAvailable: "Mevcut Güç",
   PowerUsed: "Kullanılan Güç",
   PreciousMetal: "Değerli Metal",
   Printing: "Baskı",
   PrintingHouse: "Basımevi",
   PrintingPress: "Matbaa",
   PrivateOwnership: "Özel Mülkiyet",
   Produce: "Üretim",
   ProduceResource: "Üretim: %{resource}",
   ProductionMultiplier: "Üretim Çarpanı",
   ProductionPriority: "Üretim Önceliği",
   ProductionPriorityDescV4: "Priority determins the order that buildings transport and produce - a bigger number means a building transports and produces before other buildings",
   ProductionWorkers: "Production Workers",
   Progress: "İlerleme",
   ProgressTowardsNextGreatPerson: "Rebirthte Bir Sonraki Büyük Kişiye Doğru İlerleme",
   ProgressTowardsTheNextGreatPerson: "Progress Towards the Next Great Person",
   PromotionGreatPersonDescV2: "When consumed, promote any permanent great people of the same age to the next age",
   ProphetsMosque: "Prophet's Mosque",
   ProphetsMosqueDesc: "Double the effect of Harun al-Rashid. Generate science based on faith production of all mosques",
   Province: "Şehir",
   ProvinceAegyptus: "Aigiptos",
   ProvinceAfrica: "Afrika",
   ProvinceAsia: "Asya",
   ProvinceBithynia: "Bitinya",
   ProvinceCantabri: "Cantabri",
   ProvinceCappadocia: "Kapadokya",
   ProvinceCilicia: "Kilikya",
   ProvinceCommagene: "Kommagene",
   ProvinceCreta: "Girit",
   ProvinceCyprus: "Kıbrıs",
   ProvinceCyrene: "Kirene",
   ProvinceGalatia: "Galatya",
   ProvinceGallia: "Galya",
   ProvinceGalliaCisalpina: "Cisalpina Galya",
   ProvinceGalliaTransalpina: "Gallia Transalpina",
   ProvinceHispania: "Hispania",
   ProvinceIllyricum: "Illyricum",
   ProvinceItalia: "Italya",
   ProvinceJudia: "Judia",
   ProvinceLycia: "Likya",
   ProvinceMacedonia: "Makedonya",
   ProvinceMauretania: "Mauretanya",
   ProvinceNumidia: "Numidya",
   ProvincePontus: "Pontus",
   ProvinceSardiniaAndCorsica: "Sardinia And Corsica",
   ProvinceSicillia: "Sicilya",
   ProvinceSophene: "Sophene",
   ProvinceSyria: "Suri",
   PublishingHouse: "Yayınevi",
   PyramidOfGiza: "Giza Piramidi",
   PyramidOfGizaDesc: "İşçi üreten tüm binalara +1 Üretim Çarpanı verir",
   QinShiHuang: "Çin Şi Huang",
   Radio: "Radyo",
   RadioStation: "Radyo İstasyonu",
   Railway: "Demiryolu",
   RamessesII: "Ramses II",
   RamessesIIDesc: "+%{value} İnşaat Kapasitesi Çarpanı",
   RandomColorScheme: "Random Color Scheme",
   RapidFire: "Seri Ateş",
   ReadFullPatchNotes: "Yama Notunu Oku",
   RebirthHistory: "Rebirth History",
   RebirthTime: "Rebirth Time",
   Reborn: "Rebirth",
   RebornModalDescV3: "You will start a new empire but all your great people <b>from this run</b> becomes permanent shards, which can be used to upgrade your <b>permanent great people level</b>. You will also get extra great people shards based on your <b>total empire value</b>",
   RebornOfflineWarning: "Şu anda çevrimdışı durumdasınız. Sadece sunucuya bağlı olduğunuzda yeniden doğabilirsiniz",
   RebornTradeWarning: "Aktif veya alınmayı bekleyen takaslarınız var. <b>Rebirth bunları silecek.</b> - iptal edip takaslarınızı almayı düşünebilirsizin",
   RedistributeAmongSelected: "Seçilenler Arasında Yeniden Dağıtın",
   RedistributeAmongSelectedCap: "Cap",
   RedistributeAmongSelectedImport: "Import",
   Refinery: "Rafineri",
   Reichstag: "Reichstag",
   Religion: "Din",
   ReligionBuddhism: "Buddhism",
   ReligionChristianity: "Christianity",
   ReligionDescHTML: "Choose from <b>Christianity, Islam, Buddhism or Polytheism</b> as your empire religion. You <b>cannot switch religion</b> after it is chosen. You can unlock more boost within each religion",
   ReligionIslam: "Islam",
   ReligionPolytheism: "Polytheism",
   Renaissance: "Rönesans",
   RenaissanceAge: "Rönesans Çağı",
   ReneDescartes: "René Descartes",
   RequiredDeposit: "Gerekli Kaynak",
   RequiredWorkersTooltipV2: "Required number of workers for production is equal to the sum of all resources consumed and produced after multipliers (excluding dynamic multipliers)",
   RequirePower: "Güç Gerektirir",
   RequirePowerDesc: "Bu bina gücü olan bir karo üzerine inşa edilmesi gerekir ve gücü komşu karolara yayabilir",
   Research: "Araştırma",
   ResearchFund: "Research Fund",
   ResearchLab: "Araştırma Laboratuvarı",
   ResearchMenu: "Araştırma",
   ResourceAmount: "Miktar",
   ResourceBar: "Resource Bar",
   ResourceBarExcludeStorageFullHTML: "Exclude buildings that have <b>full storage</b> from Not Producing Buildings",
   ResourceBarExcludeTurnedOffOrNoActiveTransportHTML: "Exclude buildings that are <b>turned off</b> from Not Producing Buildings",
   ResourceBarShowUncappedHappiness: "Show Uncapped Happiness",
   ResourceCloneTooltip: "The production multiplier only applies to the cloned resource (i.e. the extra copy)",
   ResourceColor: "Kaynak Rengi",
   ResourceExportBelowCap: "Export Below Cap",
   ResourceExportBelowCapTooltip: "Miktarı sınırın altında olsa bile diğer binaların bu binadan bir kaynak taşımasına izin verin",
   ResourceExportToSameType: "Export to the Same Type",
   ResourceExportToSameTypeTooltip: "Allow other buildings of the same type to transport a resource from this building",
   ResourceFromBuilding: "%{resource} 'dan %{building} 'a",
   ResourceImport: "Kaynak Taşıma",
   ResourceImportCapacity: "Kaynak Taşıma Kapasitesi",
   ResourceImportImportCapV2: "Maksimum Miktar",
   ResourceImportImportCapV2Tooltip: "Bu bina, maksimum miktara ulaşıldığında bu kaynağı taşımayı durduracaktır",
   ResourceImportImportPerCycleV2: "Döngü Başına",
   ResourceImportImportPerCycleV2ToolTip: "Bu kaynağın döngü başına taşınan miktarı",
   ResourceImportPartialWarningHTML: "The total resource transport capacity has exceeds the maximum capacity: <b>each resource transport will only transport partially per cycle</b>",
   ResourceImportResource: "Kaynak",
   ResourceImportSettings: "Kaynak Taşıma: %{res}",
   ResourceImportStorage: "Depo",
   ResourceNeeded: "Extra %{resource} x%{amount} Needed",
   ResourceTransportPreference: "Taşıma Tercihi",
   RevealDeposit: "Göster",
   Revolution: "Devrim",
   RhineGorge: "Rhine Gorge",
   RhineGorgeDesc: "+2 Happiness for each wonder within 2 tile range",
   RichardFeynman: "Richard Feynman",
   RichardFeynmanDesc: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   RichardJordanGatling: "Richard Jordan Gatling",
   Rifle: "Tüfek",
   RifleFactory: "Tüfek Fabrikası",
   Rifling: "Yiv",
   Rijksmuseum: "Rijksmuseum",
   RijksmuseumDesc: "+5 Mutluluk. Kültür tüketen veya üreten tüm binalara +1 Üretim, Depolama ve İşçi Kapasitesi verir",
   RoadAndWheel: "Yol & Tekerlek",
   RobertNoyce: "Robert Noyce",
   Robocar: "Robocar",
   RobocarFactory: "Robocar Factory",
   Robotics: "Robotik",
   RockefellerCenterChristmasTree: "Rockefeller Center Christmas Tree",
   RockefellerCenterChristmasTreeDesc: "+3 Happiness for each unlocked age. This natural wonder can only be discovered in December",
   Rocket: "Roket",
   RocketFactory: "Roket Fabrikası",
   Rocketry: "Roketçilik",
   Roman: "Roman",
   RomanForum: "Roman Forum",
   RudolfDiesel: "Rudolf Diesel",
   Rurik: "Rurik",
   RurikDesc: "+%{value} Mutluluk",
   SagradaFamilia: "Sagrada Família",
   SagradaFamiliaDesc: "Sagrada Família",
   SaintBasilsCathedral: "Saint Basil's Katedrali",
   SaintBasilsCathedralDescV2: "Kaynak çıkarma binalarının bir yatağın bitişiğinde çalışmasına izin verir. Tüm aşama I binalarına +1 Üretim Çarpanı, İşçi Kapasitesi Çarpanı ve Depolama Çarpanı verir",
   Saladin: "Saladin",
   Samsuiluna: "Samsu-iluna",
   Sand: "Kum",
   Sandpit: "Kum Ocağı",
   SantaClausVillage: "Santa Claus Village",
   SantaClausVillageDesc: "When completed, a great person of the current age is born. This wonder can be upgraded and each additional upgrade provides an extra great person. When choosing great people from this wonder, 4 choices are provided. This wonder can only be constructed in December",
   SargonOfAkkad: "Sargon Of Akkad",
   Satellite: "Satellite",
   SatelliteFactory: "Satellite Factory",
   SatoshiNakamoto: "Satoshi Nakamoto",
   Saturnalia: "Saturnalia: Alps no longer increases Consumption Multiplier",
   SaveAndExit: "Kaydet ve Çık",
   School: "Okul",
   Science: "Bilim",
   ScienceFromBusyWorkers: "Meşgul Çalışanlardan Gelen Bilim",
   ScienceFromIdleWorkers: "Boşta İşçilerin Bilim Üretimi",
   SciencePerBusyWorker: "Meşgul Çalışan Başına",
   SciencePerIdleWorker: "Boşta İşçi Başına",
   ScrollSensitivity: "Scroll Sensitivity",
   ScrollSensitivityDescHTML: "Adjust sensitivity when scrolling mousewheel. <b>Must be between 0.01 to 100. Default is 1</b>",
   ScrollWheelAdjustLevelTooltip: "İmleciniz bunun üzerindeyken seviyeyi ayarlamak için kaydırma tekerleğini kullanabilirsiniz",
   SeaTradeCost: "Deniz Ticareti Maliyeti",
   SeaTradeUpgrade: "Denizin karşısındaki oyuncularla ticaret. Her deniz karesi için tarife: %{tariff}",
   SelectCivilization: "Select Civilization",
   SelectedAll: "Tümünü Seç",
   SelectedCount: "%{count} Seçilen",
   Semiconductor: "Yarı İletken",
   SemiconductorFab: "Semiconductor Fab",
   SendExplorer: "Kaşif Gönder",
   SergeiKorolev: "Sergei Korolev",
   SetAsDefault: "Varsayılan Olarak Ayarla",
   SetAsDefaultBuilding: "Tümü İçin Varsayılan Olarak Ayarla %{building}",
   Shamanism: "Şamanizm",
   Shelter: "Barınak",
   Shortcut: "Kısayol",
   ShortcutBuildingPageSellBuildingV2: "Demolish Building",
   ShortcutBuildingPageToggleBuilding: "Üretimi Aç/Kapa",
   ShortcutBuildingPageToggleBuildingSetAllSimilar: "Hepsi için üretimi Aç/Kapa",
   ShortcutBuildingPageUpgrade1: "Upgrade Button 1 (+1)",
   ShortcutBuildingPageUpgrade2: "Upgrade Button 2 (+5)",
   ShortcutBuildingPageUpgrade3: "Upgrade Button 3 (+10)",
   ShortcutBuildingPageUpgrade4: "Upgrade Button 4 (+15)",
   ShortcutBuildingPageUpgrade5: "Upgrade Button 5 (+20)",
   ShortcutClear: "Temizle",
   ShortcutConflict: "Kısayolunuz aşağıdakilerle çakışıyor %{name}",
   ShortcutNone: "None",
   ShortcutPressShortcut: "Kısayol Tuşuna Bas...",
   ShortcutSave: "Kaydet",
   ShortcutScopeBuildingPage: "Yapı Sayfası",
   ShortcutScopeConstructionPage: "İnşaat/Yükseltme Sayfası",
   ShortcutScopeEmptyTilePage: "Boş Karo Sayfası",
   ShortcutScopePlayerMapPage: "Ticaret Haritası Sayfası",
   ShortcutScopeTechPage: "Teknoloji Sayfası",
   ShortcutScopeUnexploredPage: "Unexplored Page",
   ShortcutTechPageGoBackToCity: "Şehre Dön",
   ShortcutTechPageUnlockTech: "Seçili Teknolojinin Kilidini Aç",
   ShortcutUpgradePageCancelAllUpgrades: "Cancel All Upgrades",
   ShortcutUpgradePageCancelUpgrade: "Yükseltmeyi İptal Et",
   ShortcutUpgradePageDecreaseLevel: "Yükseltme Seviyesini Düşür",
   ShortcutUpgradePageEndConstruction: "İnşaatı İptal Et",
   ShortcutUpgradePageIncreaseLevel: "Yükseltme Seviyesini Yükselt",
   ShowTransportArrow: "Show Transport Arrow",
   ShowTransportArrowDescHTML: "Turning this off will hide transport arrows. It might <i>slightly</i> improve performance on low end devices. Performance improvement takes effect <b>after restarting your game</b>",
   ShowUnbuiltOnly: "Sadece henüz inşa edilmemiş binaları göster",
   Shrine: "Tapınak",
   SidePanelWidth: "Yan Panel Genişliği",
   SidePanelWidthDescHTML: "Yan panelin genişliğini değiştirin. <b>Etkili olması için oyunun yeniden başlatılmasını gerektirir</b>",
   SiegeRam: "Koçbaşı",
   SiegeWorkshop: "Koçbaşı Atölyesi",
   Silicon: "Silicon",
   SiliconSmelter: "Silicon Smelter",
   Skyscraper: "Skyscraper",
   Socialism: "Socialism",
   SocialismLevel4DescHTMLV2: "Generate one-time science equivalent to the cost of the cheapest <b>World Wars Age</b> technology",
   SocialismLevel5DescHTMLV2: "Generate one-time science equivalent to the cost of the cheapest <b>Cold War Age</b> technology",
   SocialismLevelX: "Socialism Level %{level}",
   SocialNetwork: "Sosyal Ağ",
   Socrates: "Sokrates",
   SocratesDesc: "+%{value} Meşgul Çalışanlardan Bilim",
   Software: "Software",
   SoftwareCompany: "Software Company",
   Sound: "Ses",
   SoundEffect: "Ses Efekti",
   SourceGreatPerson: "Büyük Kişi: %{person}",
   SourceGreatPersonPermanent: "Kalıcı Büyük Kişi: %{person}",
   SourceIdeology: "Ideology: %{ideology}",
   SourceReligion: "Religion: %{religion}",
   SourceResearch: "Araştırma: %{tech}",
   SourceTradition: "Tradition: %{tradition}",
   SpaceCenter: "Space Center",
   Spacecraft: "Spacecraft",
   SpacecraftFactory: "Spacecraft Factory",
   SpaceNeedle: "Space Needle",
   SpaceNeedleDesc: "+1 Happiness for each wonder constructed",
   SpaceProgram: "Uzay Programı",
   Sports: "Spor",
   Stable: "Ahır",
   Stadium: "Stadyum",
   StartFestival: "Let the Festival Begin!",
   Stateship: "Devlet Başkanlığı",
   StatisticsBuildings: "Binalar",
   StatisticsBuildingsSearchText: "Type a building name to search",
   StatisticsEmpire: "Empire",
   StatisticsExploration: "Exploration",
   StatisticsOffice: "İstatistik Ofisi",
   StatisticsOfficeDesc: "İmparatorluğunuzun istatistiklerini sağlayın. Haritayı keşfetmek için kaşifler oluşturun",
   StatisticsResources: "Kaynaklar",
   StatisticsResourcesDeficit: "Eksiklik",
   StatisticsResourcesDeficitDesc: "Çıktı: %{output} - Girdi: %{input}",
   StatisticsResourcesRunOut: "Bitti",
   StatisticsResourcesSearchText: "Type a resource name to search",
   StatisticsScience: "Bilim",
   StatisticsScienceFromBuildings: "Yapılardan Bilim",
   StatisticsScienceFromWorkers: "İşçilerden Bilim",
   StatisticsScienceProduction: "Bilim Üretimi",
   StatisticsStalledTransportation: "Durmuş Taşıma",
   StatisticsTotalTransportation: "Toplam Taşıma",
   StatisticsTransportation: "Taşımacılık",
   StatisticsTransportationPercentage: "Taşımacılık Çalışanlarının Yüzdesi",
   StatueOfLiberty: "Özgürlük Heykeli",
   StatueOfLibertyDesc: "Bitişiğindeki tüm binalara +N Üretim, Depolama ve İşçi Kapasitesi Çarpanı verir. N = Aynı tipteki bitişik binalarının sayısı",
   StatueOfZeus: "Zeus Heykeli",
   StatueOfZeusDesc: "Bitişiğindeki boş karolarda rastgele kaynakları ortaya çıkarır. Bitişiğindeki Tüm Aşama I binalara +5 Üretim ve Depolama Çarpanı verir",
   SteamAchievement: "Steam Başarımları",
   SteamAchievementDetails: "Steam Başarımları görüntüle",
   SteamEngine: "Buhar Motoru",
   Steamworks: "Buhar Fabrikası",
   Steel: "Çelik",
   SteelMill: "Çelik Fabrikası",
   StephenHawking: "Stephen Hawking",
   Stock: "Hisse senedi",
   StockExchange: "Hisse Senedi Borsası",
   StockMarket: "Borsa",
   StockpileDesc: "This building will transport %{capacity}x input resources per production cycle until the max is reached",
   StockpileMax: "Max Stockpile",
   StockpileMaxDesc: "This building will stop transporting a resource once there are enough for %{cycle} production cycles",
   StockpileMaxUnlimited: "Unlimited",
   StockpileMaxUnlimitedDesc: "The building will never stop transporting resources until the storage is full",
   StockpileSettings: "Stok Alanı Girdi Kapasitesi",
   Stone: "Taş",
   StoneAge: "Taş Devri",
   Stonehenge: "Stonehenge",
   StonehengeDesc: "Taş tüketen veya üreten tüm binalar +1 Üretim Çarpanı alır",
   StoneQuarry: "Taş Ocağı",
   StoneTool: "Taş Alet",
   StoneTools: "Taş Aletler",
   Storage: "Depolama",
   StorageBaseCapacity: "Temel Kapasite",
   StorageMultiplier: "Depolama Çarpanı",
   StorageUsed: "Kullanılan Depolama",
   StPetersBasilica: "St. Peter's Basilikası",
   StPetersBasilicaDescV2: "All churches get +5 Storage Multiplier. Generate science based on faith production of all churches",
   Submarine: "Submarine",
   SubmarineYard: "Submarine Yard",
   SuleimanI: "Suleiman I",
   SummerPalace: "Yazlık Saray",
   SummerPalaceDesc: "Barut tüketen veya üreten tüm bitişik binalar -1 Mutluluk'tan muaftır. Barut tüketen veya üreten tüm binalara +1 Üretim, Depolama ve İşçi Kapasitesi verir.",
   Supercomputer: "Supercomputer",
   SupercomputerLab: "Supercomputer Lab",
   SupporterPackRequired: "Supporter Pack Required",
   SupporterThankYou: "CivIdle is kept afloat thanks to the generousity of the following supporter pack owners",
   SwissBank: "Swiss Bank",
   SwissBankDescV2: "Convert a chosen resource from adjacent warehouses to Koti (10 million in Sanskrit) - a tradeable resource that is worth 10M value. Each level of Swiss Bank add 1 Koti conversion per cycle, which is affected by Production Multiplier. Swiss Bank can store unlimited amount of Koti",
   Sword: "Kılıç",
   SwordForge: "Kılıç Ocağı ",
   SydneyOperaHouse: "Sidney Opera Evi",
   SydneyOperaHouseDescV2: "Sydney Opera House",
   SyncToANewDevice: "Sync To A New Device",
   Synthetics: "Sentetikler",
   TajMahal: "Tac Mahal",
   TajMahalDescV2: "Klasik Çağ'ın büyük bir insanı ve Orta Çağ'ın büyük bir insanı doğar. 20. Seviyenin üzerindeki binaları yükseltirken +5 İnşaatçı Kapasite Çarpanı verir",
   TangOfShang: "Tang Of Shang",
   TangOfShangDesc: "+%{value} Boşta Çalışanlardan Bilim",
   Tank: "Tank",
   TankFactory: "Tank Fabrikası",
   TechAge: "Age",
   TechGlobalMultiplier: "Güçlendirme",
   TechHasBeenUnlocked: "%{tech} 'in kilidi açıldı",
   TechProductionPriority: "Bina önceliğinin kilidini açın - her bina için üretim önceliğinin ayarlanmasına izin verin",
   TechResourceTransportPreference: "Bina taşıma tercihinin kilidini aç - bir binanın üretimi için gereken kaynakları nasıl taşıyacağını ayarlamaya izin verir",
   TechResourceTransportPreferenceAmount: "Miktar",
   TechResourceTransportPreferenceAmountTooltip: "Bu bina, depoda daha fazla miktarda bulunan binalardan kaynak taşımayı tercih edecektir",
   TechResourceTransportPreferenceDefault: "Varsayılan",
   TechResourceTransportPreferenceDefaultTooltip: "Bu kaynak için taşıma tercihini geçersiz kıl, bunun yerine binanın taşıma tercihini kullanacak.",
   TechResourceTransportPreferenceDistance: "Mesafe",
   TechResourceTransportPreferenceDistanceTooltip: "Bu bina, daha yakın mesafedeki binalardan kaynak taşımayı tercih edecektir",
   TechResourceTransportPreferenceOverrideTooltip: "Bu kaynağın taşıma tercihi geçersiz kılınmıştır:  %{mode}",
   TechResourceTransportPreferenceStorage: "Depo",
   TechResourceTransportPreferenceStorageTooltip: "Bu bina, daha yüksek oranda kullanılmış depoya sahip binalardan kaynak taşımayı tercih edecektir",
   TechStockpileMode: "Stoklama modunun kilidini açın - her bina için stoklamayı ayarlamaya izin verin",
   Teleport: "Teleport",
   TeleportDescHTML: "A teleport is generated <b>every %{time} seconds</b>. A teleport can be used to <b>move a building (wonders excluded)</b> once",
   Television: "Televizyon",
   TempleOfArtemis: "Artemis Tapınağı",
   TempleOfArtemisDesc: "Tüm Kılıç Ocakları ve Zırh Atölyeleri tamamlandığında +5 Seviye kazanır. Tüm Kılıç Ocaklarına ve Zırh Atölyelerine +1 Üretim Çarpanı, İşçi Kapasitesi Çarpanı ve Depolama Çarpanı verir",
   TempleOfHeaven: "Cennet Tapınağı",
   TempleOfHeavenDesc: "Seviyesi 10 veya daha yüksek olan tüm binalara +1 İşçi Kapasitesi Çarpanı verir",
   TempleOfPtah: "Ptah Tapınağı",
   TerracottaArmy: "Toprak Askerler",
   TerracottaArmyDesc: "Tüm Demir Madeni Kampları +1 Üretim Çarpanı, İşçi Kapasitesi Çarpanı ve Depolama Çarpanı alır. Demir Ocaklarına, bitişik Her Demir Madeni Kampına için +1 Üretim Çarpanı verir",
   Thanksgiving: "Thanksgiving: Wall Street provides double the boost to buildings and applies to Mutual Fund, Hedge Fund and Bitcoin Miner. Research Funds get +5 Production Multiplier",
   Theater: "Tiyatro",
   Theme: "Tema",
   ThemeColor: "Temee Rengi",
   ThemeColorResearchBackground: "Araştırma Arka Planı",
   ThemeColorReset: "Varsayılana Dön",
   ThemeColorResetBuildingColors: "Yapı Renklerini Sıfırla",
   ThemeColorResetResourceColors: "Kaynak Renklerini Sıfırla",
   ThemeInactiveBuildingAlpha: "Aktif Olmayan Yapı Opaklığı",
   ThemePremiumTile: "This tile is only available for Supporter Pack owners",
   ThemeResearchHighlightColor: "Araştırma Vurgulama Rengi",
   ThemeResearchLockedColor: "Kilitli Araştırma Rengi",
   ThemeResearchUnlockedColor: "Açılmış Araştırma Rengi",
   ThemeTransportIndicatorAlpha: "Taşıma Göstergesi Opaklığı",
   Theocracy: "Teokrasi",
   TheoreticalData: "Theoretical Data",
   ThePentagon: "The Pentagon",
   ThePentagonDesc: "After constructed, generate teleports that can be used to move buildings. All buildings within 2 tile range get +1 Production, Worker Capacity and Storage Multiplier",
   TheWhiteHouse: "The White House",
   ThomasEdison: "Thomas Edison",
   ThomasGresham: "Thomas Gresham",
   Tile: "Bölge",
   TileBonusRefreshIn: "Tile bonus will refresh in <b>%{time}</b>",
   TimBernersLee: "Tim Berners-Lee",
   TimeWarp: "Zaman Bükülmesi",
   TimeWarpWarning: "Bilgisayarınızın kaldırabileceğinden daha yüksek bir hızda hızlandırmak veri kaybına neden olabilir: RİSKİ SİZE AİT OLMAK KAYDIYLA KULLANIN",
   ToggleWonderEffect: "Toggle Wonder Effect",
   Tool: "Alet",
   TopkapiPalace: "Topkapı Palace",
   TopkapiPalaceDesc: "All buildings within 2 tile range get +X Storage Multiplier. X = 50% of its Production Multiplier (excluding Dynamic)",
   TotalEmpireValue: "İmparatorluğun Toplam Değeri",
   TotalEmpireValuePerCycle: "Döngü Başına Toplam İmparatorluk Değeri",
   TotalEmpireValuePerCyclePerGreatPeopleLevel: "Total Empire Value Per Cycle Per Great People Level",
   TotalEmpireValuePerWallSecond: "Total Empire Value Wall Second",
   TotalEmpireValuePerWallSecondPerGreatPeopleLevel: "Total Empire Value Per Wall Second Per Great People Level",
   TotalGameTimeThisRun: "Total Game Time This Run",
   TotalScienceRequired: "Total Science Required",
   TotalStorage: "Toplam Depolama",
   TotalWallTimeThisRun: "Total Wall Time This Run",
   TotalWallTimeThisRunTooltip: "Wall time (aka. elapsed real time) measures the actual time taken for this run. The differs from the game time in that Time Warp in Petra and Offline Production does not affect wall time but it does affect game time",
   TotalWorkers: "Toplam İşçiler",
   TowerBridge: "Tower Bridge",
   TowerBridgeDesc: "After constructed, a great person from unlocked ages is born every 3600 cycles (1h game time)",
   TowerOfBabel: "Tower of Babel",
   TowerOfBabelDesc: "Provides +2 Production Multiplier to all buildings that has at least one working building located adjacent to the wonder",
   TradeFillSound: "'Trade Filled' Sound",
   TradeValue: "Trade Value",
   TraditionCommerce: "Commerce",
   TraditionCultivation: "Cultivation",
   TraditionDescHTML: "Choose from <b>Cultivation, Commerce, Expansion and Honor</b> as your empire tradition. You <b>cannot switch tradition</b> after it is chosen. You can unlock more boost within each tradition",
   TraditionExpansion: "Expansion",
   TraditionHonor: "Honor",
   Train: "Tren",
   TranslationPercentage: "%{language} is %{percentage} translated. Help improve this translation on GitHub",
   TranslatorCredit: "KancikJelibon and ZeroMenthoL",
   Translators: "Çevirmenler",
   TransportAllocatedCapacityTooltip: "Bu kaynağın taşınması için tahsis edilen İnşaat Kapasitesi",
   TransportationWorkers: "Transportation Workers",
   TransportCapacity: "Taşıma Kapasitesi",
   TransportCapacityMultiplier: "Taşıma Kapasitesi Çarpanı",
   TransportManualControlTooltip: "Bu kaynağı inşaat/yükseltme için taşıyın",
   TransportPlanCache: "Transport Plan Cache",
   TransportPlanCacheDescHTML:
      "Every cycle, each building calculates the best transport plan based on its settings - this process requires high CPU power. Enabling this will attempt to cache the result of the transport plan if it is still valid and therefore reduce CPU usage and frame rate drop. <b>Experimental Feature</b>",
   TribuneUpgradeDescGreatPeopleWarning: "Şu anki turunuzda büyük kişiler var. Önce <b>yeniden doğmalısın</b>. Quaestor rütbesine yükseltmek mevcut koşunuzu sıfırlayacaktır",
   TribuneUpgradeDescGreatPeopleWarningTitle: "Önce Rebirth yapın",
   TribuneUpgradeDescV4:
      "You can play the full game as Tribune if you do not plan to participate in the <b>optional</b> online features. To acquire unrestricted access to the online features, you will need to upgrade to Quaestor. <b>This is an anti-bot measure to keep the game free for everyone.</b> However, <b>when upgrading to Quaestor</b> you can carry over great people: <ul><li>Up to Level <b>3</b> for Bronze, Iron and Classical Age</li><li>Up to Level <b>2</b> for Middle Age, Renaissance and Industrial Age</li><li>Up to Level <b>1</b> for World Wars, Cold War and Information Age</li></ul>Great People Shards above the level and <b>Age Wisdom</b> levels <b>cannot</b> be carried over",
   TurnOffFullBuildings: "Turn Off All %{building} With Full Storage",
   TurnOnTimeWarpDesc: "Her saniye için %{speed} warp'a mal olur ve imparatorluğunuzu %{speed}x hızında çalışacak şekilde hızlandırır.",
   Tutorial: "Eğitici",
   TutorialPlayerFlag: "Oyuncu bayrağınızı seçin",
   TutorialPlayerHandle: "Oyuncu nickinizi seçin",
   TV: "TV",
   TVStation: "TV Station",
   UnclaimedGreatPersonPermanent: "Alınmamış <b>Kalıcı Büyük İnsanlarınız</b> var, almakiçin buraya tıklayın",
   UnclaimedGreatPersonThisRun: "Alınmamış <b>Büyük İnsanlarınız</b> var, almakiçin buraya tıklayın",
   UnexploredTile: "Keşfedilmemiş Bölge",
   UNGeneralAssemblyCurrent: "Current UN General Assembly #%{id}",
   UNGeneralAssemblyMultipliers: "<b>+%{count}</b> Production, Worker Capacity and Storage Multpliers for <b>%{buildings}</b>",
   UNGeneralAssemblyNext: "Upcoming UN General Assembly #%{id}",
   UNGeneralAssemblyVoteEndIn: "You can change your vote any time before the voting ends in <b>%{time}</b>",
   UniqueBuildings: "Eşsiz Yapılar",
   UniqueTechMultipliers: "Unique Tech Multipliers",
   UnitedNations: "Birleşmiş Milletler",
   UnitedNationsDesc: "All Tier IV and V and VI buildings get +1 Production, Worker Capacity and Storage Multiplier. Participate in UN General Assembly and vote for an additional boost each week",
   University: "Üniversite",
   UnlockableResearch: "Kilidi Açılabilinir Araştırma",
   UnlockBuilding: "Kilidi aç",
   UnlockTechProgress: "İlerleme",
   UnlockXHTML: "Unlock <b>%{name}</b>",
   Upgrade: "Yükselt",
   UpgradeBuilding: "Yükselt",
   UpgradeBuildingNotProducingDescV2: "Bu bina yükseltilmektedir - <b>yükseltme tamamlanana kadar üretim duracaktır</b>",
   UpgradeTo: "Upgrade To Level %{level}",
   Uranium: "Uranyum",
   UraniumEnrichmentPlant: "Uranyum Zenginleştirme Tesisi",
   UraniumMine: "Uranyum Madeni",
   Urbanization: "Kentleşme",
   UserAgent: "User Agent: %{driver}",
   View: "Görünüm",
   ViewMenu: "Görünüm",
   ViewTechnology: "Görüntüle",
   Vineyard: "Üzüm Bağı",
   VirtualReality: "Sanal Gerçeklik",
   Voltaire: "Voltaire",
   WallOfBabylon: "Wall of Babylon",
   WallOfBabylonDesc: "All buildings get +N Storage Multiplier. N = number of unlocked ages / 2",
   WallStreet: "Wall Street",
   WallStreetDesc: "All buildings that produce coin, banknote, bond, stock and forex within 2 tile range get +N production multiplier. N = Random value between 1 to 5 which is different per building and changes with every market refresh. Double the effect of John D. Rockefeller",
   WaltDisney: "Walt Disney",
   Warehouse: "Antrepo",
   WarehouseAutopilotSettings: "Otopilot Ayarları",
   WarehouseAutopilotSettingsEnable: "Otopilotu Etkinleştir",
   WarehouseAutopilotSettingsRespectCapSetting: "Require Storage < Cap",
   WarehouseAutopilotSettingsRespectCapSettingTooltip: "Otopilot yalnızca depodaki miktarı sınırın altında olan kaynakları taşıyacaktır",
   WarehouseDesc: "Belirli kaynakları içine taşır ve ekstra depolama alanı sağlar",
   WarehouseExtension: "Unlock warehouse caravansary extension mode. Allow warehouses adjacent to caravansaries to be included in player trading",
   WarehouseSettingsAutopilotDesc: "Bu depo, deposu dolu binalardan kaynak taşımak için mevcut kapasitesini kullanacaktır.",
   WarehouseUpgrade: "Depo otopilot modunun kilidini açın. Bir depo ve bitişiğindeki binalar arasında ücretsiz taşıma sağlar.",
   WarehouseUpgradeDesc: "Bu depo ve bitişiğindeki karolar arasında ücretsiz taşıma sağlar",
   Warp: "Warp",
   WarpSpeed: "Warp Speed",
   Water: "Su",
   WellStockedTooltip: "İyi stoklanmış binalar, üretimi için yeterli kaynağa sahip olan binalardır; üretim yapan, deposu dolu olan veya işçi eksikliği nedeniyle üretim yapmayan binaları içerir",
   WernherVonBraun: "Wernher von Braun",
   Westminster: "Westminster",
   Wheat: "Buğday",
   WheatFarm: "Buğday Çiftliği",
   WildCardGreatPersonDescV2: "When consumed, become any great person of the same age",
   WilliamShakespeare: "William Shakespeare",
   Wine: "Şarap",
   Winery: "Şaraphane",
   WishlistSpaceshipIdle: "Wishlist Spaceship Idle",
   Wonder: "Harika",
   WonderBuilderCapacityDescHTML: "<b>İnşaatçı Kapasitesi</b> harikalar inşa ederken harikanın kilidini açan <b>çağ</b> ve <b>teknoloji</b> tarafından etkilenir",
   WondersBuilt: "İnşa Edilen Dünya Harikaları",
   WondersUnlocked: "Dünya Harikalarının Kilidi Açıldı",
   WonderUpgradeLevel: "Wonder Level",
   Wood: "Odun",
   Worker: "İşçi",
   WorkerCapacityMultiplier: "İşçi Kapasite Çarpanı",
   WorkerHappinessPercentage: "Mutluluk Çarpanı",
   WorkerMultiplier: "İşçi Kapasitesi",
   WorkerPercentagePerHappiness: "Her Mutluluk için %{value}% Çarpan",
   Workers: "İşçiler",
   WorkersAvailableAfterHappinessMultiplier: "Mutluluk Çarpanından Sonra İşçiler",
   WorkersAvailableBeforeHappinessMultiplier: "Mutluluk Çarpanından Önce İşçiler",
   WorkersBusy: "Meşgul İşçiler",
   WorkerScienceProduction: "İşçilerin Bilimi Üretimi",
   WorkersRequiredAfterMultiplier: "Gerekli İşçiler",
   WorkersRequiredBeforeMultiplier: "Gerekli İşçi Kapasitesi",
   WorkersRequiredForProductionMultiplier: "İşçi Başına Üretim Kapasitesi",
   WorkersRequiredForTransportationMultiplier: "İşçi Başına Ulaşım Kapasitesi",
   WorkersRequiredInput: "Ulaşım",
   WorkersRequiredOutput: "Üretim",
   WorldWarAge: "Dünya Savaşları",
   WorldWideWeb: "World Wide Web",
   WritersGuild: "Yazarlar Loncası",
   Writing: "Yazma",
   WuZetian: "İmparatoriçe Wu Zetian",
   WuZetianDesc: "+%{value} Taşıma Kapasitesi Çarpanı",
   Xuanzang: "Xuanzang",
   YangtzeRiver: "Yangtze Nehri",
   YangtzeRiverDesc: "All buildings that consume water get +1 Production, Worker Capacity and Storage Multiplier. Double the effect of Zheng He (Great Person). Each level of Permanent Empress Wu Zetian (Great Person) provides +1 Storage Multiplier to all buildings",
   YearOfTheSnake: "Year of the Snake",
   YearOfTheSnakeDesc:
      "After completed, when entering a new age, instead of getting one great person of each unlocked age, get the same amount of great people in the current age. All buildings within 2-tile range get +1 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to buildings within 2-tile range. This wonder can only be constructed during the lunar new year period (1.20 ~ 2.10)",
   YellowCraneTower: "Sarı Vinç Kulesi",
   YellowCraneTowerDesc: "+1 choice when choosing great people. All buildings within 1 tile range get +1 Production, Worker Capacity and Storage Multiplier. When constructed next to Yangtze River, the range increases to 2 tile",
   YuriGagarin: "Yuri Gagarin",
   ZagrosMountains: "Zagros Mountains",
   ZagrosMountainsDesc: "All adjacent buildings that have less than 5 Production Multiplier get +2 Production Multiplier. Double the effect of Nebuchadnezzar II (Great Person)",
   ZahaHadid: "Zaha Hadid",
   ZahaHadidDesc: "+%{value} Builder Capacity Multiplier",
   Zenobia: "Zenobia",
   ZenobiaDesc: "+%{value}s Petra Warp Deposu",
   ZhengHe: "Zheng He",
   ZigguratOfUr: "Ziggurat of Ur",
   ZigguratOfUrDescV2: "Every 10 happiness (capped) provides +1 Production Multiplier to all buildings that do not produce workers and are unlocked in previous ages (max = number of unlocked ages / 2). Wonders (incl. Natural) no longer provide +1 Happiness. The effect can be turned off",
   Zoroaster: "Zoroaster",
   Zugspitze: "Zugspitze",
   ZugspitzeDesc: "For each unlocked age, get one point that can be used to provide one extra level to any Great Person that is born from this run",
};
