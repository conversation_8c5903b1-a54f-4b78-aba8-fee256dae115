export const RU = {
   About: "О CivIdle",
   AbuSimbel: "Абу-Симбел",
   AbuSimbelDesc: "Удваивает эффект Рамзеса II. Все Примыкающие чудеса получают +1 Cчастье",
   AccountActiveTrade: "Активных Заявок",
   AccountChatBadge: "Значок Чата",
   AccountCustomColor: "Пользовательский Цвет",
   AccountCustomColorDefault: "По умолчанию",
   AccountGreatPeopleLevelRequirement: "Требуемый уровень Великих Людей",
   AccountLevel: "Уровень Аккаунта",
   AccountLevelAedile: "Эдил",
   AccountLevelConsul: "Консул",
   AccountLevelMod: "Модератор",
   AccountLevelPlayTime: "Онлайн-время ≥ %{requiredTime} (ваше: %{actualTime})",
   AccountLevelPraetor: "Претор",
   AccountLevelQuaestor: "Квестор",
   AccountLevelSupporterPack: "Обладать Пакетом Поддержки",
   AccountLevelTribune: "Трибун",
   AccountLevelUpgradeConditionAnyHTML: "Для улучшения аккаунта, необходимо соответствовать <b>одному из следующих</b> критериев:",
   AccountPlayTimeRequirement: "Требуемое время игры",
   AccountRankUp: "Повысить ранг аккаунта",
   AccountRankUpDesc: "Весь ваш прогресс будет перенесен на ваш новый ранг",
   AccountRankUpTip: "Поздравляем, ваш аккаунт имеет право на более высокий ранг - нажмите здесь, чтобы повысить его!",
   AccountSupporter: "Обладатель Пакета Поддержки",
   AccountTradePriceRange: "Диапазон Цен Торговли",
   AccountTradeTileReservationTime: "Резервирование Торговой Плитки",
   AccountTradeTileReservationTimeDesc: "Это время, в течение которого ваша Торговая Плитка будет зарезервирована за вами, с тех пор как вы последний раз были онлайн. После окончания периода резервирования, ваша плитка станет доступной для других игроков",
   AccountTradeValuePerMinute: "Торговой Стоимости В Минуту",
   AccountTypeShowDetails: "Показать Детали Аккаунта",
   AccountUpgradeButton: "Улучшить аккаунт до Квестора",
   AccountUpgradeConfirm: "Улучшить Аккаунт",
   AccountUpgradeConfirmDescV2: "Улучшение вашего аккаунта приведет к <b>сбросу вашего текущего Пробега</b> и переносу постоянных Великих Людей в пределах разрешенных уровней. Это <b>нельзя</b> отменить, вы уверены, что хотите продолжить?",
   Acknowledge: "Acknowledge",
   Acropolis: "Акрополь",
   ActorsGuild: "Гильдия Актёров",
   AdaLovelace: "Ада Лавлейс",
   AdamSmith: "Адам Смит",
   AdjustBuildingCapacity: "Производственная Мощность",
   AdvisorElectricityContent:
      "Электростанции предоставляют вам две новые системы. Первая, 'Электроэнергия', обозначена плитками с молниями, прилегающими к электростанции. Некоторые здания (начиная с Радио в эпохе Мировых войн) имеют индикатор 'Требуется Электричество' в списке входных ресурсов. <b>Это означает, что они должны быть построены на плитке с молнией, чтобы функционировать</b>. Здания, которым требуется электроэнергия и которые ее имеют, также будут передавать электроэнергию на плитки, прилегающие к этому зданию, поэтому вы можете питать их друг от друга, пока хотя бы одно из них касается электростанции.<br><br>Другая система 'Электрификация' может быть применена к <b>любому зданию в любом месте</b> на карте, если оно не производит науку или рабочих. Это использует электроэнергию, вырабатываемую электростанцией, для увеличения как потребления, так и производства здания. Больше уровней электрификации требуют все большего и большего количества электроэнергии. Электрификация зданий, которым 'Требуется Электричество', более эффективна, чем электрификация тех, которым не требуется.",
   AdvisorElectricityTitle: "Электроэнергия и Электрификация",
   AdvisorGreatPeopleContent:
      "Каждый раз, когда вы входите в новую эру технологий, вы сможете выбрать Великого человека из этой эпохи и из каждой предыдущей эпохи. Эти Великие люди дают глобальные бонусы, которые могут увеличить производство, науку, счастье и многое другое.<br><br>Эти бонусы являются постоянными для оставшейся части пробега. Когда вы перерождаетесь, все ваши Великие люди становятся постоянными, и их бонус длится вечно.<br><br>Выбор того же человека в более позднем забеге суммирует ваш постоянный и внутриигровой бонус, а когда вы перерождаетесь с дубликатами, дополнительные бонусы сохраняются и могут быть использованы для улучшения постоянного бонуса. Доступ к этому осуществляется в меню <b>Управление постоянными Великими людьми</b> в вашем домашнем здании.",
   AdvisorGreatPeopleTitle: "Великие люди",
   AdvisorHappinessContent:
      "Счастье — это основная механика в CivIdle, которая ограничивает расширение. Вы получаете счастье, открывая новые технологии, переходя в новые эпохи, строя чудеса, от Великих людей, которые его предоставляют, и несколькими другими способами, которые вы можете открыть по мере обучения. <b>Каждое новое здание стоит 1 счастье</b>. За каждое очко выше/ниже 0 счастья вы получаете 2% бонуса или штрафа к общему количеству ваших рабочих (максимум -50 и +50 счастья). Вы можете увидеть подробную статистику вашего счастья в разделе <b>Счастье вашего Домашнего здания</b>.",
   AdvisorHappinessTitle: "Сделайте своих людей счастливыми",
   AdvisorOkay: "Понял, спасибо!",
   AdvisorScienceContent:
      "Ваши занятые работники генерируют науку, которая позволяет вам открывать новые технологии и развивать вашу цивилизацию. Вы можете получить доступ к меню исследований несколькими способами. Нажав на счетчик науки, открыв свои разблокируемые технологии в вашем домашнем здании или используя меню 'Вид'. Все это приведет вас к дереву технологий, показывающему все технологии, а также сколько науки требуется для каждой из них. Если у вас достаточно науки, чтобы изучить новую технологию, просто щелкните по ней и нажмите 'Открыть' в боковом меню. <b>Каждый новый уровень и век технологий требует все больше и больше науки, но вы также откроете новые и лучшие способы получения науки.</b>",
   AdvisorScienceTitle: "Научное открытие!",
   AdvisorSkipAllTutorials: "Пропустить все руководства",
   AdvisorStorageContent:
      "Хотя здания имеют приличный объем хранилища, они могут заполниться, особенно если простаивают долгое время. <b>Когда здания заполнены, они больше не могут производить</b>. Это не всегда проблема, так как у вас явно большой запас, так как здание заполнено. Но поддержание производства, как правило, лучше.<br><br>Один из способов решения проблемы полного хранилища — это склад. Когда вы строите склад, вы получаете меню всех обнаруженных вами продуктов, и вы можете настроить склад на экспорт любых продуктов в любых количествах, пока общее количество всех продуктов находится в пределах того, что склад может экспортировать на основе его уровня и множителя хранилища.<br><br>Простой способ настроить склад — отметить каждый продукт, который вы хотите импортировать на склад, и использовать кнопки 'Распределить между выбранными', чтобы разделить вашу скорость импорта и хранилище поровну. Если вы хотите, чтобы здания также могли извлекать со склада, обязательно включите опцию 'Экспорт ниже Максимума'.",
   AdvisorStorageTitle: "Хранение и склады",
   AdvisorTraditionContent:
      "Некоторые чудеса (Чога Занбиль, Луксорский храм, Биг-Бен) предоставляют доступ к новому набору опций, позволяя вам настраивать путь вашего пробега. Каждое из них позволяет вам выбрать один из 4 вариантов для традиций, религии и идеологии вашей империи соответственно.<br><br>Как только вы выберете одно, этот выбор будет зафиксирован для этого пробега, хотя вы сможете выбрать другие в будущих пробегах. После выбора каждое из них также может быть улучшено несколько раз, предоставляя необходимые ресурсы. Награды на каждом уровне являются накопительными, другими словами если уровень 1 дает +1 производства к X и уровень 2 дает +1 производства к X, то на уровне 2 у вас будет +2 производства к X в общей сложности.",
   AdvisorTraditionTitle: "Выбор путей и улучшаемые чудеса",
   AdvisorWonderContent:
      "Чудеса — это особые здания, которые обеспечивают глобальные эффекты, которые могут оказать значительное влияние на ваш игровой процесс. В дополнение к перечисленным функциям все Чудеса также дают +1 Счастья. Однако вам нужно быть осторожным, так как <b>Чудеса требуют МНОГО ресурсов и имеют более высокую, чем обычно, Мощность Строительства</b>. Это означает, что они могут легко очистить ваши запасы необходимых ресурсов, оставив другие ваши здания простаивать. <b>Вы можете свободно включать и выключать каждый ресурс</b>, что позволяет вам строить его поэтапно, пока вы накапливаете достаточно материалов, чтобы все работало.",
   AdvisorWonderTitle: "Чудеса света",
   AdvisorWorkerContent:
      "Каждый раз, когда здание производит или транспортирует товары, для этого требуются работники. Если у вас недостаточно работников, некоторые здания не смогут запустить этот цикл. Очевидным решением этой проблемы является увеличение общего количества доступных работников путем строительства или модернизации зданий, которые производят работников (Хижина/Дом/Апартаменты/Жилой комплекс).<br><br><b>Однако имейте в виду, что здания отключаются во время модернизации и не могут предоставлять никаких своих ресурсов, включая работников, поэтому вам, возможно, захочется модернизировать только одно жилое здание за раз.</b> Хорошей целью на ранних этапах игры является поддержание занятости примерно 70% ваших работников. Если занято более 70%, модернизируйте/стройте жилье. Если занято менее 70%, расширяйте производство.",
   AdvisorWorkerTitle: "Управление работниками",
   Aeschylus: "Эсхил",
   Agamemnon: "Агамемнон",
   AgeWisdom: "Мудрость Эпох",
   AgeWisdomDescHTML: "Каждый уровень Мудрости Эпох обеспечивает <b>эквивалентный уровень</b> подходящих Постоянных Великих Людей этой эпохи — его можно улучшить с помощью подходящих осколков Постоянных Великих Людей.",
   AgeWisdomGreatPeopleShardsNeeded: "Для следующего повышения «Мудрости Эпох» вам нужно ещё %{amount} осколков Великих Людей",
   AgeWisdomGreatPeopleShardsSatisfied: "У вас достаточно осколков Великих Людей для следующего улучшения Мудрости Эпох",
   AgeWisdomNeedMoreGreatPeopleShards: "Нужно больше осколков Великих Людей",
   AgeWisdomNotEligible: "Этот Великий Человек не подходит для Мудрости Эпох",
   AgeWisdomSource: "%{age} Мудрость: %{person}",
   AgeWisdomUpgradeWarningHTMLV3: "Age Wisdom <b>does not carry over</b> when upgrading from Tribune to Quaestor",
   AGreatPersonIsBorn: "Рождается Великий Человек",
   AircraftCarrier: "Авианосец",
   AircraftCarrierYard: "Верфь Авианосцев",
   Airplane: "Самолёт",
   AirplaneFactory: "Авиационный Завод",
   Akitu: "Акиту: Зиккурат Ура и река Евфрат применяются к зданиям, разблокированным в текущей эпохе",
   AlanTuring: "Алан Тьюринг",
   AlanTuringDesc: "+%{value} Науки от Незанятых Работников",
   AlbertEinstein: "Альберт Эйнштейн",
   Alcohol: "Алкоголь",
   AldersonDisk: "Диск Олдерсона",
   AldersonDiskDesc: "+25 Счастья. Чудо можно улучшить, +5 Счастья за каждый уровень улучшения",
   Alloy: "Сплав",
   Alps: "Альпы",
   AlpsDesc: "За каждый 10-й уровень здания получают +1 к Мощности Производства (+1 к Множителям Потребления и Производства)",
   Aluminum: "Алюминий",
   AluminumSmelter: "Алюминиевый Завод",
   AmeliaEarhart: "Амелия Эрхарт",
   American: "Америка",
   AndrewCarnegie: "Эндрю Карнеги",
   AngkorWat: "Ангкор-Ват",
   AngkorWatDesc: "Все примыкающие здания получают +1 к Множителю Производительности Работников. Производит 1000 Работников",
   AntiCheatFailure: "Рейтинг вашего аккаунта был ограничен из-за <b>непрохождения античит-проверки</b>. Свяжитесь с разработчиком, если хотите подать аппеляцию",
   AoiMatsuri: "Аой Мацури: гора Фудзи генерирует двойное количество Временного Разрыва",
   Apartment: "Апартаменты",
   Aphrodite: "Афродита",
   AphroditeDescV2: "+1 к Множителю Мощности Строительства за каждый уровень при улучшении зданий выше 20-го уровня. Все разблокированные постоянные великие люди Классической эпохи получают +1 уровень в этом забеге.",
   ApolloProgram: "Программа Аполлон",
   ApolloProgramDesc: "Все Ракетные Заводы получают +2 к Множителям Производства, Производительности Работников и Хранилища. Спутниковые Заводы, Заводы Космических Кораблей и Шахты Ядерных Ракет получают +1 к Множителю Производства за каждый примыкающий Ракетный завод",
   ApplyToAll: "Применить Для Всех",
   ApplyToAllBuilding: "Применить ко всем %{building}",
   ApplyToBuildingInTile: "Применить ко всем %{building} в радиусе %{tile} плиток",
   ApplyToBuildingsToastHTML: "Успешно применено к <b>%{count} %{building}</b>",
   Aqueduct: "Акведук",
   ArcDeTriomphe: "Триумфальная Арка",
   ArcDeTriompheDescV2: "За каждую единицу счастья (до максимума) даётся +1 вместимость строителей ко всем зданиям",
   Archimedes: "Архимед",
   Architecture: "Архитектура",
   Aristophanes: "Аристофан",
   AristophanesDesc: "+%{value} Счастья",
   Aristotle: "Аристотель",
   Arithmetic: "Арифметика",
   Armor: "Доспехи",
   Armory: "Арсенал",
   ArtificialIntelligence: "Искуственный Интеллект",
   Artillery: "Артиллерия",
   ArtilleryFactory: "Завод Артиллерии",
   AshokaTheGreat: "Ашока",
   Ashurbanipal: "Ашшурбанапал",
   Assembly: "Установка",
   Astronomy: "Астрономия",
   AtomicBomb: "Атомная Бомба",
   AtomicFacility: "Атомный Завод",
   AtomicTheory: "Атомная Теория",
   Atomium: "Атомиум",
   AtomiumDescV2:
      "Все производящие Науку здания (исключая Лаборатории клонирования) в радиусе 2 Плиток получают +5 к Множителю Производства. Производит Науку, эквивалентно производству Науки в пределах 2 Плиток. По завершении постройки единовременно даёт Науку, эквивалентную стоимости самой дорогой разблокированной Технологии.",
   Autocracy: "Автократия",
   Aviation: "Авиация",
   Babylonian: "Вавилон",
   BackToCity: "Вернуться в город",
   BackupRecovery: "Восстановление Резервной Копии",
   Bakery: "Пекарня",
   Ballistics: "Баллистика",
   Bank: "Банк",
   Banking: "Банковское Дело",
   BankingAdditionalUpgrade: "Все здания 10-го уровня или выше получают +1 к Множителю Хранилища",
   Banknote: "Банкнота",
   BaseCapacity: "Базовая Мощность",
   BaseConsumption: "Базовое Потребление",
   BaseMultiplier: "Базовый Множитель",
   BaseProduction: "Базовое Производство",
   BastilleDay: "День взятия Бастилии: Удваивает эффект от Центра Помпиду и Триумфальной арки. Удваивает производство культуры от Мон Сен-Мишеля",
   BatchModeTooltip: "%{count} зданий выбрано. Улучшение будет применено ко всем выбранным зданиям",
   BatchSelectAllSameType: "Все того же Типа",
   BatchSelectAnyType1Tile: "Любого Типа в радиусе 1 Плитки",
   BatchSelectAnyType2Tile: "Любого Типа в радиусе 2 Плиток",
   BatchSelectAnyType3Tile: "Любого Типа в радиусе 3 Плиток",
   BatchSelectSameType1Tile: "Того же Типа в радиусе 1 Плитки",
   BatchSelectSameType2Tile: "Того же Типа в радиусе 2 Плиток",
   BatchSelectSameType3Tile: "Того же Типа в радиусе 3 Плиток",
   BatchSelectSameTypeSameLevel: "Того же Типа и уровня",
   BatchSelectThisBuilding: "Это здание",
   BatchStateSelectActive: "Активные",
   BatchStateSelectAll: "Все",
   BatchStateSelectTurnedFullStorage: "Полное хранилище",
   BatchStateSelectTurnedOff: "Выключено",
   BatchUpgrade: "Массовое улучшение",
   Battleship: "Линкор",
   BattleshipBuilder: "Верфь Линкоров",
   BigBen: "Биг-Бен",
   BigBenDesc: "+2 Науки от занятых Работников. Выберите идеологию империи, разблокирует больше усилений с каждым выбором",
   Biplane: "Биплан",
   BiplaneFactory: "Завод Бипланов",
   Bitcoin: "Биткоин",
   BitcoinMiner: "Биткоин майнер",
   BlackForest: "Чёрный лес",
   BlackForestDesc: "При обнаружении на карте появляются все деревянные плитки. На соседних плитках появляется дерево. Все здания, которые используют древесину или доски, получают +5 к Множителю производсва",
   Blacksmith: "Кузница",
   Blockchain: "Блокчейн",
   BlueMosque: "Blue Mosque",
   BlueMosqueDesc: "All wonders provide +1 Production, Worker Capacity and Storage Multiplier to adjacent buildings. When constructed next to Hagia Sophia, provide extra +1 Production, Worker Capacity and Storage Multiplier",
   BobHope: "Боб Хоуп",
   BobHopeDesc: "+%{value} Счастья",
   Bond: "Облигация",
   BondMarket: "Рынок Облигаций",
   Book: "Книга",
   BoostCyclesLeft: "Циклов осталось",
   BoostDescription: "+%{value} %{multipliers} для %{buildings}",
   Borobudur: "Боробудур",
   BorobudurDesc: "Боробудур",
   BranCastle: "Замок Бран",
   BranCastleDesc: "Замок Бран",
   BrandenburgGate: "Бранденбургские Ворота",
   BrandenburgGateDesc: "Все Угольные Шахты и Нефтяные Скважины получают +1 к Множителям Производства, Производительности Работников и Хранилища. Нефтеперерабатывающие Заводы получают +1 к Множителям Производства, Хранилища и Производительности Работников за каждую примыкающую плитку с Нефтью",
   Bread: "Хлеб",
   Brewery: "Пивоварня",
   Brick: "Кирпич",
   Brickworks: "Кирпичный Завод",
   BritishMuseum: "Британский музей",
   BritishMuseumChooseWonder: "Выберите чудо",
   BritishMuseumDesc: "После постройки может превратиться в уникальное чудо других цивилизаций",
   BritishMuseumTransform: "Трансформировать",
   Broadway: "Бродвей",
   BroadwayCurrentlySelected: "Текущее выбранное",
   BroadwayDesc: "Родится великий человек текущей и предыдущей эпох. Выберите великого человека и удвойте его/её эффект",
   BronzeAge: "Бронзовый Век",
   BronzeTech: "Бронза",
   BuddhismLevelX: "Буддизм %{level}",
   Build: "Построить",
   BuilderCapacity: "Мощность Строительства",
   BuildingColor: "Цвет Здания",
   BuildingColorMatchBuilding: "Скопировать Цвет из Здания",
   BuildingColorMatchBuildingTooltip: "Копирует цвет постройки, которая производит этот ресурс. Если есть более одного варианта, выбирает случайным образом",
   BuildingDefaults: "Настройки Зданий По Умолчанию",
   BuildingDefaultsCount: "%{count} настроек переопределяются в здании по умолчанию",
   BuildingDefaultsRemove: "Очистить все настройки",
   BuildingEmpireValue: "Стоимость Империи от Зданий / Стоимость Империи от Ресурсов",
   BuildingMultipliers: "Усиление",
   BuildingName: "Название",
   BuildingNoMultiplier: "%{building} <b>не зависит</b> ни от каких мультипликаторов (Производство, количество Работников, Хранилище и т.д.)",
   BuildingSearchText: "Введите название здания или ресурса для поиска",
   BuildingTier: "Тир",
   Cable: "Кабель",
   CableFactory: "Завод Кабелей",
   Calendar: "Календарь",
   CambridgeUniversity: "Кембриджский университет",
   CambridgeUniversityDesc: "+1 уровень Мудрости эпох для эпохи Возрождения и последующих эпох",
   CambridgeUniversitySource: "Кембриджский университет (%{age})",
   Cancel: "Отменить",
   CancelAllUpgradeDesc: "Отменить все улучшения %{building}",
   CancelUpgrade: "Отменить Улучшение",
   CancelUpgradeDesc: "Все ресурсы, которые уже были перемещены, останутся в хранилище",
   Cannon: "Пушка",
   CannonWorkshop: "Пушечная Мастерская",
   CannotEarnPermanentGreatPeopleDesc: "Так как это пробный пробег, постоянные великие люди не могут быть получены",
   Capitalism: "Капитализм",
   Cappadocia: "Cappadocia",
   CappadociaDesc: "All buildings within 3 tile range get +1 Production, Worker Capacity and Storage Multiplier for every level above Level 30",
   Car: "Машина",
   Caravansary: "Базар",
   CaravansaryDesc: "Позволяет обменивайться ресурсами с другими игроками и предоставляет дополнительное Хранилище",
   Caravel: "Каравелла",
   CaravelBuilder: "Верфь Каравелл",
   CarFactory: "Завод Машин",
   CarlFriedrichGauss: "Карл Фридрих Гаусс",
   CarlFriedrichGaussDesc: "+%{idle} Науки от Незанятых Работников. +%{busy} Науки от Занятых Работников",
   CarlSagan: "Карл Саган",
   Census: "Перепись Населения",
   CentrePompidou: "Центр Помпиду",
   CentrePompidouDesc:
      "После постройки все здания получают +1 к множителю Производства и +2 к множителю Хранилища. Чудо сохранится, если текущий забег достигнет Информационной эпохи, а следующий забег - другой цивилизации. Чудо получает +1 уровень при перерождении за каждый забег, который достигает Информационной эпохи с уникальной цивилизацией. Каждый уровень дает +1 к множителю Производства и +2 к множителю Хранения. Стоимость этого чуда не включена в общую стоимость империи, и Британский Музей не может трансформироваться в это чудо",
   CentrePompidouWarningHTML: "Центр Помпиду исчезнет, если вы переродитесь как <b>%{civ}</b>",
   CerneAbbasGiant: "Гигант Кернеабаса",
   CerneAbbasGiantDesc: "Рождается великий человек текущей эпохи, когда завершается постройка этого чуда",
   ChangePlayerHandle: "Изменить",
   ChangePlayerHandleCancel: "Отменить",
   ChangePlayerHandledDesc: "Выберите уникальное имя игрока длиной от 5 до 16 символов. Ваше имя игрока может содержать только буквы и цифры",
   Chariot: "Колесница",
   ChariotWorkshop: "Колесничная Мастерская",
   Charlemagne: "Карл Великий",
   CharlesDarwin: "Чарльз Дарвин",
   CharlesDarwinDesc: "+%{value} Науки от Занятых Работников",
   CharlesMartinHall: "Чарльз Мартин Холл",
   CharlesParsons: "Чарльз Парсонс",
   CharlieChaplin: "Чарльз Чаплин",
   CharlieChaplinDesc: "+%{value} Счастья",
   Chat: "Чат",
   ChatChannel: "Канал Чата",
   ChatChannelLanguage: "Язык",
   ChatHideLatestMessage: "Скрыть Содержание Последнего Сообщения",
   ChatNoMessage: "Нет Сообщений Чата",
   ChatReconnect: "Отсоединено, подключение...",
   ChatSend: "Отправить",
   CheckInAndExit: "Сохранить и выйти",
   CheckInCloudSave: "Загрузить сохранение в облако",
   CheckOutCloudSave: "Загрузить облачное сохранение",
   Cheese: "Сыр",
   CheeseMaker: "Сыроварня",
   Chemistry: "Химия",
   ChesterWNimitz: "Честер Уильям Нимиц",
   ChichenItza: "Чичен-Ица",
   ChichenItzaDesc: "Все прилегающие постройки получают +1 к Множителям Производства, Производительности Работников и Хранилища",
   Chinese: "Китай",
   ChoghaZanbil: "Чога Занбиль",
   ChoghaZanbilDescV2: "Выберите традицию вашей империи, открывает больше улучшений с каждым выбором",
   ChooseGreatPersonChoicesLeft: "У вас осталось %{count} выборов",
   ChristianityLevelX: "Христианство %{level}",
   Church: "Церковь",
   CircusMaximus: "Большой Цирк",
   CircusMaximusDescV2: "+5 Счастья. Все Гильдии Музыкантов, Гильдии Писателей и Гильдии Художников получают +1 к Множителям Производства и Хранилища",
   CityState: "Город-Государство",
   CityViewMap: "Город",
   CivGPT: "ЦивGPT",
   CivIdle: "СivIdlе",
   CivIdleInfo: "С гордостью представлено Fish Pond Studio",
   Civilization: "Цивилизация",
   CivilService: "Госслужба",
   CivOasis: "ЦивОазис",
   CivTok: "ЦивТок",
   ClaimedGreatPeople: "Полученные Великие Люди",
   ClaimedGreatPeopleTooltip: "Вы заработали %{total} Великих Людей за Перерождение, %{claimed} из них уже получены",
   ClassicalAge: "Классический Век",
   ClearAfterUpdate: "Очищать Всю Торговлю После Обновления Рынка",
   ClearSelected: "Очистить Выбранное",
   ClearSelection: "Очистить",
   ClearTransportPlanCache: "Очистить кэш транспортного плана",
   Cleopatra: "Клеопатра",
   CloneFactory: "Фабрика клонирования",
   CloneFactoryDesc: "Клонирует любой ресурс",
   CloneFactoryInputDescHTML: "Фабрика клонирования может клонировать только <b>%{res}</b> напрямую доставляемый из <b>%{buildings}</b>",
   CloneLab: "Лаборатория клонирования",
   CloneLabDesc: "Преобразует любой ресурс в Науку",
   CloneLabScienceMultiplierHTML: "Множители производства, которые <b>применяются только к научным производственным зданиям</b> (например, множители производства от Атомиума), <b>не применяются</b> к Лаборатории клонирования",
   Cloth: "Ткань",
   CloudComputing: "Облачные Вычисления",
   CloudSaveRefresh: "Обновить",
   CloudSaveReturnToGame: "Вернуться к игре",
   CNTower: "Си-Эн Тауэр",
   CNTowerDesc: "Все Киностудии, Радиостанции и Телеканалы освобождаются от -1 Счастья. Все здания, открытые в эпохи Мировых Войн и Холодной Войны получают +N к Множителям Производства, Производительности Работников и Хранилища. N = разница между Эпохой и Тиром здания",
   Coal: "Уголь",
   CoalMine: "Угольная Шахта",
   CoalPowerPlant: "Угольная Электростанция",
   Coin: "Монета",
   CoinMint: "Монетный Двор",
   ColdWarAge: "Холодная Война",
   CologneCathedral: "Кельнский Собор",
   CologneCathedralDesc:
      "После завершения постройки единовременно даёт науку, эквивалентной стоимости самой дорогой технологии в текущей эпохе. Все здания, производящие науку (за исключением Лаборатории клонирования), получают +1 к Множителю производства. Это чудо можно улучшить, за каждое улучшение даёт +1 к Множителю производства для всех зданий производящих науку (за исключением Лаборатории клонирования)",
   Colonialism: "Колониализм",
   Colosseum: "Колизей",
   ColosseumDescV2: "Колесничные мастерские освобождены от -1 Счастья. Потребляет 10 колесниц и производит 10 Счастья. +2 Счастья за каждую разблокированную эпоху",
   ColossusOfRhodes: "Колосс Родосский",
   ColossusOfRhodesDesc: "Все прилегающие здания, не производящие Работников, получают +1 Счастье",
   Combustion: "Горение",
   Commerce4UpgradeHTMLV2: "При разблокировании все <b>соседние банки</b> получают бесплатное улучшение до <b>30 Уровня</b>",
   CommerceLevelX: "Коммерция %{level}",
   Communism: "Коммунизм",
   CommunismLevel4DescHTML: "Родится великий человек <b>Промышленной эпохи</b> и великий человек <b>эпохи Мировых войн</b>",
   CommunismLevel5DescHTML: "Родится великий человек <b>эпохи Холодной войны</b>. При переходе в новую эпоху получите <b>2 дополнительных</b> великих человека этой эпохи",
   CommunismLevelX: "Коммунизм %{level} уровня",
   Computer: "Компьютер",
   ComputerFactory: "Компьютерный Завод",
   ComputerLab: "Компьютерная лаборатория",
   Concrete: "Бетон",
   ConcretePlant: "Цементный Завод",
   Condo: "Жилой Комплекс",
   ConfirmDestroyResourceContent: "Вы собираетесь уничтожить %{amount} %{resource}. Это нельзя отменить",
   ConfirmNo: "Нет",
   ConfirmYes: "Да",
   Confucius: "Конфуций",
   ConfuciusDescV2: "+%{value} Науки от всех Работников если более 50% Работников заняты и менее 50% Занятых Работников заняты Транспортировкой",
   ConnectToADevice: "Подключиться к Устройству",
   Conservatism: "Консерватизм",
   ConservatismLevelX: "Консерватизм %{level} уровня",
   Constitution: "Конституция",
   Construction: "Строительство",
   ConstructionBuilderBaseCapacity: "Базовая Мощность",
   ConstructionBuilderCapacity: "Мощность Строительства",
   ConstructionBuilderMultiplier: "Множитель Мощности",
   ConstructionBuilderMultiplierFull: "Множитель Мощности Строительства",
   ConstructionCost: "Стоимость Строительства: %{cost}",
   ConstructionDelivered: "Доставлено",
   ConstructionPriority: "Приоритет Строительства",
   ConstructionProgress: "Прогресс",
   ConstructionResource: "Ресурс",
   Consume: "Потребляет",
   ConsumeResource: "Потребляет: %{resource}",
   ConsumptionMultiplier: "Множитель Потребления",
   ContentInDevelopment: "Контент в Разработке",
   ContentInDevelopmentDesc: "Этот игровой контент все еще находится в разработке и будет доступен в будущем обновлении игры, следите за новостями!",
   Copper: "Медь",
   CopperMiningCamp: "Меднодобывающий Лагерь",
   CosimoDeMedici: "Козимо де Медичи",
   Cotton: "Хлопок",
   CottonMill: "Хлопчатобумажный Комбинат",
   CottonPlantation: "Хлопковая Плантация",
   Counting: "Счёт",
   Courthouse: "Здание Суда",
   CristoRedentor: "Статуя Христа-Искупителя",
   CristoRedentorDesc: "Все здания в радиусе 2 Плиток освобождаются от -1 Счастья",
   CrossPlatformAccount: "Кросс-платформенная Учетная запись",
   CrossPlatformConnect: "Подключение",
   CrossPlatformSave: "Кросс-платформенное сохранение",
   CrossPlatformSaveLastCheckIn: "Последний вход в игру",
   CrossPlatformSaveStatus: "Текущий статус",
   CrossPlatformSaveStatusCheckedIn: "Вход выполнен",
   CrossPlatformSaveStatusCheckedOut: "Выход выполнен на %{platform}",
   CrossPlatformSaveStatusCheckedOutTooltip: "Ваше кросс-платформенное сохранение было создано на другой платформе, вам необходимо зарегистрироваться на этой платформе, прежде чем вы сможете войти на этой платформе",
   Cultivation4UpgradeHTML: "Рождение великого человека <b>Эпохи Ренессанса</b>",
   CultivationLevelX: "Культивация %{level}",
   Culture: "Культура",
   Culus: "Cülus: Double the effect of Cappadocia. Mount Ararat's effect becomes based on square root of Effective Great People Level, instead of cubic root",
   CurrentLanguage: "Русский",
   CurrentPlatform: "Current Platform",
   CursorBigOldFashioned: "3D (Большой)",
   CursorOldFashioned: "3D",
   CursorStyle: "Стиль курсора",
   CursorStyleDescHTML: "Сменить стиль курсора. <b>Изменения вступят в силу после перезапуска игры</b>",
   CursorSystem: "Системный",
   Cycle: "Цикл",
   CyrusII: "Кир II",
   DairyFarm: "Молочная ферма",
   DefaultBuildingLevel: "Уровень здания по умолчанию",
   DefaultConstructionPriority: "Приоритет Строительства по умолчанию",
   DefaultProductionPriority: "Приоритет Производства по умолчанию",
   DefaultStockpileMax: "Максимальный Запас по умолчанию",
   DefaultStockpileSettings: "Входная Мощность Хранилища по умолчанию",
   DeficitResources: "Недостаток Ресурсов",
   Democracy: "Демократия",
   DemolishAllBuilding: "Снести Все %{building} в пределах %{tile} Плитки",
   DemolishAllBuildingConfirmContent: "Вы уверены, что хотите снести %{count} %{name}?",
   DemolishAllBuildingConfirmTitle: "Снести %{count} Зданий?",
   DemolishBuilding: "Снести здание",
   DennisRitchie: "Деннис Ричи",
   Deposit: "Месторождение",
   DepositTileCountDesc: "%{count} Плиток(а/и) %{deposit} может быть найдено в %{city}",
   Dido: "Дидона",
   Diplomacy: "Дипломатия",
   DistanceInfinity: "Не ограничено",
   DistanceInTiles: "Дистанция (В Плитках)",
   DolmabahcePalace: "Dolmabahçe Palace",
   Drilling: "Бурение",
   DukeOfZhou: "Герцог Чжоу",
   DuneOfPilat: "Дюна Пилата",
   DuneOfPilatDesc: "В каждой эпохе удваивает мудрость этох для предыдущей эпохи",
   DynamicMultiplierTooltip: "Динамический множитель - не влияет на Рабочих и Хранилище",
   Dynamite: "Динамит",
   DynamiteWorkshop: "Мастерская Динамита",
   DysonSphere: "Сфера Дайсона",
   DysonSphereDesc: "Все постройки получают +5 к Множителю Производства. Чудо можно улучшить, +1 к Множителю Производства за каждый уровень улучшения всем постройкам",
   EasterBunny: "Пасхальный Кролик",
   EasterBunnyDesc: "Когда построено, 10% дополнительных Великих Людей из этого забега будут перенесены в следующий забег после перерождения, и родятся после постройки Пасхального Кролика в новом забеге. Это чудо может быть построено только в течение Апреля.",
   EastIndiaCompany: "Ост-Индская компания",
   EastIndiaCompanyDescV2:
      "Это чудо накапливает общую стоимость совершенных вами торговых операций между игроками. За каждые 2000 торговой стоимости все здания, прилегающие к Базарам, получают +0.5 к Множителю производства на 1 цикл. Это чудо можно улучшить, за каждое улучшение дает дополнительный +0.5 к Множителю производства. Торговая операция засчитывается, когда вы выполняете торговый запрос другого игрока или когда выполняется ваш собственный торговый запрос. Усиления суммируются, увеличивая продолжительность",
   Education: "Образование",
   EffectiveGreatPeopleLevel: "Уровень эффективности великих людей",
   EffectiveGreatPeopleLevelDesc: "Уровень эффективности великих людей - это сумма всех постоянных великих людей и уровня Мудрости эпох. Он измеряет эффект, который дают великие люди и Мудрость веков",
   Egyptian: "Египет",
   EiffelTower: "Эйфелева Башня",
   EiffelTowerDesc: "Все прилегающие Сталелитейные Заводы получают +N к Множителям Производства, Производительности Работников и Хранилища. N = Число прилегащих Сталелитейных Заводов",
   Elbphilharmonie: "Эльбская Филармония",
   ElbphilharmonieDesc: "Все здания в пределах 3 плиток получают +1 к Множителю производства за каждое соседнее работающее здание, имеющее другой уровень",
   Electricity: "Электричество",
   Electrification: "Электрификация",
   ElectrificationPowerRequired: "Требуется Электричество",
   ElectrificationStatusActive: "Активно",
   ElectrificationStatusDesc: "Электрифицировать можно как здания, требующие электроэнергии, так и здания, не требующие электроэнергии. Однако электрификация зданий, которым требуется электроэнергия, более эффективна.",
   ElectrificationStatusNoPowerV2: "Недостаточно Электроэнергии",
   ElectrificationStatusNotActive: "Не Активно",
   ElectrificationStatusV2: "Статус Электрификации",
   ElectrificationUpgrade: "Разблокирует Электрификацию. Позволяет зданиям потреблять Электроэнергию для увеличения Мощности Производства",
   Electrolysis: "Электролиз",
   ElvisPresley: "Элвис Пресли",
   ElyseePalace: "Елисейский Дворец",
   EmailDeveloper: "Написать разработчику",
   Embassy: "Посольство",
   EmperorWuOfHan: "Император У-ди",
   EmpireValue: "Стоимость Империи",
   EmpireValueByHour: "Стоимость империи по часам",
   EmpireValueFromBuilding: "Стоимость Империи от Зданий",
   EmpireValueFromBuildingsStat: "От Зданий",
   EmpireValueFromResources: "От Ресурсов",
   EmpireValueFromResourcesStat: "От Ресурсов",
   EmpireValueIncrease: "Увеличение стоимости империи",
   EmptyTilePageBuildLastBuilding: "Повторить последнюю постройку",
   EndConstruction: "Прекратить Строительство",
   EndConstructionDescHTML: "Когда вы прекращаете строительство, все ресурсы, которые уже были использованы, <b>не будут возвращены</b>",
   Engine: "Двигатель",
   Engineering: "Инженерия",
   English: "Английский",
   Enlightenment: "Просвещение",
   Enrichment: "Обогащение",
   EnricoFermi: "Энрико Ферми",
   EstimatedTimeLeft: "Осталось примерно",
   EuphratesRiver: "Река Евфрат",
   EuphratesRiverDesc:
      "Каждые 10% занятых рабочих, занятых производством (не транспортировкой), дают +1 Множитель Производства всем зданиям, которые не производят рабочих и были разблокированы в предыдущей эпохе (максимум = количество разблокированных эпох / 2). Когда рядом с ним строятся Висячие Сады, то Висячие Сады получают +1 эффект за каждую эпоху после их разблокировки. При обнаружении порождает воду на всех соседних плитках, на которых нет месторождений.",
   ExpansionLevelX: "Расширение %{level}",
   Exploration: "Разведка",
   Explorer: "Исследователь",
   ExplorerRangeUpgradeDesc: "Увеличивает радиус исследователей на %{range}",
   ExploreThisTile: "Отправить Исследователя",
   ExploreThisTileHTML: "Исследователь исследует <b>эту Плитку и прилегающие к ней Плитки</b>. Исследователи появляются в %{name}. У вас осталось %{count} Исследователей",
   ExtraGreatPeople: "%{count} Дополнительных Великих Людей",
   ExtraGreatPeopleAtReborn: "Дополнительные Великие Люди После Перерождения",
   ExtraTileInfoType: "Дополнительная информация о Плитке",
   ExtraTileInfoTypeDesc: "Выберите, какая информация будет отображаться на каждой Плитке",
   ExtraTileInfoTypeEmpireValue: "Стоимость для Империи",
   ExtraTileInfoTypeNone: "Ничего",
   ExtraTileInfoTypeStoragePercentage: "Заполненность Хранилища",
   Faith: "Вера",
   Farming: "Фермерство",
   FavoriteBuildingAdd: "Добавить в Избранное",
   FavoriteBuildingEmptyToast: "У вас нет избранных зданий",
   FavoriteBuildingRemove: "Удалить из Избранного",
   FeatureRequireQuaestorOrAbove: "Для этой функции требуется ранг Квестора или выше.",
   Festival: "Фестиваль",
   FestivalCycle: "Цикл Фестиваля",
   FestivalTechTooltipV2: "Положительное Счастье (макс. 50) конвертируется в Фестивальные Очки. За каждые %{point} Фестивальных Очков ваша империя входит в Цикл Фестиваля, предоставляя значительный бонус, специфичный для карты. Фестиваль на этой карте %{desc}",
   FestivalTechV2: "Разблокировать Фестиваль - положительное Счастье (макс. 50) конвертируется в Фестивальные Очки. За каждые %{point} Фестивальных Очков ваша империя входит в Цикл Фестиваля, предоставляя значительный бонус, специфичный для карты",
   Feudalism: "Феодализм",
   Fibonacci: "Фибоначчи",
   FibonacciDescV2: "+%{idle} Науки от Незанятых Работников. +%{busy} Науки от Занятых Работников. Стоимость постоянного улучшения следует последовательности Фибоначчи",
   FighterJet: "Истребитель",
   FighterJetPlant: "Завод Истребителей",
   FilterByAge: "Отфильтрофать по Эпохе",
   FinancialArbitrage: "Финансовый Арбитраж",
   FinancialLeverage: "Финансовый Рычаг",
   Fire: "Огонь",
   Firearm: "Огнестрельное Оружие",
   FirstTimeGuideNext: "Дальше",
   FirstTimeTutorialWelcome: "Добро пожаловать в СivIdlе",
   FirstTimeTutorialWelcome1HTML:
      "Добро пожаловать в СivIdlе. В этой игре вы будете управлять собственной империей: <b>управлять производством, открывать технологии, торговать ресурсами с другими игроками, создавать великих людей и строить чудеса света</b>.<br><br>Перемещайтесь с помощью мыши. Используйте колесо прокрутки для увеличения или уменьшения масштаба. Щелкните по пустой плитке, чтобы построить новые здания, щелкните по зданию, чтобы осмотреть его.<br><br>Некоторые здания, такие как Каменный Карьер и Лесозаготовительный лагерь, необходимо строить поверх плитки с ресурсами. Я рекомендую размещать Хижину, которая обеспечивает рабочими, рядом с туманом — строительство здания займет некоторое время. После завершения строительства она развеет туман поблизости.",
   FirstTimeTutorialWelcome2HTML:
      "Здания можно улучшать — это требует ресурсов и времени. Пока здание улучшается, <b>оно больше не будет производить</b>. Это касается и зданий, которые предоставляют рабочих, <b>поэтому никогда не улучшайте все свои здания одновременно!</b><br><br>По мере роста вашей империи вы получите больше науки и откроете новые технологии. Я расскажу вам об этом подробнее, когда мы доберемся до этого, но вы можете перейти в Вид -> Исследования, чтобы быстро взглянуть<br><br>",
   FirstTimeTutorialWelcome3HTML: "Теперь, когда вы знаете все основы игры, вы можете начать строить свою империю. Но прежде чем я вас отпущу, вы должны <b>выбрать себе никнейм</b> и поздороваться в игровом чате. У нас удивительно полезное сообщество: если вы запутались, не бойтесь спрашивать!",
   Fish: "Рыба",
   FishPond: "Рыбный Пруд",
   FlorenceNightingale: "Флоренс Найтингейл",
   FlorenceNightingaleDesc: "+%{value} Счастья",
   Flour: "Мука",
   FlourMill: "Мельница",
   FontSizeScale: "Изменение размера шрифта",
   FontSizeScaleDescHTML: "Изменение размера шрифта в пользовательском интерфейсе игры. <b>Установка масштаба больше 1x может сломать интерфейс</b>",
   ForbiddenCity: "Запретный Город",
   ForbiddenCityDesc: "Все Заводы Бумаги, Гильдии Писателей и Типографии получают +1 к Множителям Производства, Производительности Работников и Хранилища",
   Forex: "Валюта",
   ForexMarket: "Валютная Биржа",
   FrankLloydWright: "Фрэнк Ллойд Райт",
   FrankLloydWrightDesc: "+%{value} к Множителю Мощности Строительства",
   FrankWhittle: "Фрэнк Уиттл",
   FreeThisWeek: "Бесплатно на этой неделе",
   FreeThisWeekDescHTMLV2: "<b>Каждую неделю</b> можно бесплатно играть в одну из премиальных цивилизаций. Бесплатная цивилизация на этой неделе - <b>%{city}</b>",
   French: "Франция",
   Frigate: "Фрегат",
   FrigateBuilder: "Верфь Фрегатов",
   Furniture: "Мебель",
   FurnitureWorkshop: "Мебельная Мастерская",
   Future: "Будущее",
   GabrielGarciaMarquez: "Габриэль Гарсиа Маркес",
   GabrielGarciaMarquezDesc: "+%{value} Счастья",
   GalileoGalilei: "Галилео Галилей",
   GalileoGalileiDesc: "+%{value} Науки От Незанятых Работников",
   Galleon: "Галеон",
   GalleonBuilder: "Верфь Галеонов",
   Gameplay: "Геймплей",
   Garment: "Одежда",
   GarmentWorkshop: "Швейная Мастерская",
   GasPipeline: "Газопровод",
   GasPowerPlant: "Газовая Электростанция",
   GatlingGun: "Гатлинг",
   GatlingGunFactory: "Фабрика Гатлингов",
   Genetics: "Генетика",
   Geography: "География",
   GeorgeCMarshall: "Джордж К. Маршалл",
   GeorgeWashington: "Джордж Вашингтон",
   GeorgiusAgricola: "Георгий Агрикола",
   German: "Германия",
   Glass: "Стекло",
   Glassworks: "Стекольный Завод",
   GlobalBuildingDefault: "Настройки Зданий по умолчанию",
   Globalization: "Глобализация",
   GoBack: "Вернуться назад",
   Gold: "Золото",
   GoldenGateBridge: "Мост Золотые Ворота",
   GoldenGateBridgeDesc: "Все Электростанции получают +1 к Множителю Производства. Обеспечивает Электроэнергией все Плитки в радиусе 2",
   GoldenPavilion: "Золотой Павильон",
   GoldenPavilionDesc: "Все здания в радиусе 3 клеток получают +1 к Множителю Производства за каждое соседнее здание, которое производит любой из потребляемых им ресурсов (за исключением Лаборатории клонирования и Фабрики клонирования)",
   GoldMiningCamp: "Золотодобывающий Лагерь",
   GordonMoore: "Гордон Мур",
   GrandBazaar: "Большой Базар",
   GrandBazaarDesc: "Управляйте всеми вашими Рынками. Прилегающие Базары получают +5 к Множителю Производства и Хранилища. Прилегающие Рынки получают различные торговые предложения",
   GrandBazaarFilters: "Фильтры",
   GrandBazaarFilterWarningHTML: "Выберите фильтр, чтобы увидеть рыночные сделки",
   GrandBazaarFilterYouGet: "Вы получаете",
   GrandBazaarFilterYouPay: "Вы платите",
   GrandBazaarSeach: "Поиск",
   GrandBazaarSearchGet: "Получить",
   GrandBazaarSearchPay: "Заплатить",
   GrandBazaarTabActive: "Активные",
   GrandBazaarTabTrades: "Сделки",
   GrandCanyon: "Большой каньон",
   GrandCanyonDesc: "Здания, разблокированные в текущей эпохе, получают +2 к Множителю производства. Удваивает эффект от Дж. П. Моргана",
   GraphicsDriver: "Графический Драйвер: %{driver}",
   GreatDagonPagoda: "Пагода Шведагон",
   GreatDagonPagodaDescV2: "Все пагоды освобождаются от штрафа -1 Счастья. Производит науку исходя из производства веры всех пагод",
   GreatMosqueOfSamarra: "Большая мечеть в Самарре",
   GreatMosqueOfSamarraDescV2: "+1 к дальности обзора зданий. Открывает 5 случайных неисследованных Плиток с месторождениями и строит соответствующее добывающее здание 10-го уровня на каждой",
   GreatPeople: "Великий Человек",
   GreatPeopleEffect: "Эффект",
   GreatPeopleFilter: "Введите имя или эпоху, чтобы отфильтровать Великих Людей",
   GreatPeopleName: "Имя",
   GreatPeoplePermanentColumn: "Постоянные",
   GreatPeoplePermanentShort: "Постоянные",
   GreatPeoplePickPerRoll: "Взять Великого Человека за выбор",
   GreatPeopleThisRun: "Великие Люди из этого Пробега",
   GreatPeopleThisRunColumn: "Этот Пробег",
   GreatPeopleThisRunShort: "Этот Пробег",
   GreatPersonLevelRequired: "Требуемый уровень Постоянных Великих Людей",
   GreatPersonLevelRequiredDescV2: "Для %{city} цивилизации требуются %{required} уровень постоянных людей. На данный момент у вас есть %{current}",
   GreatPersonPromotionPromote: "Улучшить",
   GreatPersonThisRunEffectiveLevel: "Сейчас у вас есть %{count} %{person} с этого пробега. Дополнительный %{person} будет иметь 1/%{effect} эффекта",
   GreatPersonWildCardBirth: "Родить",
   GreatSphinx: "Великий Сфинкс",
   GreatSphinxDesc: "Все здания Тира II и выше в пределах 2 Плиток получают +N к Множителям Потребления и Производства. N = количество прилегающих к нему зданий того же типа",
   GreatWall: "Великая Китайская стена",
   GreatWallDesc: "Все здания в радиусе 1 Плитки получают +N к Множителям Производства, Производительности Работников и Хранилища. N = количество Эпох между текущей и Эпохой, когда здание было открыто. Если построена рядом с Запретным Городом, то радиус увеличивается до 2 Плиток",
   GreedyTransport: "Избыточная транспортировка для Строительства/Улучшения",
   GreedyTransportDescHTML: "Это заставит здания продолжать транспортировать ресурсы, даже если у них достаточно ресурсов для текущего улучшения, что может сделать улучшение на несколько уровней <b>быстрее</b>, но в итоге приведет к транспортировке <b>большего количества ресурсов, чем необходимо</b>",
   Greek: "Греция",
   GrottaAzzurra: "Голубой Грот",
   GrottaAzzurraDescV2: "При обнаружении все ваши здания I тира получают +5 к уровню и +1 к Множителю Производства, Производительности Работников и Хранилища.",
   Gunpowder: "Порох",
   GunpowderMill: "Пороховая Фабрика",
   GuyFawkesNightV2: "Ночь Гая Фокса: Ост-Индская Компания увеличивает производительность зданий, прилегающих к Базарам, в 2 раза. Тауэрский Мост на 20% быстрее генерирует великих людей",
   HagiaSophia: "Собор Святой Софии",
   HagiaSophiaDescV2: "+5 Счастья. Здания с 0% Производственной Мощностью освобождаются от -1 Счастья. При загрузке игры обеспечьте достаточное количество Счастья во избежание остановки производства",
   HallOfFame: "Зал славы",
   HallOfSupremeHarmony: "Зал Высшей Гармонии",
   Hammurabi: "Хаммурапи",
   HangingGarden: "Висячие Сады",
   HangingGardenDesc: "+1 к Множителю Производительности Строителей. Соседние Акведуки получают +1 к Множителям Производства, Производительности Работников и Хранилища",
   Happiness: "Счастье",
   HappinessFromBuilding: "От Зданий (Исключая Чудеса)",
   HappinessFromBuildingTypes: "От хорошо укомплектованных типов зданий",
   HappinessFromHighestTierBuilding: "От Работающего Здания самого высокого Тира",
   HappinessFromUnlockedAge: "От открытых Эпох",
   HappinessFromUnlockedTech: "От открытых Исследований",
   HappinessFromWonders: "От Чудес (Включая Природные)",
   HappinessUncapped: "Счастье (Без Лимита)",
   HarryMarkowitz: "Гарри Марковитц",
   HarunAlRashid: "Харун ар-Рашид",
   Hatshepsut: "Хатшепсут",
   HatshepsutTemple: "Храм Хатшепсут",
   HatshepsutTempleDesc: "Открывает все Плитки с Водой на карте после завершения строительства. Пшеничные Фермы получают +1 к Множителю Производства за каждую Плитку с Водой, прилегающую к Пшеничной Ферме",
   Headquarter: "Центральный Офис",
   HedgeFund: "Хедж-фонд",
   HelpMenu: "Помощь",
   HenryFord: "Генри Форд",
   Herding: "Стадо",
   Herodotus: "Геродот",
   HighlightBuilding: "Highlight %{building}",
   HimejiCastle: "Замок Химэдзи",
   HimejiCastleDesc: "Все Верфи Каравелл, Верфи Галеонов, и Верфи Фрегатов получают +1 к Множителям Производства, Производительности Работников и Хранилища",
   Hollywood: "Голливуд",
   HollywoodDesc: "+5 Счастья. +1 Счастье за каждое хорошо укомплектованное здание, потребляющее или производящее культуру в радиусе 2 плиток",
   HolyEmpire: "Священная Империя",
   Homer: "Гомер",
   Honor4UpgradeHTML: "Удваивает эффект от <b>Чжень Хе</b> (Великий Человек)",
   HonorLevelX: "Отвага %{level}",
   Horse: "Лошадь",
   HorsebackRiding: "Верховая Езда",
   House: "Дом",
   Housing: "Жильё",
   Hut: "Хижина",
   HydroDam: "Гидроплотина",
   Hydroelectricity: "Гидроэнергетика",
   HymanGRickover: "Хайман Дж. Риковер",
   IdeologyDescHTML: "Выберите <b>Либерализм, Консерватизм, Социализм или Коммунизм</b> идеологией вашей империи. Вы <b>не можете сменить идеологию</b> после выбора. Вы можете разблокировать больше усилений для каждой идеологии",
   IMPei: "Бэй Юймин",
   IMPeiDesc: "+%{value} к Множителю Мощности Строительства",
   Imperialism: "Империализм",
   ImperialPalace: "Имперский Дворец",
   IndustrialAge: "Промышленность",
   InformationAge: "Информационный Век",
   InputResourceForCloning: "Входной ресурс для клонирования",
   InternationalSpaceStation: "Международная космическая станция",
   InternationalSpaceStationDesc: "Все здания получают +5 Множителю Хранилища. Это чудо можно улучшить, за каждое улучшение все здания получают +1 к Множителю Хранилища",
   Internet: "Интернет",
   InternetServiceProvider: "Интернет провайдер",
   InverseSelection: "Инвертировать",
   Iron: "Железо",
   IronAge: "Железный Век",
   Ironclad: "Броненосец",
   IroncladBuilder: "Верфь Броненосцев",
   IronForge: "Кузница Железа",
   IronMiningCamp: "Железо-добывающий Лагерь",
   IronTech: "Железо",
   IsaacNewton: "Исаак Ньютон",
   IsaacNewtonDescV2: "+%{value} Науки от всех Работников если более 50% Работников заняты и менее 50% Занятых Работников заняты Транспортировкой",
   IsambardKingdomBrunel: "Изамбард Кингдом Брюнель",
   IsidoreOfMiletus: "Исидор Милетский",
   IsidoreOfMiletusDesc: "+%{value} к Множителю Мощности Строительства",
   Islam5UpgradeHTML: "При разблокировке единоразово дает науку в количестве самой дорогой технологии <b>Промышленной эпохи</b> ",
   IslamLevelX: "Ислам %{level}",
   ItsukushimaShrine: "Святилище Ицукусима",
   ItsukushimaShrineDescV2: "Когда все технологии в эпоху будут разблокированы, единоразово генерирует науку, равную самой дешевой технологии в следующей эпохе",
   JamesWatson: "Джеймс Дьюи Уотсон",
   JamesWatsonDesc: "+%{value} Науки от Занятых Работников",
   JamesWatt: "Джеймс Уатт",
   Japanese: "Япония",
   JetPropulsion: "Реактивный Двигатель",
   JohannesGutenberg: "Иоганн Гутенберг",
   JohannesKepler: "Иоганн Кеплер",
   JohnCarmack: "Джон Кармак",
   JohnDRockefeller: "Джон Д. Рокфеллер",
   JohnMcCarthy: "Джон Маккарти",
   JohnVonNeumann: "Джон фон Нейман",
   JohnVonNeumannDesc: "+%{value} Науки от Занятых Работников",
   JoinDiscord: "Присоединяйтесь к Discord",
   JosephPulitzer: "Джозеф Пулитцер",
   Journalism: "Журналистика",
   JPMorgan: "Дж. П. Морган",
   JRobertOppenheimer: "Дж. Роберт Оппенгеймер",
   JuliusCaesar: "Юлий Цезарь",
   Justinian: "Юстиниан",
   Kanagawa: "Канагава",
   KanagawaDesc: "Все великие люди текущей эпохи получают дополнительный уровень для этого забега (исключая Зенобию)",
   KarlMarx: "Карл Маркс",
   Knight: "Рыцарь",
   KnightCamp: "Лагерь Рыцарей",
   Koti: "Koti",
   KotiInStorage: "Koti In Storage",
   KotiProduction: "Koti Production",
   LandTrade: "Сухопутная Торговля",
   Language: "Язык",
   Lapland: "Лапландия",
   LaplandDesc: "Когда открыто, раскрывает всю карту. Все здания в пределах 2 плиток получают +5 к Множителю производства. Это чудо природы может быть обнаружено только в Декабре",
   LargeHadronCollider: "Большой адронный коллайдер",
   LargeHadronColliderDescV2: "Великие люди Информационной эпохи получают +2 уровня за этот забег. Это чудо можно улучшить, за каждый уровень улучшения +1 уровень всем великим людям Информационной эпохи в этом забеге.",
   Law: "Закон",
   Lens: "Линзы",
   LensWorkshop: "Мастерская Линз",
   LeonardoDaVinci: "Леонардо да Винчи",
   Level: "Уровень",
   LevelX: "Уровень %{level}",
   Liberalism: "Либерализм",
   LiberalismLevel3DescHTML: "Бесплатная транспортировка <b>от</b> и <b>до</b> Складов",
   LiberalismLevel5DescHTML: "<b>Удваивает</b> эффект электрификации",
   LiberalismLevelX: "Либерализм %{level} уровня",
   Library: "Библиотека",
   LighthouseOfAlexandria: "Александрийский Маяк",
   LighthouseOfAlexandriaDesc: "Все примыкающие здания, получают +5 к Множителю Хранилища",
   LinusPauling: "Лайнус Полинг",
   LinusPaulingDesc: "+%{value} Науки от Незанятых Работников",
   Literature: "Литература",
   LiveData: "Текущее значение",
   LocomotiveFactory: "Локомотивный Завод",
   Logging: "Лесозаготовка",
   LoggingCamp: "Лесозаготовительный Лагерь",
   LouisSullivan: "Луис Салливан",
   LouisSullivanDesc: "+%{value} к Множителю Мощности Строительства",
   Louvre: "Лувр",
   LouvreDesc: "За каждые 10 Великих людей после перерождения рождается один Великий человек из открытых эпох",
   Lumber: "Доска",
   LumberMill: "Лесопилка",
   LunarNewYear: "Лунный Новый год: Великая Стена дает двойное усиление для зданий. Фарфоровая башня дает +1 к уровню всех Великих Людей из этого забега",
   LuxorTemple: "Луксорский храм",
   LuxorTempleDescV2: "+1 Науки от занятых рабочих. Выберите религию для империи, разблокирует больше усилений с каждым выбором",
   Machinery: "Оборудование",
   Magazine: "Журнал",
   MagazinePublisher: "Издатель Журналов",
   Maglev: "Маглев",
   MaglevFactory: "Фабрика маглев",
   MahatmaGandhi: "Махатма Ганди",
   ManageAgeWisdom: "Управлять Мудростью Эпох",
   ManagedImport: "Неуправляемый импорт",
   ManagedImportDescV2: "Это здание будет автоматически импортировать ресурсы, производимые в радиусе %{range} Плиток(и). Ресурсы, транспортируемые этим зданием, не могут быть изменены вручную. Максимальная дистанция транспортировки будет проигнорирована",
   ManageGreatPeople: "Управление Великими Людьми",
   ManagePermanentGreatPeople: "Управление Постоянными Великими Людьми",
   ManageSave: "Управление сохранениями",
   ManageWonders: "Управление Чудесами",
   Manhattan: "Манхэттен",
   ManhattanProject: "Манхэттенский Проект",
   ManhattanProjectDesc: "Все Урановые Рудники получают +2 к Множителям Производства, Производительности Работников и Хранилища. Заводы по Обогащению Урана и Атомные Объекты получают +1 к Множителю Производства за каждую прилегающую Урановую Шахту, построенную на Плитке с Ураном",
   Marble: "Мрамор",
   Marbleworks: "Мраморный Завод",
   MarcoPolo: "Марко Поло",
   MarieCurie: "Мария Склодовская-Кюри",
   MarinaBaySands: "Пески Марина Бэй",
   MarinaBaySandsDesc: "Все здания получают +5 к Множителю Производительности Работников. Это чудо можно улучшить, за каждое улучшение все здания получают +1 к Множителю Производительности Работников",
   Market: "Рынок",
   MarketDesc: "Обменивайте один ресурс на другой, доступные ресурсы обновляются каждый час",
   MarketRefreshMessage: "Сделки на %{count} рынках обновлены",
   MarketSell: "Продать",
   MarketSettings: "Настройки Рынка",
   MarketValueDesc: "%{value} по сравнению со средней ценой",
   MarketYouGet: "Вы получите",
   MarketYouPay: "Вы платите",
   MartinLuther: "Мартин Лютер",
   MaryamMirzakhani: "Марьям Мирзахани",
   MaryamMirzakhaniDesc: "+%{value} Науки от Незанятых Работников",
   Masonry: "Кирпичная Кладка",
   MatrioshkaBrain: "Матрешка Мозговая",
   MatrioshkaBrainDescV2: "Позволяет учитывать науку в стоимости империи (5 Науки = 1 Стоимость Империи). +5 Науки за каждого занятого и незанятого рабочего. Это чудо можно улучшить, за каждое улучшение +1 Науки за каждого занятого и незанятого рабочего и +1 к Множителю Производства для зданий, производящих науку.",
   MausoleumAtHalicarnassus: "Мавзолей в Галикарнасе",
   MausoleumAtHalicarnassusDescV2: "Transports from or to buildings within 2 tile range do not cost workers",
   MaxExplorers: "Макс. Исследователей",
   MaxTransportDistance: "Максимальная Дистанция Транспортировки",
   Meat: "Мясо",
   Metallurgy: "Металлургия",
   Michelangelo: "Микеланджело",
   MiddleAge: "Средневековье",
   MilitaryTactics: "Военная Доктрина",
   Milk: "Молоко",
   Moai: "Моаи",
   MoaiDesc: "Моаи",
   MobileOverride: "Мобильное переопределение",
   MogaoCaves: "Пещеры Могао",
   MogaoCavesDescV3: "+1 Счастья за каждые 10% занятых рабочих. Все соседние здания, производящие веру, освобождаются от штрафа -1 Счастья",
   MonetarySystem: "Денежная Система",
   MontSaintMichel: "Мон Сен-Мишель",
   MontSaintMichelDesc: "Производит культуру за счет простаивающих работников. Даёт +1 к Множителю хранилища всем зданиям в радиусе 2-х плиток. Это чудо может быть улучшено с помощью сгенерированной культуры, и каждый уровень дает дополнительный +1 к Множителю хранилища",
   Mosque: "Мечеть",
   MotionPicture: "Кинокартина",
   MountArarat: "Mount Ararat",
   MountAraratDesc: "All buildings within 2 tile range get +X Production, Worker Capacity and Storage Multiplier. X = cubic root of Effective Great People Level",
   MountFuji: "Гора Фудзи",
   MountFujiDescV2: "Когда Петра построена рядом с ним, Петра получает +8ч Хранилищу Временного Разрыва. Когда игра запущена, генерирует 20 Временного Разрыва каждую минуту в Петре (не ускоряется самой Петрой, не генерируется, когда игра офлайн)",
   MountSinai: "Гора Синай",
   MountSinaiDesc: "Когда обнаруживается, рождается Великий Человек текущей Эпохи. Все здания, производящие Веру, получают +5 к Множителю Хранилища",
   MountTai: "Гора Тайшань",
   MountTaiDesc: "Все здания, производящие Науку, получают +1 к Множителю Производства. Удваивает эффект Конфуция (Великий Человек). При обнаружении единовременно даёт Науку, эквивалентную стоимости самой дорогой разблокированной Технологии",
   MoveBuilding: "Передвинуть здание",
   MoveBuildingFail: "Выбранная плитка не подходит",
   MoveBuildingNoTeleport: "У вас нет достаточно телепортов",
   MoveBuildingSelectTile: "Выберите плитку...",
   MoveBuildingSelectTileToastHTML: "Выберите <b>пустую исследованную плитку</b> на карте как цель",
   Movie: "Фильм",
   MovieStudio: "Киностудия",
   Museum: "Музей",
   Music: "Музыка",
   MusiciansGuild: "Гильдия Музыкантов",
   MutualAssuredDestruction: "Гарантированное Взаимное Уничтожение",
   MutualFund: "Взаимный фонд",
   Name: "Название",
   Nanotechnology: "Нанотехнологии",
   NapoleonBonaparte: "Наполеон Бонапарт",
   NaturalGas: "Природный Газ",
   NaturalGasWell: "Скважина Природного Газа",
   NaturalWonderName: "Природное Чудо: %{name}",
   NaturalWonders: "Природные Чудеса",
   Navigation: "Навигация",
   NebuchadnezzarII: "Навуходоносор II",
   Neuschwanstein: "Нойшванштайн",
   NeuschwansteinDesc: "+10 к Множителю Мощности Строительства при строительстве Чудес",
   Newspaper: "Газета",
   NextExplorersIn: "Следующий Исследователь через",
   NextMarketUpdateIn: "Следующее обновление Рынка через",
   NiagaraFalls: "Ниагарский водопад",
   NiagaraFallsDescV2: "Все склады, рынки и базары получают +N к Множителю Хранилища. N = число открытых эпох. Альберт Эйнштейн добавляет +1 к Множителю Производства Фонду исследований (на него не влияют другие улучшения, такие как Бродвей)",
   NielsBohr: "Нильс Бор",
   NielsBohrDescV2: "+%{value} Науки от всех Работников если более 50% Работников заняты и менее 50% Занятых Работников заняты Транспортировкой",
   NileRiver: "Река Нил",
   NileRiverDesc: "Удваивает эффект Хатшепсут (Великий Человек). Все Пшеничные Фермы получают +1 к Множителям Производства и Хранилища. Все прилегающие Пшеничные Фермы получают +5 к Множителям Производства и Хранилища",
   NoPowerRequired: "Это здание не требует электроэнергии",
   NothingHere: "Здесь ничего нет",
   NotProducingBuildings: "Остановленное производство",
   NuclearFission: "Ядерное Деление",
   NuclearFuelRod: "Ядерный Топливный Стержень",
   NuclearMissile: "Ядерная Ракета",
   NuclearMissileSilo: "Шахта Ядерных Ракет",
   NuclearPowerPlant: "Атомная Электростанция",
   NuclearReactor: "Ядерный Реактор",
   NuclearSubmarine: "Атомная подлодка",
   NuclearSubmarineYard: "Верфь Атомных подлодок",
   OdaNobunaga: "Oda Nobunaga",
   OfflineErrorMessage: "Вы сейчас оффлайн, для выполнения этой операции требуется подключение к Интернету",
   OfflineProduction: "Оффлайн Продукция",
   OfflineProductionTime: "Время производства в оффлайн",
   OfflineProductionTimeDescHTML: "Для <b>первого %{time} оффлайн времени</b>, вы можете выбрать либо производство в оффлайн или ускорение времени - вы можете установить разделитель здесь. <b>Оставшееся оффлайн время</b> может быть преобразовано только в ускорение времени",
   OfflineTime: "Время Оффлайн",
   Oil: "Нефть",
   OilPress: "Пресс для Масла",
   OilRefinery: "Нефтеперерабатывающий Завод",
   OilWell: "Нефтяная Скважина",
   Ok: "ОК",
   Oktoberfest: "Октоберфест: Двойной эффект от Цугшпитце",
   Olive: "Олива",
   OlivePlantation: "Оливковая Плантация",
   Olympics: "Олимпиада",
   OnlyAvailableWhenPlaying: "Доступно только в %{city}",
   OpenLogFolder: "Открыть Папку Лога",
   OpenSaveBackupFolder: "Откройте Папку Резервных Копий",
   OpenSaveFolder: "Открыть Папку Сохранений",
   Opera: "Опера",
   OperationNotAllowedError: "Эта операция не разрешена",
   Opet: "Опет: Великий Сфинкс больше не увеличивает Множитель Потребления",
   OpticalFiber: "Оптоволокно",
   OpticalFiberPlant: "Завод Оптоволокна",
   Optics: "Оптика",
   OptionsMenu: "Опции",
   OptionsUseModernUIV2: "Использовать Шрифт Со Сглаживанием",
   OsakaCastle: "Замок Осака",
   OsakaCastleDesc: "Обеспечивает Электроэнергией все плитки в радиусе 2 плиток. Разрешает электрификацию зданий, производящих науку (включая Лабораторию клонирования)",
   OtherPlatform: "Другая Платформа",
   Ottoman: "Ottoman",
   OttoVonBismarck: "Отто фон Бисмарк",
   OxfordUniversity: "Оксфордский Университет",
   OxfordUniversityDescV3: "+10% производимой Науки для производящих Науку зданий. По завершении постройки единовременно даёт Науку, эквивалентную стоимости самой дорогой разблокированной Технологии",
   PabloPicasso: "Пабло Пикассо",
   Pagoda: "Пагода",
   PaintersGuild: "Гильдия Художников",
   Painting: "Картина",
   PalmJumeirah: "Пальма Джумейра",
   PalmJumeirahDesc: "+10 к Мощности Строительства. Это чудо можно улучшить, за каждое улучшение +2 к Мощности Строительства",
   Pamukkale: "Pamukkale",
   PamukkaleDesc: "When discovered, convert each one of the Permanent Great People Shards (except for Promotion and Wildcard) to the same Great Person From This Run",
   Panathenaea: "Панафинея: Посейдон обеспечивает +1 к Множителю Производства для всех зданий",
   Pantheon: "Пантеон",
   PantheonDescV2: "Все здания с радиусом 2 клетки получают +1 к Множителю Производительности Работников и Хранилища. Генерирует науку, равную производству веры всех святилищ",
   Paper: "Бумага",
   PaperMaker: "Завод Бумаги",
   Parliament: "Парламент",
   Parthenon: "Парфенон",
   ParthenonDescV2: "Рождаются два Великих Человека Классического Века, и вы получаете 4 варианта выбора для каждого. Гильдии Музыкантов и Гильдии Художников получают +1 к Множителям Производства, Производительности Работников и Хранилища, а также освобождаются от -1 к Счастья",
   Passcode: "Код доступа",
   PasscodeToastHTML: "<b>%{code}</b> это ваш код доступа, он действителен в течение 30 минут",
   PatchNotes: "Примечания к Патчам",
   Peace: "Мир",
   Peacekeeper: "Миротворец",
   Penthouse: "Penthouse",
   PercentageOfProductionWorkers: "Процент производственных рабочих",
   Performance: "Производительность",
   PermanentGreatPeople: "Постоянные Великие Люди",
   PermanentGreatPeopleAcquired: "Приобретённые Постоянные Великие Люди",
   PermanentGreatPeopleUpgradeUndo: "Отменить постоянное улучшение Великих Людей: это преобразует улучшенный уровень обратно в Осколки - вы получите %{amount} Осколков",
   Persepolis: "Персеполь",
   PersepolisDesc: "Все Медно-добывающие Лагеря, Лесозаготовительные Лагеря и Каменные Карьеры получают +1 к Множителям Производства, Производительности Работников и Хранилища",
   PeterHiggs: "Питер Хиггс",
   PeterHiggsDesc: "+%{value} Науки от Занятых Работников",
   Petra: "Петра",
   PetraDesc: "Создаёт Временной Разрыв, когда вы не в сети, который вы можете использовать для ускорения вашей Империи",
   PetraOfflineTimeReconciliation: "Вы получили %{count} Временного Разрыва после сверки оффлайн времени сервера",
   Petrol: "Бензин",
   PhiloFarnsworth: "Фило Тейлор Фарнсуорт",
   Philosophy: "Философия",
   Physics: "Физика",
   PierreDeCoubertin: "Пьер де Кубертен",
   Pizza: "Пицца",
   Pizzeria: "Пиццерия",
   PlanetaryRover: "Планетарный вездеход",
   Plastics: "Пластмасса",
   PlasticsFactory: "Завод Пластмасс",
   PlatformAndroid: "Android",
   PlatformiOS: "iOS",
   PlatformSteam: "Steam",
   PlatformSyncInstructionHTML: "Если вы хотите синхронизировать свой прогресс на этом устройстве с новым устройством, нажмите <b>Синхронизация с Новым Устройством</b>и получите одноразовый код доступа. На вашем новом устройстве нажмите <b>Подключиться к Устройству</b> и введите одноразовый код доступа",
   Plato: "Платон",
   PlayerHandle: "Игровой Аккаунт",
   PlayerHandleOffline: "Вы сейчас не в сети",
   PlayerMapClaimThisTile: "Занять эту Плитку",
   PlayerMapClaimTileCondition2: "Вы не были заблокированы системой противодействия мошенничеству",
   PlayerMapClaimTileCondition3: "Вы разблокировали необходимую технологию: %{tech}",
   PlayerMapClaimTileCondition4: "Вы не заняли Плитку или прошло время ожидания перемещения вашей Плитки",
   PlayerMapClaimTileCooldownLeft: "Время ожидания осталось: %{time}",
   PlayerMapClaimTileNoLongerReserved: "Эта Плитка больше не зарезервирована. Вы можете выгнать <b>%{name}</b> и зарезервировать Плитку за собой",
   PlayerMapEstablishedSince: "Основано в",
   PlayerMapLastSeenAt: "Последнее посещение",
   PlayerMapMapTileBonus: "Бонус торговой плитки",
   PlayerMapMenu: "Торговля",
   PlayerMapOccupyThisTile: "Занять эту плитку",
   PlayerMapOccupyTileCondition1: "Эта плитка прилегает к вашим домашней или занятым плиткам",
   PlayerMapPageGoBackToCity: "Вернуться в Город",
   PlayerMapSetYourTariff: "Установить ваш Налог",
   PlayerMapTariff: "Налог",
   PlayerMapTariffApply: "Применить Налоговую Ставку",
   PlayerMapTariffDesc: "Каждый Торговец, проходящий через вашу Плитку, будет платить вам Налог. Здесь есть баланс: если вы увеличите Налог, то получите больше выгоды от каждой Сделки, но меньше Торговцев пройдет через вашу Плитку.",
   PlayerMapTileAvailableTilePoint: "Available Tile Point",
   PlayerMapTileFromOccupying: "From Owned/Occupied Tiles",
   PlayerMapTileFromOccupyingTooltipHTML: "An owned/occupied tile generates <b>%{point}</b> tile point per hour (up to %{max} days from the first claimed tile)",
   PlayerMapTileFromRank: "From Account Rank",
   PlayerMapTileTilePoint: "Tile Point",
   PlayerMapTileUsedTilePoint: "Used Tile Point",
   PlayerMapTileUsedTilePointTooltipHTML: "You need <b>1 tile point</b> to own/occupy a tile",
   PlayerMapTradesFrom: "Заявки от %{name}",
   PlayerMapUnclaimedTile: "Незанятая Плитка",
   PlayerMapYourTile: "Ваша Плитка",
   PlayerTrade: "Торговля с Игроками",
   PlayerTradeAddSuccess: "Заявка была успешно добавлена",
   PlayerTradeAddTradeCancel: "Отменить",
   PlayerTradeAmount: "Количество",
   PlayerTradeCancelDescHTML: "Вам вернётся <b>%{res}</b> после отмены этой Сделки: <b>%{percent}</b> будет удержано, за отказ и <b>%{discard}</b> будет выброшено из-за переполненности Хранилища.<br><b>Вы уверены, что хотите отменить сделку?</b>",
   PlayerTradeCancelTrade: "Отменить Сделку",
   PlayerTradeClaim: "Забрать",
   PlayerTradeClaimAll: "Получить всё",
   PlayerTradeClaimAllFailedMessageV2: "Не удалось получить ни одной Сделки - Хранилище переполнено?",
   PlayerTradeClaimAllMessageV2: "Вы получили: <b>%{resources}</b>",
   PlayerTradeClaimAvailable: "%{count} Сделок было выполнено и доступно для забора",
   PlayerTradeClaimTileFirst: "Займите свою Плитку на торговой карте",
   PlayerTradeClaimTileFirstWarning: "Торговать с другими игроками можно только после того, как вы заняли свою Плитку на торговой карте.",
   PlayerTradeClearAll: "Обнулить все поля",
   PlayerTradeClearFilter: "Очистить фильтры",
   PlayerTradeDisabledBeta: "Вы можете создавать новые Сделки только после выхода бета-версии в релиз.",
   PlayerTradeFill: "Продать",
   PlayerTradeFill50: "Заполнить 50%",
   PlayerTradeFill95: "Заполнить 95%",
   PlayerTradeFillAmount: "Количество к Продаже",
   PlayerTradeFillAmountMaxV2: "Заполнить Макс.",
   PlayerTradeFillBy: "Продано",
   PlayerTradeFillPercentage: "Заполнить Процент",
   PlayerTradeFillSuccessV2: "<b>%{success}/%{total}</b> заявок было завершено. Вы заплатили <b>%{fillAmount} %{fillResource}</b> и получили <b>%{receivedAmount} %{receivedResource}</b>",
   PlayerTradeFillTradeButton: "Продать",
   PlayerTradeFillTradeTitle: "Продать",
   PlayerTradeFilters: "Фильтры",
   PlayerTradeFiltersApply: "Применить",
   PlayerTradeFiltersClear: "Очистить",
   PlayerTradeFilterWhatIHave: "Отфильтровать по тому, что у меня есть",
   PlayerTradeFrom: "От",
   PlayerTradeIOffer: "Я предлагаю",
   PlayerTradeIWant: "Я хочу",
   PlayerTradeMaxAll: "Макс. все поля",
   PlayerTradeMaxTradeAmountFilter: "Макс. количество",
   PlayerTradeMaxTradeExceeded: "Вы превысили максимальное количество активных сделок для вашего уровня аккаунта",
   PlayerTradeNewTrade: "Новая Заявка",
   PlayerTradeNoFillBecauseOfResources: "Ни одна сделка не была выполнена из-за нехватки ресурсов.",
   PlayerTradeNoValidRoute: "Невозможно найти подходящий торговый маршрут между вами и %{name}",
   PlayerTradeOffer: "Даёт",
   PlayerTradePlaceTrade: "Разместить Заявку",
   PlayerTradePlayerNameFilter: "Имя игрока",
   PlayerTradeResource: "Ресурс",
   PlayerTradeStorageRequired: "Хранилища Требуется",
   PlayerTradeTabImport: "Импорт",
   PlayerTradeTabPendingTrades: "Ожидающие сделки",
   PlayerTradeTabTrades: "Сделки",
   PlayerTradeTariffTooltip: "Получено от Торгового Налога",
   PlayerTradeWant: "Хочет",
   PlayerTradeYouGetGross: "Вы Получаете (До Налога): %{res}",
   PlayerTradeYouGetNet: "Вы Получаете (После Налога): %{res}",
   PlayerTradeYouPay: "Вы Платите: %{res}",
   Poem: "Поэма",
   PoetrySchool: "Школа поэзии",
   Politics: "Политика",
   PolytheismLevelX: "Политеизм %{level}",
   PorcelainTower: "Фарфоровая Пагода",
   PorcelainTowerDesc: "+5 Счастья. После завершения постройки все ваши Дополнительные Великие Люди За Перерождение становятся доступны в текущем Пробеге (они выбираются по тому же правилу, что и Постоянные Великие Люди)",
   PorcelainTowerMaxPickPerRoll: "Предпочитать максимальное количество за выбор",
   PorcelainTowerMaxPickPerRollDescHTML: "При выборе великих людей после завершения строительства Фарфоровой Пагоды отдайте предпочтение максимальному количеству за выбор из доступного количества",
   Poseidon: "Посейдон",
   PoseidonDescV2: "Все соседние здания получают бесплатные улучшения до уровня 25 и +N к Множителям Производства, Производительности Работников и Хранилища. N = тир здания",
   PoultryFarm: "Птицеферма",
   Power: "Электроэнергия",
   PowerAvailable: "Электроэнергии Всего",
   PowerUsed: "Электроэнергии Использовано",
   PreciousMetal: "Драгоценный Металл",
   Printing: "Печать",
   PrintingHouse: "Типография",
   PrintingPress: "Печатный Станок",
   PrivateOwnership: "Частная Собственность",
   Produce: "Производит",
   ProduceResource: "Производит: %{resource}",
   ProductionMultiplier: "Множитель Производства",
   ProductionPriority: "Приоритет Производства",
   ProductionPriorityDescV4: "Приоритет определяет порядок, в котором здания транспортируют и производят продукцию - большее количество означает, что здание транспортирует и производит продукцию раньше других зданий",
   ProductionWorkers: "Производящие Работники",
   Progress: "Прогресс",
   ProgressTowardsNextGreatPerson: "Прогресс до следующего Великого Человека После Перерождения",
   ProgressTowardsTheNextGreatPerson: "Прогресс на пути к следующему великому человеку",
   PromotionGreatPersonDescV2: "При использовании повышает любого постоянного великого человека той же эпохи до следующей эпохи",
   ProphetsMosque: "Мечеть Пророка",
   ProphetsMosqueDesc: "Удваивает эффект от Харум аль-Рашида. Генерирует науку, равную производству веры всех мечетей",
   Province: "Провинция",
   ProvinceAegyptus: "Египет",
   ProvinceAfrica: "Африка",
   ProvinceAsia: "Азия",
   ProvinceBithynia: "Вифиния",
   ProvinceCantabri: "Кантабрия",
   ProvinceCappadocia: "Каппадокия",
   ProvinceCilicia: "Киликия",
   ProvinceCommagene: "Коммагена",
   ProvinceCreta: "Крит",
   ProvinceCyprus: "Кипр",
   ProvinceCyrene: "Киренаика",
   ProvinceGalatia: "Галатия",
   ProvinceGallia: "Галлия",
   ProvinceGalliaCisalpina: "Цизальпийская Галлия",
   ProvinceGalliaTransalpina: "Трансальпийская Галлия",
   ProvinceHispania: "Испания",
   ProvinceIllyricum: "Иллирик",
   ProvinceItalia: "Италия",
   ProvinceJudia: "Иудея",
   ProvinceLycia: "Ликия",
   ProvinceMacedonia: "Македония",
   ProvinceMauretania: "Мавритания",
   ProvinceNumidia: "Нумидия",
   ProvincePontus: "Понт",
   ProvinceSardiniaAndCorsica: "Сардиния и Корсика",
   ProvinceSicillia: "Сицилия",
   ProvinceSophene: "Софена",
   ProvinceSyria: "Сирия",
   PublishingHouse: "Издательство",
   PyramidOfGiza: "Пирамида Гизы",
   PyramidOfGizaDesc: "Все здания, которые производят Работников, получают +1 к Множителю Производства",
   QinShiHuang: "Цинь Шихуанди",
   Radio: "Радио",
   RadioStation: "Радиостанция",
   Railway: "Железная Дорога",
   RamessesII: "Рамзес II",
   RamessesIIDesc: "+%{value} к Множителю Мощности Строительства",
   RandomColorScheme: "Случайная цветовая схема",
   RapidFire: "Шквальный Огонь",
   ReadFullPatchNotes: "Прочесть примечания к патчу",
   RebirthHistory: "История перерождения",
   RebirthTime: "Время перерождения",
   Reborn: "Переродиться",
   RebornModalDescV3: "Вы начнете новую империю, но все ваши Великие Люди <b>из этого забега</b> станут Постоянными Осколками, которые можно использовать для повышения вашего <b>уровня Постоянных Великих Людей</b>. Вы также получите дополнительные Осколки Великих Людей на основе вашей <b>общей Стоимости Империи</b>",
   RebornOfflineWarning: "Сейчас вы оффлайн. Вы можете перерождаться только когда подключены к серверу",
   RebornTradeWarning: "У вас есть активные или завершенные, но не полученные Сделки. <b>Перерождение безвозвратно удалит их</b> - вам сначала следует рассмотреть возможность их отмены или обналичивания",
   RedistributeAmongSelected: "Распределить между выбранными",
   RedistributeAmongSelectedCap: "Предел",
   RedistributeAmongSelectedImport: "Импорт",
   Refinery: "Нефтеперерабатывающий Завод",
   Reichstag: "Рейхстаг",
   Religion: "Религия",
   ReligionBuddhism: "Буддизм",
   ReligionChristianity: "Христианство",
   ReligionDescHTML: "Выберите <b>Христианство, Ислам, Буддизм или Политеизм</b> религией своей империи. Вы <b>не сможете изменить религию</b> после выбора. Вы можете разблокировать больше усилений каждой религии",
   ReligionIslam: "Ислам",
   ReligionPolytheism: "Политеизм",
   Renaissance: "Ренессанс",
   RenaissanceAge: "Ренессанс",
   ReneDescartes: "Рене Декарт",
   RequiredDeposit: "Требуется Месторождение",
   RequiredWorkersTooltipV2: "Необходимое количество рабочих для производства равно сумме всех потребляемых и производимых ресурсов после множителей (без учета динамических множителей)",
   RequirePower: "Требуется Электричество",
   RequirePowerDesc: "Это здание должно быть построено на Плитке с Электричеством и распространит Электричество на прилегающие плитки",
   Research: "Исследование",
   ResearchFund: "Фонд исследований",
   ResearchLab: "Исследовательская Лаборатория",
   ResearchMenu: "Исследования",
   ResourceAmount: "Количество",
   ResourceBar: "Панель Ресурсов",
   ResourceBarExcludeStorageFullHTML: "Исключить здания с <b>переполненным Хранилищем</b> из Остановленного Производства",
   ResourceBarExcludeTurnedOffOrNoActiveTransportHTML: "Исключить здания с нулевой <b>Производственной Мощностью</b> из Остановленного Производства",
   ResourceBarShowUncappedHappiness: "Показывать Счастье без лимита",
   ResourceCloneTooltip: "Коэффициент производства применяется только к клонированному ресурсу (т.е. к дополнительной копии).",
   ResourceColor: "Цвет Ресурса",
   ResourceExportBelowCap: "Экспорт ниже Максимума",
   ResourceExportBelowCapTooltip: "Разрешить другим зданиям транспортировать ресурс из этого здания, даже если его количество меньше максимального",
   ResourceExportToSameType: "Транспортировать в тот же тип",
   ResourceExportToSameTypeTooltip: "Позволяет другим зданиям того же типа транспортировать ресурс из этого здания",
   ResourceFromBuilding: "%{resource} из %{building}",
   ResourceImport: "Транспортировка Ресурсов",
   ResourceImportCapacity: "Мощность Транспортировки Ресурсов",
   ResourceImportImportCapV2: "Максимальное Количество",
   ResourceImportImportCapV2Tooltip: "Это здание прекратит транспортировку этого ресурса, когда будет достигнуто максимальное количество",
   ResourceImportImportPerCycleV2: "За Цикл",
   ResourceImportImportPerCycleV2ToolTip: "Количество данного ресурса, перемещаемого за цикл",
   ResourceImportPartialWarningHTML: "Общая Мощность Транспортировки ресурсов превысила максимальную пропускную способность: <b>каждый транспорт ресурсов будет транспортировать только часть за цикл</b>",
   ResourceImportResource: "Ресурсы",
   ResourceImportSettings: "Транспортировка Ресурса: %{res}",
   ResourceImportStorage: "Хранится",
   ResourceNeeded: "Дополнительно требуется %{resource} x%{amount}",
   ResourceTransportPreference: "Транспортные Предпочтения",
   RevealDeposit: "Открывает",
   Revolution: "Революция",
   RhineGorge: "Рейнское Ущелье",
   RhineGorgeDesc: "+2 Счастья за каждое чудо в пределах 2 плиток",
   RichardFeynman: "Ричард Фейнман",
   RichardFeynmanDesc: "+%{value} Науки от всех Работников если более 50% Работников заняты и менее 50% Занятых Работников заняты Транспортировкой",
   RichardJordanGatling: "Ричард Джордан Гатлинг",
   Rifle: "Винтовка",
   RifleFactory: "Фабрика Винтовок",
   Rifling: "Нарезка Ствола",
   Rijksmuseum: "Рейксмузеум",
   RijksmuseumDesc: "+5 Счастья. Все здания, которые производят или потребляют Культуру получают +1 к Множителям Производства, Производительности Работников и Хранилища",
   RoadAndWheel: "Дорога и Колесо",
   RobertNoyce: "Роберт Нойс",
   Robocar: "Робокар",
   RobocarFactory: "Фабрика робокаров",
   Robotics: "Робототехника",
   RockefellerCenterChristmasTree: "Рождественская Ёлка в Рокфеллер Центре",
   RockefellerCenterChristmasTreeDesc: "+3 Счастья за каждую открытую эпоху. Это чудо природы может быть открыто только в Декабре",
   Rocket: "Ракета",
   RocketFactory: "Ракетный Завод",
   Rocketry: "Ракетная Промышленность",
   Roman: "Рим",
   RomanForum: "Римский Форум",
   RudolfDiesel: "Рудольф Дизель",
   Rurik: "Рюрик",
   RurikDesc: "+%{value} Счастья",
   SagradaFamilia: "Саграда Фамилия",
   SagradaFamiliaDesc: "Все здания в радиусе 2 Плиток получают +N к Множителям Производства, Производительности Работников и Хранилища. N = максимальная разница в Тирах между прилегающими зданиями в радиусе 1 плитки от Саграда Фамилия",
   SaintBasilsCathedral: "Храм Василия Блаженного",
   SaintBasilsCathedralDescV2: "Позволяет добывающим зданиям работать рядом с месторождением. Все соседние здания I тира получают +1 к Множителям Производства, Производительности Работников и Хранилища",
   Saladin: "Салах ад-Дин",
   Samsuiluna: "Самсу-илуна",
   Sand: "Песок",
   Sandpit: "Песчаный Карьер",
   SantaClausVillage: "Деревня Санта-Клауса",
   SantaClausVillageDesc: "После завершения строительства рождается великий человек текущей эпохи. Это чудо может быть улучшено, и каждое следующее улучшение дает еще одного великого человека. Чтобы выбрать великих людей из этого чуда, предлагается 4 варианта. Это чудо может быть построено только в Декабре",
   SargonOfAkkad: "Саргон Аккадский",
   Satellite: "Спутник",
   SatelliteFactory: "Спутниковый Завод",
   SatoshiNakamoto: "Сатоши Накамото",
   Saturnalia: "Сатурналии: Альпы больше не увеличивают Множитель Потребления",
   SaveAndExit: "Сохранить И Выйти",
   School: "Школа",
   Science: "Наука",
   ScienceFromBusyWorkers: "Наука от Занятых Работников",
   ScienceFromIdleWorkers: "Наука от Незанятых Работников",
   SciencePerBusyWorker: "За Занятого Работника",
   SciencePerIdleWorker: "За Незанятого Работника",
   ScrollSensitivity: "Чувствительность скролла",
   ScrollSensitivityDescHTML: "Настройка чувствительности пролистывания скроллом. <b>Выберите значение от 0.01 до 100. По умолчанию 1</b>",
   ScrollWheelAdjustLevelTooltip: "Вы можете использовать колесо прокрутки для изменения уровня, когда ваш курсор находится над этим пунктом.",
   SeaTradeCost: "Стоимость морской торговли",
   SeaTradeUpgrade: "Торговля с игроками через море. Тариф для каждой морской плитки: %{tariff}",
   SelectCivilization: "Выбрать Цивилизацию",
   SelectedAll: "Выбрать Всё",
   SelectedCount: "%{count} Выбрано",
   Semiconductor: "Полупроводник",
   SemiconductorFab: "Завод Полупроводников",
   SendExplorer: "Отправить Исследователя",
   SergeiKorolev: "Сергей Королёв",
   SetAsDefault: "Установить по умолчанию",
   SetAsDefaultBuilding: "Установить по умолчанию для всех %{building}",
   Shamanism: "Шаманизм",
   Shelter: "Укрытие",
   Shortcut: "Горячие Клавиши",
   ShortcutBuildingPageSellBuildingV2: "Снести здание",
   ShortcutBuildingPageToggleBuilding: "Переключение Производственной Мощности",
   ShortcutBuildingPageToggleBuildingSetAllSimilar: "Переключить Производственную Мощность И Применить ко всем зданиям того же Типа",
   ShortcutBuildingPageUpgrade1: "Кнопка улучшения 1 (+1)",
   ShortcutBuildingPageUpgrade2: "Кнопка улучшения 2 (+5)",
   ShortcutBuildingPageUpgrade3: "Кнопка улучшения 3 (+10)",
   ShortcutBuildingPageUpgrade4: "Кнопка улучшения 4 (+15)",
   ShortcutBuildingPageUpgrade5: "Кнопка улучшения 5 (+20)",
   ShortcutClear: "Очистить",
   ShortcutConflict: "Ваша Горячая Клавиша пересекается с %{name}",
   ShortcutNone: "Нет",
   ShortcutPressShortcut: "Нажмите на клавишу...",
   ShortcutSave: "Сохранить",
   ShortcutScopeBuildingPage: "Страница Зданий",
   ShortcutScopeConstructionPage: "Страница строительства/улучшения",
   ShortcutScopeEmptyTilePage: "Страница пустой Плитки",
   ShortcutScopePlayerMapPage: "Меню Торговая Карта",
   ShortcutScopeTechPage: "Страница Технологий",
   ShortcutScopeUnexploredPage: "Страница неисследованной Плитки",
   ShortcutTechPageGoBackToCity: "Вернуться в Город",
   ShortcutTechPageUnlockTech: "Разблокировать выбранную Технологию",
   ShortcutUpgradePageCancelAllUpgrades: "Отменить все улучшения",
   ShortcutUpgradePageCancelUpgrade: "Отменить Улучшения",
   ShortcutUpgradePageDecreaseLevel: "Понизить Уровень Улучшения",
   ShortcutUpgradePageEndConstruction: "Отменить Строительство",
   ShortcutUpgradePageIncreaseLevel: "Поднять Уровень Улучшения",
   ShowTransportArrow: "Показать стрелки транспорта",
   ShowTransportArrowDescHTML: "Отключение этого параметра скроет стрелки транспорта. Это может <i>немного</i> улучшить производительность на слабых устройствах. Улучшение производительности вступит в силу <b>после перезапуска игры</b>",
   ShowUnbuiltOnly: "Показывать только те здания, которые ещё не построены",
   Shrine: "Храм",
   SidePanelWidth: "Ширина Боковой Панели",
   SidePanelWidthDescHTML: "Изменение ширины боковой панели. <b>Для вступления в силу требуется перезапуск игры</b>",
   SiegeRam: "Осадный Таран",
   SiegeWorkshop: "Осадная Мастерская",
   Silicon: "Кремний",
   SiliconSmelter: "Кремниевый Завод",
   Skyscraper: "Небоскреб",
   Socialism: "Социализм",
   SocialismLevel4DescHTMLV2: "Единоразово генерирует науку, равную самой дешевой технологии <b>эпохи Мировых войн</b>",
   SocialismLevel5DescHTMLV2: "Единоразово генерирует науку, равную самой дешевой технологии <b>эпохи Холодной войны</b>",
   SocialismLevelX: "Социализм %{level} уровня",
   SocialNetwork: "Социальная Сеть",
   Socrates: "Сократ",
   SocratesDesc: "+%{value} Науки от Занятых Работников",
   Software: "Программное обеспечение",
   SoftwareCompany: "Компания-разработчик программного обеспечения",
   Sound: "Звук",
   SoundEffect: "Звуковые эффекты",
   SourceGreatPerson: "Великий Человек: %{person}",
   SourceGreatPersonPermanent: "Постоянный Великий Человек: %{person}",
   SourceIdeology: "Идеология: %{ideology}",
   SourceReligion: "Религия: %{religion}",
   SourceResearch: "Исследование: %{tech}",
   SourceTradition: "Традиция: %{tradition}",
   SpaceCenter: "Космический центр",
   Spacecraft: "Космический Корабль",
   SpacecraftFactory: "Завод Космических Кораблей",
   SpaceNeedle: "Спейс-Нидл",
   SpaceNeedleDesc: "+1 Счастья за каждое построенное Чудо",
   SpaceProgram: "Космическая Программа",
   Sports: "Спорт",
   Stable: "Конюшня",
   Stadium: "Стадион",
   StartFestival: "Да начнется Фестиваль!",
   Stateship: "Государство",
   StatisticsBuildings: "Здания",
   StatisticsBuildingsSearchText: "Введите название здания для поиска",
   StatisticsEmpire: "Империя",
   StatisticsExploration: "Разведка",
   StatisticsOffice: "Офис Статистики",
   StatisticsOfficeDesc: "Предоставляет статистику вашей Империи. Обучает Исследователей для изучения карты",
   StatisticsResources: "Ресурсы",
   StatisticsResourcesDeficit: "Дефицит",
   StatisticsResourcesDeficitDesc: "Производство: %{output} - Потребление: %{input}",
   StatisticsResourcesRunOut: "Закончится",
   StatisticsResourcesSearchText: "Введите название ресурса для поиска",
   StatisticsScience: "Наука",
   StatisticsScienceFromBuildings: "Наука От Зданий",
   StatisticsScienceFromWorkers: "Науки От Работников",
   StatisticsScienceProduction: "Производство Науки",
   StatisticsStalledTransportation: "Приостановленных Перевозок",
   StatisticsTotalTransportation: "Всего Перевозок",
   StatisticsTransportation: "Перевозка",
   StatisticsTransportationPercentage: "Процент Транспортных Работников",
   StatueOfLiberty: "Статуя Свободы",
   StatueOfLibertyDesc: "Все прилегающие постройки получают +N к Множителям Производства, Производительности Работников и Хранилища. N = Количество прилегающих построек одного типа к зданию, находящемуся в радиусе 1 плитки от Статуи Свободы",
   StatueOfZeus: "Статуя Зевса",
   StatueOfZeusDesc: "Производит на прилегающих Плитках случайные Месторождения, известных ресурсов. Все прилегающие здания I Тира получают +5 к Множителям Производства и Хранилища",
   SteamAchievement: "Достижения Steam",
   SteamAchievementDetails: "Посмотреть Достижения Steam",
   SteamEngine: "Паровой Двигатель",
   Steamworks: "Паровая Мастерская",
   Steel: "Сталь",
   SteelMill: "Сталелитейный Завод",
   StephenHawking: "Стивен Хокинг",
   Stock: "Акции",
   StockExchange: "Фондовая Биржа",
   StockMarket: "Фондовый Рынок",
   StockpileDesc: "Это здание будет перевозить %{capacity}x входных ресурсов за производственный цикл до достижения максимального значения",
   StockpileMax: "Максимальный запас",
   StockpileMaxDesc: "Это здание перестанет перевозить ресурс, как только его станет достаточно для %{cycle} производственных циклов",
   StockpileMaxUnlimited: "Не ограничено",
   StockpileMaxUnlimitedDesc: "Здание никогда не прекратит транспортировку ресурсов, пока хранилище не будет полным",
   StockpileSettings: "Входная Мощность Хранилища",
   Stone: "Камень",
   StoneAge: "Каменный Век",
   Stonehenge: "Стоунхендж",
   StonehengeDesc: "Все здания, которые потребляют или производят Камень, получают +1 к Множителю Производства",
   StoneQuarry: "Каменный Карьер",
   StoneTool: "Каменные Инструменты",
   StoneTools: "Каменные Инструменты",
   Storage: "Хранилище",
   StorageBaseCapacity: "Базовая Мощность",
   StorageMultiplier: "Множитель Хранилища",
   StorageUsed: "Хранилища Использовано",
   StPetersBasilica: "Собор Святого Петра",
   StPetersBasilicaDescV2: "Все церкви получают +5 к Множителю Хранилища. Генерирует науку, равную производству веры всех церквей",
   Submarine: "Подлодка",
   SubmarineYard: "Верфь Подлодок",
   SuleimanI: "Suleiman I",
   SummerPalace: "Летний Дворец",
   SummerPalaceDesc: "Все прилегающие здания, потребляющие или производящие Порох, освобождаются от -1 Счастья. Все здания, потребляющие или производящие Порох, получают +1 к Множителям Производства, Производительности Работников и Хранилища",
   Supercomputer: "Суперкомпьютер",
   SupercomputerLab: "Суперкомпьютерная лаборатория",
   SupporterPackRequired: "Требуется пакет поддержки",
   SupporterThankYou: "CivIdle удерживается на плаву благодаря щедрости следующих владельцев пакетов поддержки",
   SwissBank: "Swiss Bank",
   SwissBankDescV2: "Convert a chosen resource from adjacent warehouses to Koti (10 million in Sanskrit) - a tradeable resource that is worth 10M value. Each level of Swiss Bank add 1 Koti conversion per cycle, which is affected by Production Multiplier. Swiss Bank can store unlimited amount of Koti",
   Sword: "Меч",
   SwordForge: "Кузница Мечей",
   SydneyOperaHouse: "Сиднейский Оперный Театр",
   SydneyOperaHouseDescV2: "Сиднейский оперный театр",
   SyncToANewDevice: "Синхронизация с Новым Устройством",
   Synthetics: "Синтетика",
   TajMahal: "Тадж-Махал",
   TajMahalDescV2: "Рождается великий человек Классического Века и Великий Человек Средневековья. +5 к Множителю Мощности Строительства при улучшении зданий выше 20-го уровня",
   TangOfShang: "Император Тан",
   TangOfShangDesc: "+%{value} Науки от Незанятых Работников",
   Tank: "Танк",
   TankFactory: "Танковый Завод",
   TechAge: "Эпоха",
   TechGlobalMultiplier: "Усиление",
   TechHasBeenUnlocked: "%{tech} было открыто",
   TechProductionPriority: "Разблокирует приоритет зданий - позволяет установить приоритет производства для каждого здания",
   TechResourceTransportPreference: "Разблокирует транспортные предпочтения здания - позволяет установить, как здание транспортирует ресурсы, необходимые для его производства",
   TechResourceTransportPreferenceAmount: "Количество",
   TechResourceTransportPreferenceAmountTooltip: "Это здание будет предпочитать транспортировать ресурсы из зданий, в которых хранится большее количество ресурсов",
   TechResourceTransportPreferenceDefault: "Стандартно",
   TechResourceTransportPreferenceDefaultTooltip: "Не переопределять транспортные предпочтения для этого ресурса, вместо этого будут использоваться транспортные предпочтения здания",
   TechResourceTransportPreferenceDistance: "Дистанция",
   TechResourceTransportPreferenceDistanceTooltip: "Это здание предпочитает транспортировать ресурсы из зданий, которые находятся ближе по расстоянию",
   TechResourceTransportPreferenceOverrideTooltip: "У этого ресурса установлено предпочтение транспортировки: %{mode}",
   TechResourceTransportPreferenceStorage: "Хранилище",
   TechResourceTransportPreferenceStorageTooltip: "Это здание будет предпочитать транспортировку ресурсов из зданий с бóльшим процентом занятого Хранилища",
   TechStockpileMode: "Разблокирует режим запасов - это позволит настраивать запасы для каждого здания",
   Teleport: "Телепорт",
   TeleportDescHTML: "Телепорт генерируется <b>каждые %{time} секунд</b>. Телепорт можно использовать для <b>перемещения здания (за исключением чудес)</b> один раз",
   Television: "Телевидение",
   TempleOfArtemis: "Храм Артемиды",
   TempleOfArtemisDesc: "Все Кузницы Мечей и Арсеналы получат +5 к Уровню, когда завершится строительство. Все Кузницы Мечей и Арсеналы получают +1 к Множителям Производства, Производительности Работников и Хранилища",
   TempleOfHeaven: "Храм Неба",
   TempleOfHeavenDesc: "Все строения 10 уровня и выше получают +1 к Множителю Производительности Работников",
   TempleOfPtah: "Храм Птаха",
   TerracottaArmy: "Терракотовая Армия",
   TerracottaArmyDesc: "Все Железо-добывающие Лагеря получают +1 к Множителям Производства, Производительности Работников и Хранилища. Кузницы Железа получают +1 к Множителю Производства за каждый примыкающий Железодобывающий Лагерь, который построен на месте месторождения Железа",
   Thanksgiving: "День благодарения: Уолл-стрит обеспечивает двойной прирост для зданий и распространяется на взаимный фонд, хедж-фонд и майнер биткоинов, а так же +5 к Множителю Производства для Фонда Исследований",
   Theater: "Театр",
   Theme: "Тема",
   ThemeColor: "Цвет Темы",
   ThemeColorResearchBackground: "Фон Исследования",
   ThemeColorReset: "Вернуть по умолчанию",
   ThemeColorResetBuildingColors: "Сбросить Цвета Построек",
   ThemeColorResetResourceColors: "Сбросить Цвета Ресурсов",
   ThemeInactiveBuildingAlpha: "Яркость Неработающих Зданий",
   ThemePremiumTile: "This tile is only available for Supporter Pack owners",
   ThemeResearchHighlightColor: "Цвет Выбранного Исследования",
   ThemeResearchLockedColor: "Цвет",
   ThemeResearchUnlockedColor: "Цвет Закрытого Исследования",
   ThemeTransportIndicatorAlpha: "Яркость Значков Транспорта",
   Theocracy: "Теократия",
   TheoreticalData: "Теоретические Данные",
   ThePentagon: "Пентагон",
   ThePentagonDesc: "После постройки генерирует телепорты, которые можно использовать для перемещения зданий. Все здания в пределах 2 плиток получают +1 к Множителю продуктивности, рабочим и хранилища",
   TheWhiteHouse: "Белый Дом",
   ThomasEdison: "Томас Эдисон",
   ThomasGresham: "Томас Грэшем",
   Tile: "Плитка",
   TileBonusRefreshIn: "Tile bonus will refresh in <b>%{time}</b>",
   TimBernersLee: "Тим Бернерс-Ли",
   TimeWarp: "Ускорение Времени",
   TimeWarpWarning: "Ускорение на более высокой скорости, чем может выдержать ваш компьютер, может привести к потере данных: ИСПОЛЬЗУЙТЕ НА СВОЙ СТРАХ И РИСК",
   ToggleWonderEffect: "Переключить эффект Чуда",
   Tool: "Инструменты",
   TopkapiPalace: "Topkapı Palace",
   TopkapiPalaceDesc: "All buildings within 2 tile range get +X Storage Multiplier. X = 50% of its Production Multiplier (excluding Dynamic)",
   TotalEmpireValue: "Общая Стоимость Империи",
   TotalEmpireValuePerCycle: "Общая Стоимость Империи за Цикл",
   TotalEmpireValuePerCyclePerGreatPeopleLevel: "Общая Стоимость Империи за Цикл за Уровень Великих Людей",
   TotalEmpireValuePerWallSecond: "Общая Стоимость Империи в реальную секунду",
   TotalEmpireValuePerWallSecondPerGreatPeopleLevel: "Общая Стоимость Империи в реальную секунду за Уровень Великих Людей",
   TotalGameTimeThisRun: "Общее время игры в этом забеге",
   TotalScienceRequired: "Всего науки требуется",
   TotalStorage: "Общее Хранилище",
   TotalWallTimeThisRun: "Общее реальное время в этом забеге",
   TotalWallTimeThisRunTooltip: "Реальное время (т.е. реальное прошедшее время) измеряет фактическое время, затраченное на этот забег. Отличается от игрового времени тем, что Временной Разрыв в Петре и Оффлайн Продукция не влияет на Реальное время, но влияет на игровое время",
   TotalWorkers: "Всего Работников",
   TowerBridge: "Тауэрский Мост",
   TowerBridgeDesc: "После завершения строительства каждые 3600 циклов (1 час игрового времени) рождается великий человек из открытых эпох",
   TowerOfBabel: "Вавилонская башня",
   TowerOfBabelDesc: "Обеспечивает +2 к Множителю Производства всем зданиям, в которых есть хотя бы одно работающее здание, расположенное рядом с чудом.",
   TradeFillSound: "Звук 'Сделка Завершена'",
   TradeValue: "Стоимость Сделки",
   TraditionCommerce: "Коммерция",
   TraditionCultivation: "Культивация",
   TraditionDescHTML: "Выберите <b>Культивирование, Торговлю, Экспансия и Отвага</b> традицией империи. Вы <b>не можете сменить традицию</b> после того, как она выбрана. Вы можете разблокировать больше улучшений каждой традиции.",
   TraditionExpansion: "Экспансия",
   TraditionHonor: "Отвага",
   Train: "Поезд",
   TranslationPercentage: "%{language} на %{percentage} переведён. Помогите улучшить этот перевод на GitHub",
   TranslatorCredit: "WingedLeo, schizophrenia, r1j1k, Luc1ferSam91, vladbaldin666",
   Translators: "Переводчики",
   TransportAllocatedCapacityTooltip: "Мощность Строительства, используемая для транспортировки данного ресурса",
   TransportationWorkers: "Транспортирующие Работники",
   TransportCapacity: "Мощность Транспорта",
   TransportCapacityMultiplier: "Множитель Мощности Транспорта",
   TransportManualControlTooltip: "Транспортировать этот ресурс для строительства/улучшения",
   TransportPlanCache: "Кэширование транспортного плана",
   TransportPlanCacheDescHTML:
      "Кэширование транспортного плана: каждый цикл каждое здание вычисляет наилучший транспортный план на основе своих настроек — этот процесс требует высокой мощности ЦП. Включение этого параметра попытается кэшировать результат транспортного плана, если он все еще действителен, и, следовательно, уменьшит использование ЦП и падение частоты кадров. <b>Экспериментальная функция</b>",
   TribuneUpgradeDescGreatPeopleWarning: "В вашем текущем Пробеге есть Великие Люди. Вам следует <b>сначала переродиться</b>. Улучшение до ранга Квестора сбросит ваш текущий забег",
   TribuneUpgradeDescGreatPeopleWarningTitle: "Пожалуйста сначала Переродитесь",
   TribuneUpgradeDescV4:
      "Вы можете играть в полную игру как Трибун, если не планируете участвовать в <b>дополнительных</b> сетевых функциях. Чтобы получить неограниченный доступ к сетевым функциям, вам нужно будет обновиться до Квестора. <b>Это мера против ботов, чтобы игра оставалась бесплатной для всех.</b> Однако, <b>при обновлении до Квестора</b> вы можете переносить Великих Людей: <ul><li>До уровня <b>3</b> для Бронзового, Железного и Классического веков</li><li>До уровня <b>2</b> для Средневековья, Возрождения и Промышленного века</li><li>До уровня <b>1</b> для Мировых войн, Холодной войны и Информационного века</li></ul>Осколки Великих Людей выше уровня и уровни <b>Мудрости Эпох</b> <b>не могут</b> быть перенесены",
   TurnOffFullBuildings: "Отключить все %{building} с полным хранилищем",
   TurnOnTimeWarpDesc: "Стоит %{speed} Временного Разрыва за каждую секунду и ускоряет вашу Империю в %{speed}x раза.",
   Tutorial: "Обучение",
   TutorialPlayerFlag: "Выберите ваш игровой флаг",
   TutorialPlayerHandle: "Выберите ваше игровое имя",
   TV: "Телевидение",
   TVStation: "Телеканал",
   UnclaimedGreatPersonPermanent: "У вас есть неполученные <b>Постоянные Великие Люди</b>, нажмите здесь, чтобы получить",
   UnclaimedGreatPersonThisRun: "У вас есть неполученные <b>Великие Люди в этом Пробеге</b>, нажмите здесь, чтобы получить",
   UnexploredTile: "Неисследованная Плитка",
   UNGeneralAssemblyCurrent: "Текущая Генеральная Ассамблея ООН #%{id}",
   UNGeneralAssemblyMultipliers: "<b>+%{count}</b> к Множителям Производства, Производительности Работников и Хранилища для <b>%{buildings}</b>",
   UNGeneralAssemblyNext: "Предстоящая Генеральная Ассамблея ООН #%{id}",
   UNGeneralAssemblyVoteEndIn: "Вы можете изменить свой голос в любое время до окончания голосования через <b>%{time}</b>",
   UniqueBuildings: "Уникальные здания",
   UniqueTechMultipliers: "Уникальные множители технологий",
   UnitedNations: "ООН",
   UnitedNationsDesc: "Все здания IV, V и VI тиров получают +1 к Множителям Производства, Производительности Работников и Хранилища. Участвуйте в Генеральной ассамблее ООН и голосуйте, чтобы каждую неделю получать дополнительный бонус",
   University: "Университет",
   UnlockableResearch: "Доступные Исследования",
   UnlockBuilding: "Открыть",
   UnlockTechProgress: "Прогресс",
   UnlockXHTML: "Открыть <b>%{name}</b>",
   Upgrade: "Улучшить",
   UpgradeBuilding: "Улучшить",
   UpgradeBuildingNotProducingDescV2: "Это здание улучшается - <b>Производство остановлено до завершения улучшения</b>",
   UpgradeTo: "Улучшить до уровня %{level}",
   Uranium: "Уран",
   UraniumEnrichmentPlant: "Завод по Обогащению Урана",
   UraniumMine: "Урановая Шахта",
   Urbanization: "Урбанизация",
   UserAgent: "Пользовательский Клиент: %{driver}",
   View: "Посмотреть",
   ViewMenu: "Вид",
   ViewTechnology: "Посмотреть",
   Vineyard: "Виноградник",
   VirtualReality: "Виртуальная Реальность",
   Voltaire: "Вольтер",
   WallOfBabylon: "Стена Вавилона",
   WallOfBabylonDesc: "Все здания получают +N к Множителю Хранилища. N = количество открытых эпох / 2",
   WallStreet: "Уолл-стрит",
   WallStreetDesc: "Все здания, производящие монеты, банкноты, облигации, акции и валюту в радиусе 2 клеток получают +N к Множителю Производства. N = Случайное значение от 1 до 5, которое разное для каждого здания и меняется при каждом обновлении рынка. Двойной эффект Джона Д. Рокфеллера",
   WaltDisney: "Уолт Дисней",
   Warehouse: "Склад",
   WarehouseAutopilotSettings: "Настройки Автопилота",
   WarehouseAutopilotSettingsEnable: "Включить Автопилот",
   WarehouseAutopilotSettingsRespectCapSetting: "Требование Хранилища < Предела",
   WarehouseAutopilotSettingsRespectCapSettingTooltip: "Автопилот будет перевозить только те ресурсы, количество которых в Хранилище ниже предела",
   WarehouseDesc: "Транспортировка определенных ресурсов и создание дополнительного Хранилища",
   WarehouseExtension: "Разблокирует расширенный режим Базаров. Разрешает использовать Склады, прилегающие к Базарам для торговли с игроками.",
   WarehouseSettingsAutopilotDesc: "Этот Склад будет использовать свои незадействованные мощности для транспортировки ресурсов из зданий, которые имеют полное Хранилище. Текущая незанятая емкость: %{capacity}",
   WarehouseUpgrade: "Разблокирует режим автопилота для Склада. Бесплатная транспортировка между Складом и примыкающими Плитками",
   WarehouseUpgradeDesc: "Бесплатная транспортировка между Складом и примыкающими плитками",
   Warp: "Временной Разрыв",
   WarpSpeed: "Скорость ускорения времени",
   Water: "Вода",
   WellStockedTooltip: "Хорошо укомплектованные здания - это здания, у которых достаточно ресурсов для производства, к ним относятся здания, которые работают, имеют переполненное Хранилище или не простаивают из-за отсутствия Работников",
   WernherVonBraun: "Вернер фон Браун",
   Westminster: "Вестминстер",
   Wheat: "Пшеница",
   WheatFarm: "Пшеничная Ферма",
   WildCardGreatPersonDescV2: "При использовании становится великим человеком той же эпохи",
   WilliamShakespeare: "Уильям Шекспир",
   Wine: "Вино",
   Winery: "Винодельня",
   WishlistSpaceshipIdle: "Добавить Spaceship Idle в список желаемого",
   Wonder: "Чудо",
   WonderBuilderCapacityDescHTML: "<b>Мощность Строительства</b> при постройке Чудес зависит от <b>Эпохи</b> и <b>технологии</b>, которая открывает Чудо.",
   WondersBuilt: "Построенные Чудеса Света",
   WondersUnlocked: "Разблокированные Чудеса Света",
   WonderUpgradeLevel: "Уровень Чуда",
   Wood: "Дерево",
   Worker: "Работник",
   WorkerCapacityMultiplier: "Множитель Производительности Работников",
   WorkerHappinessPercentage: "Множитель Счастья",
   WorkerMultiplier: "Мощность Работника",
   WorkerPercentagePerHappiness: "%{value}% к Множителю за Каждое Счастье",
   Workers: "Работники",
   WorkersAvailableAfterHappinessMultiplier: "Работников, с Множителем Счастья",
   WorkersAvailableBeforeHappinessMultiplier: "Работников, без Множителя Счастья",
   WorkersBusy: "Работников Занято",
   WorkerScienceProduction: "Производство Науки Работниками",
   WorkersRequiredAfterMultiplier: "Требуется Работников",
   WorkersRequiredBeforeMultiplier: "Требуемая Мощность Работников",
   WorkersRequiredForProductionMultiplier: "Производственная Мощность за Работника",
   WorkersRequiredForTransportationMultiplier: "Транспортировочная Мощность за Работника",
   WorkersRequiredInput: "Транспортировка",
   WorkersRequiredOutput: "Производство",
   WorldWarAge: "Мировые Войны",
   WorldWideWeb: "Всемирная паутина",
   WritersGuild: "Гильдия Писателей",
   Writing: "Письменность",
   WuZetian: "Императрица У Цзэтянь",
   WuZetianDesc: "+%{value} к Множителю Мощности Транспортировки",
   Xuanzang: "Сюаньцзан",
   YangtzeRiver: "Река Янцзы",
   YangtzeRiverDesc: "Все здания, потребляющие Воду, получают +1 к Множителям Производства, Производительности Работников и Хранилища. Удваивает эффект Чжэн Хэ (Великий Человек). Каждый уровень Постоянной Императрицы У Цзэтянь (Великий Человек) дает +1 к Множителю Хранилища для всех зданий",
   YearOfTheSnake: "Год Змеи",
   YearOfTheSnakeDesc:
      "После завершения строительства, при входе в новую эпоху, вместо того, чтобы получать по одному великому человеку из каждой разблокированной эпохи, получите такое же количество великих людей в текущей эпохе. Все здания в радиусе 2 плиток получают +1 к Множителю производства. Это чудо можно улучшить, каждое дополнительное улучшение дает +1 к Множителю производства зданиям в радиусе 2 плиток. Это чудо может быть построено только в период лунного нового года (1.20 ~ 2.10)",
   YellowCraneTower: "Башня Жёлтого журавля",
   YellowCraneTowerDesc: "+1 вариант при выборе Великих Людей. Все здания в радиусе 1 Плитки получают +1 к Множителям Производства, Производительности Работников и Хранилища. Если построена рядом с Рекой Янцзы радиус увеличивается до 2 Плиток.",
   YuriGagarin: "Юрий Гагарин",
   ZagrosMountains: "Горы Загрос",
   ZagrosMountainsDesc: "Все соседние здания с множителем производства менее 5 получают +2 к Множителю Производства. Двойной эффект Навуходоносора II (Великого Человека)",
   ZahaHadid: "Заха Хадид",
   ZahaHadidDesc: "+%{value} к Множителю Строительства",
   Zenobia: "Зенобия",
   ZenobiaDesc: "+%{value}ч к Хранилищу Временного Разрыва в Петре",
   ZhengHe: "Чжэн Хэ",
   ZigguratOfUr: "Зиккурат Ура",
   ZigguratOfUrDescV2: "Каждые 10 счастья (ограничение) дают +1 к Множителю Производства для всех зданий, которые не производят работников и были открыты в предыдущей эпохе (максимум = количество разблокированных эпох / 2). Чудеса (включая природные) больше не дают +1 Счастья. Эффект можно отключить",
   Zoroaster: "Заратуштра",
   Zugspitze: "Цугшпитце",
   ZugspitzeDesc: "За каждую открытую эпоху вы получаете одно очко, которое может быть использовано для улучшения уровня любого Великого человека, родившегося в этом забеге",
};
