export const NL = {
   About: "Over CivIdle",
   AbuSimbel: "Abu Simbel",
   AbuSimbelDesc: "Double the effect of Ramesses II. All Adjacent wonders get +1 Happiness",
   AccountActiveTrade: "Actieve Handel",
   AccountChatBadge: "Chat Badge",
   AccountCustomColor: "Custom Color",
   AccountCustomColorDefault: "Default",
   AccountGreatPeopleLevelRequirement: "Required Great People Level",
   AccountLevel: "Account Rang",
   AccountLevelAedile: "Aedile",
   AccountLevelConsul: "Consul",
   AccountLevelMod: "Moderator",
   AccountLevelPlayTime: "Actieve Online Speeltijd > %{requiredTime} (Je speeltijd is %{actualTime})",
   AccountLevelPraetor: "Praetor",
   AccountLevelQuaestor: "Quaestor",
   AccountLevelSupporterPack: "Owns Supporter Pack",
   AccountLevelTribune: "Tribune",
   AccountLevelUpgradeConditionAnyHTML: "To upgrade your account, you only need to satisfy <b>one of the following</b> criteria:",
   AccountPlayTimeRequirement: "Required Play Time",
   AccountRankUp: "Upgrade Account Rank",
   AccountRankUpDesc: "All your progress will be carried over to your new rank",
   AccountRankUpTip: "Congratulations, your account is eligible for a higher rank - click here to upgrade!",
   AccountSupporter: "Supporter Pack Owner",
   AccountTradePriceRange: "Handelsprijsbereik",
   AccountTradeTileReservationTime: "Reserveringstijd Handelstegel",
   AccountTradeTileReservationTimeDesc: "Dit is de tijd dat je handelstegel voor jou gereserveerd blijft sinds je laatste online sessie. Nadat de reserveringsperiode is verstreken, wordt je tegel beschikbaar voor andere spelers",
   AccountTradeValuePerMinute: "Handelswaarde Per Minuut",
   AccountTypeShowDetails: "Toon Accountgegevens",
   AccountUpgradeButton: "Upgrade Naar Quaestor Rang",
   AccountUpgradeConfirm: "Account Upgrade",
   AccountUpgradeConfirmDescV2: "Upgrading your account will <b>reset your current run</b> and carry over permanent great people within the allowed levels. This <b>cannot</b> be undone, are you sure to continue?",
   Acknowledge: "Acknowledge",
   Acropolis: "Acropolis",
   ActorsGuild: "Acteursgilde",
   AdaLovelace: "Ada Lovelace",
   AdamSmith: "Adam Smith",
   AdjustBuildingCapacity: "Productiecapaciteit",
   AdvisorElectricityContent:
      "Power Plants provide two new systems to you. The first, 'Power' is indicated by the lightning bolt tiles adjacent to the power plant. Some buildings (starting with Radio in World Wars) have a 'requires power' indicator in their list of inputs. <b>This means they must be built on a lightning bolt tile to function</b>. Buildings that require power and have it, will also transmit power to the tiles adjacent to that building, so you can power them from each other as long as at least one is touching a power plant.<br><br>The other system 'electrification' can be applied to <b>any building anywhere</b> on the map as long as it doesn't produce science or workers. This uses up the power generated by the power plant to increase both the consumption and production of the building. More levels of electrification require larger and larger amounts of power. Electrifying buildings that also have 'requires power' is more efficient than electrifying the ones that don't.",
   AdvisorElectricityTitle: "Power and Electrification",
   AdvisorGreatPeopleContent:
      "Each time you enter a new age of technology, you will be able to select a Great Person from that age, and each previous age. These Great People give global bonuses that can increase production, science, happiness, and many other things.<br><br>These bonuses are permanent for the rest of the rebirth. When you rebirth, all of your Great People become permanent, and their bonus lasts forever.<br><br>Picking the same one in a later run will stack your permanent and in-run bonus, and when you rebirth with duplicates, the extras are stored and can be used to upgrade the permanent bonus. That is accessed in the <b>Manage Permanent Great People</b> menu in your Home Building.",
   AdvisorGreatPeopleTitle: "Great People",
   AdvisorHappinessContent:
      "Happiness is the core mechanic in CivIdle that limits expansion. You gain happiness by unlocking new technology, advancing to new ages, building wonders, from Great People who provide it, and a few other ways you can discover as you learn. <b>Each new building costs 1 happiness</b>. For each point above/below 0 happiness, you get a 2% bonus or penalty to your total workers (Capping at -50 and +50 Happiness). You can see a detailed breakdown of your happiness in your <b>Home Building's Happiness section</b>.",
   AdvisorHappinessTitle: "Keep Your People Happy",
   AdvisorOkay: "Got it, thanks!",
   AdvisorScienceContent:
      "Your busy workers generate science, which allows you to unlock new technology and advance your civilization. You can access the research menu a number of ways. By clicking on the science meter, by accessing your unlockable technologies in your Home Building, or by using the 'View' menu. These will all bring you to the tech tree, showing you all the technologies, as well as how much science is required for each. If you have enough science to learn a new technology, simply click on it and press 'unlock' in the sidebar menu. <b>Each new tier and age of technology requires more and more science, but you will unlock new and better ways to gain science as well.</b>",
   AdvisorScienceTitle: "Scientific Discovery!",
   AdvisorSkipAllTutorials: "Skip All Tutorials",
   AdvisorStorageContent:
      "While buildings have a decent amount of storage, they can fill up especially if left idle for a long time. <b>When buildings are full, they can no longer produce</b>. This isn't always an issue, since you clearly have a large stockpile since the building is full. But keeping things producing is generally better.<br><br>One way to address full storage is via a warehouse. When you build a warehouse, you get a menu of every product you've discovered, and you can set the warehouse to pull any products in any amounts as long as the total for all products is within what the warehouse can pull based on its level and storage multiplier.<br><br>An easy way to set up a warehouse is to check off each product you want to import into the warehouse, and use the 'redistribute among selected' buttons to split your import rate and storage equally. If you want buildings to also be able to pull out of the warehouse, make sure to turn on the 'export below max amount' option as well.",
   AdvisorStorageTitle: "Storage and Warehouses",
   AdvisorTraditionContent:
      "Some wonders (Chogha Zanbil, Luxor Temple, Big Ben) provide access to a new set of options, allowing you to customize the path of your rebirth. Each one allows you to choose from 1 of 4 options for your civilization's tradtion, religion and ideology respectively.<br><br>Once you choose one, that choice is locked in for that rebirth, though you can pick others in future rebirths. Once chosen, each one can also be upgraded a number of times by providing the necessary resources. The rewards in each tier are cumulative, so Tier 1 giving +1 production to X and Tier 2 giving +1 production to X means at Tier 2 you will have +2 production to X in total.",
   AdvisorTraditionTitle: "Choosing Paths and Upgradeable Wonders",
   AdvisorWonderContent:
      "Wonders are special buildings that provide global effects which can have a significant impact on your gameplay. In addition to their listed functions, all Wonders give +1 Happiness as well. You need to be careful though, as <b>Wonders require a LOT of materials, and have a higher than normal Builder Capacity as well</b>. This means that they can easily clear out your stockpiles of needed inputs, leaving your other buildings starving. <b>You can turn each input on and off freely</b>, allowing you to build it in stages while you stockpile enough materials to keep everything running.",
   AdvisorWonderTitle: "Wonders Of The World",
   AdvisorWorkerContent:
      "Every time a building produces or transports goods, this requires workers. If you don't have enough workers available, some buildings will fail to run that cycle. The obvious fix for this is to increase your total available workers by building or upgrading structures that make workers (Hut/House/Apartment/Condo).<br><br><b>Be aware though, that buildings turn off while upgrading, and can't provide any of their resources, which includes workers, so you might want to only upgrade one housing building at a time.</b> A good goal for the early stages of the game is to keep aboput 70% of your workers busy. If more than 70% are busy, upgrade/build housing. If fewer than 70% are busy, expand production.",
   AdvisorWorkerTitle: "Worker Management",
   Aeschylus: "Aeschylus",
   Agamemnon: "Agamemnon",
   AgeWisdom: "Age Wisdom",
   AgeWisdomDescHTML: "Each level of Age Wisdom provides <b>an equivalent level</b> of eligible Permanent Great People of that age - it can be upgraded with eligible Permanent Great People shards",
   AgeWisdomGreatPeopleShardsNeeded: "You need %{amount} more great people shards for the next Age Wisdom upgrade",
   AgeWisdomGreatPeopleShardsSatisfied: "You have enough great people shards for the next Age Wisdom upgrade",
   AgeWisdomNeedMoreGreatPeopleShards: "Need More Great People Shards",
   AgeWisdomNotEligible: "This Great Person is not eligible for Age Wisdom",
   AgeWisdomSource: "%{age} Wisdom: %{person}",
   AgeWisdomUpgradeWarningHTMLV3: "Age Wisdom <b>does not carry over</b> when upgrading from Tribune to Quaestor",
   AGreatPersonIsBorn: "Een Groot Persoon is Geboren",
   AircraftCarrier: "Aircraft Carrier",
   AircraftCarrierYard: "Aircraft Carrier Yard",
   Airplane: "Airplane",
   AirplaneFactory: "Airplane Factory",
   Akitu: "Akitu: Ziggurat Of Ur and Euphrates River apply to buildings unlocked in the current age",
   AlanTuring: "Alan Turing",
   AlanTuringDesc: "+%{value} Science From Idle Workers",
   AlbertEinstein: "Albert Einstein",
   Alcohol: "Alcohol",
   AldersonDisk: "Alderson Disk",
   AldersonDiskDesc: "+25 Happiness. This wonder can be upgraded and each additional upgrade provides +5 Happiness",
   Alloy: "Legering",
   Alps: "Alpen",
   AlpsDesc: "Elk 10e niveau van een gebouw krijgt +1 Productiecapaciteit (+1 Consumptie Vermenigvuldiger, +1 Productie Vermenigvuldiger)",
   Aluminum: "Aluminium",
   AluminumSmelter: "Aluminiumsmelterij",
   AmeliaEarhart: "Amelia Earhart",
   American: "American",
   AndrewCarnegie: "Andrew Carnegie",
   AngkorWat: "Angkor Wat",
   AngkorWatDesc: "Alle aangrenzende gebouwen krijgen +1 Werknemer Capaciteit Vermenigvuldiger. Levert 1000 Werknemers",
   AntiCheatFailure: "Your account rank has been restricted due to <b>failing to pass anti-cheat</b> check. Contact the developer if you want to appeal this",
   AoiMatsuri: "Aoi Matsuri: Mount Fuji generates double the warp",
   Apartment: "Appartement",
   Aphrodite: "Aphrodite",
   AphroditeDescV2: "+1 Builder Capacity Multiplier for each level when upgrading buildings over Level 20. All unlocked Classical Age permanent great people get +1 level this run",
   ApolloProgram: "Apollo Program",
   ApolloProgramDesc: "All rocket factories get +2 Production, Worker Capacity and Storage Multiplier. Satellite factories, spaceship factories and nuclear missile silos get +1 Production Multiplier for each adjacent rocket factory",
   ApplyToAll: "Toepassen Op Alles",
   ApplyToAllBuilding: "Toepassen Op Alle %{building}",
   ApplyToBuildingInTile: "Apply To All %{building} Within %{tile} Tile",
   ApplyToBuildingsToastHTML: "Successfully applied to <b>%{count} %{building}</b>",
   Aqueduct: "Aquaduct",
   ArcDeTriomphe: "Arc de Triomphe",
   ArcDeTriompheDescV2: "Every 1 happiness (capped) provides +1 builder capacity to all buildings",
   Archimedes: "Archimedes",
   Architecture: "Architectuur",
   Aristophanes: "Aristophanes",
   AristophanesDesc: "+%{value} Happiness",
   Aristotle: "Aristotle",
   Arithmetic: "Rekenkundig",
   Armor: "Harnas",
   Armory: "Wapenkamer",
   ArtificialIntelligence: "Kunstmatige Intelligentie",
   Artillery: "Artillery",
   ArtilleryFactory: "Artillery Factory",
   AshokaTheGreat: "Ashoka the Great",
   Ashurbanipal: "Ashurbanipal",
   Assembly: "Assemblage",
   Astronomy: "Astronomie  ",
   AtomicBomb: "Atomic Bomb",
   AtomicFacility: "Atomic Facility",
   AtomicTheory: "Atomic Theory",
   Atomium: "Atomium",
   AtomiumDescV2: "All buildings that produce science within 2 tile range get +5 Production Multiplier. Generate science that is equal to the science production within 2 tile range. When completed, generate one-time science equivalent to the cost of the most expensive unlocked technology",
   Autocracy: "Autocratie",
   Aviation: "Aviation",
   Babylonian: "Babylonian",
   BackToCity: "Terug Naar Stad",
   BackupRecovery: "Backup Recovery",
   Bakery: "Bakkerij",
   Ballistics: "Ballistics",
   Bank: "Bank",
   Banking: "Bankwezen",
   BankingAdditionalUpgrade: "Alle gebouwen die niveau 10 of hoger zijn krijgen +1 Opslag Vermenigvuldiger",
   Banknote: "Bankbiljet",
   BaseCapacity: "Base Capacity",
   BaseConsumption: "Basisverbruik",
   BaseMultiplier: "Basisvermenigvuldiger",
   BaseProduction: "Basisproductie",
   BastilleDay: "Bastille Day: Double the effect of Centre Pompidou and Arc de Triomphe. Double the Culture generation from Mont Saint-Michel",
   BatchModeTooltip: "%{count} buildings are currently selected. Upgrade will apply to all selected buildings",
   BatchSelectAllSameType: "All Same Type",
   BatchSelectAnyType1Tile: "Any Type in 1 Tile",
   BatchSelectAnyType2Tile: "Any Type in 2 Tile",
   BatchSelectAnyType3Tile: "Any Type in 3 Tile",
   BatchSelectSameType1Tile: "Same Type in 1 Tile",
   BatchSelectSameType2Tile: "Same Type in 2 Tile",
   BatchSelectSameType3Tile: "Same Type in 3 Tile",
   BatchSelectSameTypeSameLevel: "Same Type Same Level",
   BatchSelectThisBuilding: "This Building",
   BatchStateSelectActive: "Active",
   BatchStateSelectAll: "All",
   BatchStateSelectTurnedFullStorage: "Full Storage",
   BatchStateSelectTurnedOff: "Turned Off",
   BatchUpgrade: "Batch Upgrade",
   Battleship: "Battleship",
   BattleshipBuilder: "Battleship Builder",
   BigBen: "Big Ben",
   BigBenDesc: "+2 Science From Busy Workers. Choose an empire ideology, unlock more boost with each choice",
   Biplane: "Biplane",
   BiplaneFactory: "Biplane Factory",
   Bitcoin: "Bitcoin",
   BitcoinMiner: "Bitcoin Miner",
   BlackForest: "Black Forest",
   BlackForestDesc: "When discovered, reveals all wood tiles on the map. Spawn wood on adjacent tiles. All buildings that consume Wood or Lumber get +5 Production Multiplier",
   Blacksmith: "Smederij",
   Blockchain: "Blockchain",
   BlueMosque: "Blue Mosque",
   BlueMosqueDesc: "All wonders provide +1 Production, Worker Capacity and Storage Multiplier to adjacent buildings. When constructed next to Hagia Sophia, provide extra +1 Production, Worker Capacity and Storage Multiplier",
   BobHope: "Bob Hope",
   BobHopeDesc: "+%{value} Happiness",
   Bond: "Obligatie",
   BondMarket: "Obligatiemarkt",
   Book: "Boek",
   BoostCyclesLeft: "Boost Cycles Left",
   BoostDescription: "+%{value} %{multipliers} voor %{buildings}",
   Borobudur: "Borobudur",
   BorobudurDesc: "Borobudur",
   BranCastle: "Kasteel Bran",
   BranCastleDesc: "Kasteel Bran",
   BrandenburgGate: "Brandenburger Tor",
   BrandenburgGateDesc: "Alle kolenmijnen en oliebronnen krijgen +1 Productie, Opslag en Werknemer Capaciteit Vermenigvuldiger. Olie raffinaderijen krijgen +1 Productie, Opslag en Werknemer Capaciteit Vermenigvuldiger voor elke aangrenzende olietegel",
   Bread: "Brood",
   Brewery: "Brouwerij",
   Brick: "Baksteen",
   Brickworks: "Steenfabriek",
   BritishMuseum: "British Museum",
   BritishMuseumChooseWonder: "Choose a Wonder",
   BritishMuseumDesc: "After constructed, can transform into to a unique wonder from other civilizations",
   BritishMuseumTransform: "Transform",
   Broadway: "Broadway",
   BroadwayCurrentlySelected: "Currently selected",
   BroadwayDesc: "A great person of the current age and a great person of the previous age are born. Select a great person and double his/her effect",
   BronzeAge: "Bronstijd",
   BronzeTech: "Brons",
   BuddhismLevelX: "Buddhism %{level}",
   Build: "Bouw",
   BuilderCapacity: "Bouwcapaciteit",
   BuildingColor: "Gebouwkleur",
   BuildingColorMatchBuilding: "Kopieer Kleur Van Gebouw",
   BuildingColorMatchBuildingTooltip: "Kopieer hulpbronkleur van het gebouw dat deze hulpbron produceert. Als meerdere gebouwen deze hulpbron produceren, wordt er willekeurig een geselecteerd",
   BuildingDefaults: "Building Defaults",
   BuildingDefaultsCount: "%{count} properties are overriden in building default",
   BuildingDefaultsRemove: "Clear all property overrides",
   BuildingEmpireValue: "Building Empire Value / Resource Empire Value",
   BuildingMultipliers: "Boost",
   BuildingName: "Naam",
   BuildingNoMultiplier: "%{building} is <b>not affected</b> by any multipliers (production, worker capacity, storage, etc)",
   BuildingSearchText: "Typ een gebouw- of grondstofnaam om te zoeken",
   BuildingTier: "Niveau",
   Cable: "Cable",
   CableFactory: "Cable Factory",
   Calendar: "Kalender",
   CambridgeUniversity: "Cambridge University",
   CambridgeUniversityDesc: "+1 Age Wisdom level for Renaissance and ages after",
   CambridgeUniversitySource: "Cambridge University (%{age})",
   Cancel: "Annuleren",
   CancelAllUpgradeDesc: "Cancel all %{building} upgrades",
   CancelUpgrade: "Upgrade Annuleren",
   CancelUpgradeDesc: "Alle reeds getransporteerde bronnen blijven in de opslag",
   Cannon: "Kanon",
   CannonWorkshop: "Kanon Workshop",
   CannotEarnPermanentGreatPeopleDesc: "Omdat dit een proefrun is, kunnen er geen permanente grote mensen verdiend worden",
   Capitalism: "Kapitalisme",
   Cappadocia: "Cappadocia",
   CappadociaDesc: "All buildings within 3 tile range get +1 Production, Worker Capacity and Storage Multiplier for every level above Level 30",
   Car: "Car",
   Caravansary: "Karavanserai",
   CaravansaryDesc: "Handel grondstoffen met andere spelers en bied extra opslag",
   Caravel: "Karveel",
   CaravelBuilder: "Karveelbouwer",
   CarFactory: "Car Factory",
   CarlFriedrichGauss: "Carl Friedrich Gauss",
   CarlFriedrichGaussDesc: "+%{idle} Science from Idle Workers. +%{busy} Science from Busy Workers",
   CarlSagan: "Carl Sagan",
   Census: "Volkstelling",
   CentrePompidou: "Centre Pompidou",
   CentrePompidouDesc:
      "Once constructed, all buildings get +1 Production and +2 Storage Multiplier. The wonder will persist if the current run reaches Information Age and the next run is a different civilization. The wonder gets +1 level at rebirth for each run that reaches Information Age with a unique civilization. Each level provides +1 Production and +2 Storage Multiplier. The value of this wonder is excluded from total empire value and British Museum cannot transform into this wonder",
   CentrePompidouWarningHTML: "Centre Pompidou will disappear if you rebirth as <b>%{civ}</b>",
   CerneAbbasGiant: "Cerne Abbas Giant",
   CerneAbbasGiantDesc: "A great person of the current age is born when a wonder is constructed",
   ChangePlayerHandle: "Wijzig",
   ChangePlayerHandleCancel: "Annuleer",
   ChangePlayerHandledDesc: "Kies een unieke spelersnaam van 5 ~ 16 tekens lang. Je spelersnaam mag alleen letters en cijfers bevatten",
   Chariot: "Strijdwagen",
   ChariotWorkshop: "Strijdwagenwerkplaats",
   Charlemagne: "Karel de Grote",
   CharlesDarwin: "Charles Darwin",
   CharlesDarwinDesc: "+%{value} Wetenschap Van Bezette Werknemers",
   CharlesMartinHall: "Charles Martin Hall",
   CharlesParsons: "Charles Parsons",
   CharlieChaplin: "Charlie Chaplin",
   CharlieChaplinDesc: "+%{value} Happiness",
   Chat: "Chat",
   ChatChannel: "Chatkanaal",
   ChatChannelLanguage: "Taal",
   ChatHideLatestMessage: "Verberg Laatste Berichtinhoud",
   ChatNoMessage: "Geen chatberichten",
   ChatReconnect: "Verbinding verbroken, opnieuw verbinden...",
   ChatSend: "Verstuur",
   CheckInAndExit: "Check In And Exit",
   CheckInCloudSave: "Check In Save",
   CheckOutCloudSave: "Check Out Save",
   Cheese: "Kaas",
   CheeseMaker: "Kaasmaker",
   Chemistry: "Scheikunde",
   ChesterWNimitz: "Chester W. Nimitz",
   ChichenItza: "Chichen Itza",
   ChichenItzaDesc: "Alle aangrenzende gebouwen krijgen +1 Productie Vermenigvuldiger, Opslag en Werknemer Capaciteit Vermenigvuldiger",
   Chinese: "Chinese",
   ChoghaZanbil: "Chogha Zanbil",
   ChoghaZanbilDescV2: "Choose an empire tradition, unlock more boost with each choice",
   ChooseGreatPersonChoicesLeft: "You have %{count} choices left",
   ChristianityLevelX: "Christianity %{level}",
   Church: "Kerk",
   CircusMaximus: "Circus Maximus",
   CircusMaximusDescV2: "+5 Happiness. All Musician's Guilds, Writer's Guilds and Painter's Guilds get +1 Production and Storage Multiplier",
   CityState: "Stad Staat",
   CityViewMap: "Stad",
   CivGPT: "CivGPT",
   CivIdle: "CivIdle",
   CivIdleInfo: "Met trots gepresenteerd door Fish Pond Studio",
   Civilization: "Civilization",
   CivilService: "Ambtenarij",
   CivOasis: "CivOasis",
   CivTok: "CivTok",
   ClaimedGreatPeople: "Claimed Great People",
   ClaimedGreatPeopleTooltip: "You have %{total} great people at rebirth, %{claimed} of them are already claimed",
   ClassicalAge: "Klassieke Oudheid",
   ClearAfterUpdate: "Verwijder Alle Handel Na Marktupdate",
   ClearSelected: "Geselecteerden Wissen",
   ClearSelection: "Selectie Wissen",
   ClearTransportPlanCache: "Clear Transport Plan Cache",
   Cleopatra: "Cleopatra",
   CloneFactory: "Clone Factory",
   CloneFactoryDesc: "Clone any resources",
   CloneFactoryInputDescHTML: "Clone Factory can only clone <b>%{res}</b> directly transported from <b>%{buildings}</b>",
   CloneLab: "Clone Lab",
   CloneLabDesc: "Convert any resources into Science",
   CloneLabScienceMultiplierHTML: "Production multipliers that <b>only apply to science production buildings</b> (e.g. production multipliers from Atomium) <b>do not apply</b> to Clone Lab",
   Cloth: "Doek",
   CloudComputing: "Cloudcomputing",
   CloudSaveRefresh: "Refresh",
   CloudSaveReturnToGame: "Return To Game",
   CNTower: "CN Tower",
   CNTowerDesc: "All movie studios, radio stations and TV stations are exempt from -1 Happiness. All buildings unlocked in World Wars and Cold War get +N Production, Worker Capacity and Storage Multiplier. N = Difference between the tier and the age of the building",
   Coal: "Steenkool",
   CoalMine: "Kolenmijn",
   CoalPowerPlant: "Kolencentrale",
   Coin: "Munt",
   CoinMint: "Muntfabriek",
   ColdWarAge: "Koude Oorlog",
   CologneCathedral: "Cologne Cathedral",
   CologneCathedralDesc:
      "When constructed, generate one-time science equivalent to the cost of the most expensive technology in the current age. All buildings that produce science (excluding Clone Lab) get +1 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to all buildings that produce science (excluding Clone Lab)",
   Colonialism: "Kolonialisme",
   Colosseum: "Colosseum",
   ColosseumDescV2: "Chariot Workshops are exempt from -1 happiness. Consumes 10 chariots and produce 10 happiness. Each unlocked age gives 2 extra happiness",
   ColossusOfRhodes: "Kolossus van Rodos",
   ColossusOfRhodesDesc: "Alle aangrenzende gebouwen die geen werknemers produceren krijgen +1 Geluk",
   Combustion: "Verbranding",
   Commerce4UpgradeHTMLV2: "When unlocked, all <b>adjacent banks</b> get free upgrade to <b>level 30</b>",
   CommerceLevelX: "Commerce %{level}",
   Communism: "Communism",
   CommunismLevel4DescHTML: "A great person of <b>Industrial Age</b> and a great person of <b>World Wars Age</b> are born",
   CommunismLevel5DescHTML: "A great person of <b>Cold War Age</b> is born. When entering a new age, get <b>2 additional</b> great people of that age",
   CommunismLevelX: "Communism Level %{level}",
   Computer: "Computer",
   ComputerFactory: "Computer Factory",
   ComputerLab: "Computer Lab",
   Concrete: "Concrete",
   ConcretePlant: "Cement Plant",
   Condo: "Condo",
   ConfirmDestroyResourceContent: "Je staat op het punt om %{amount} %{resource} te vernietigen. Dit kan niet ongedaan gemaakt worden",
   ConfirmNo: "Nee",
   ConfirmYes: "Ja",
   Confucius: "Confucius",
   ConfuciusDescV2: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   ConnectToADevice: "Connect To A Device",
   Conservatism: "Conservatism",
   ConservatismLevelX: "Conservatism Level %{level}",
   Constitution: "Grondwet",
   Construction: "Bouw",
   ConstructionBuilderBaseCapacity: "Basis Capaciteit",
   ConstructionBuilderCapacity: "Bouwcapaciteit",
   ConstructionBuilderMultiplier: "Capaciteit Vermenigvuldiger",
   ConstructionBuilderMultiplierFull: "Bouwcapaciteit Vermenigvuldiger",
   ConstructionCost: "Bouwkosten: %{cost}",
   ConstructionDelivered: "Delivered",
   ConstructionPriority: "Bouwprioriteit",
   ConstructionProgress: "Voortgang",
   ConstructionResource: "Resource",
   Consume: "Verbruik",
   ConsumeResource: "Verbruik: %{resource}",
   ConsumptionMultiplier: "Verbruiksvermenigvuldiger",
   ContentInDevelopment: "Inhoud In Ontwikkeling",
   ContentInDevelopmentDesc: "Deze spelinhoud is nog in ontwikkeling en zal beschikbaar zijn in een toekomstige spelupdate, blijf op de hoogte!",
   Copper: "Koper",
   CopperMiningCamp: "Kopermijnkamp",
   CosimoDeMedici: "Cosimo de' Medici",
   Cotton: "Katoen",
   CottonMill: "Katoenfabriek",
   CottonPlantation: "Katoenplantage",
   Counting: "Tellen",
   Courthouse: "Gerechtshof",
   CristoRedentor: "Christus de Verlosser",
   CristoRedentorDesc: "Christus de Verlosser",
   CrossPlatformAccount: "Platform Account",
   CrossPlatformConnect: "Connect",
   CrossPlatformSave: "Cross Platform Save",
   CrossPlatformSaveLastCheckIn: "Last Check In",
   CrossPlatformSaveStatus: "Current Status",
   CrossPlatformSaveStatusCheckedIn: "Checked In",
   CrossPlatformSaveStatusCheckedOut: "Checked Out on %{platform}",
   CrossPlatformSaveStatusCheckedOutTooltip: "Your cross platform save has been checked out on another platform, you have to check in on that platform before you can check out on this platform",
   Cultivation4UpgradeHTML: "A great person of <b>Renaissance Age</b> is born",
   CultivationLevelX: "Cultivation %{level}",
   Culture: "Cultuur",
   Culus: "Cülus: Double the effect of Cappadocia. Mount Ararat's effect becomes based on square root of Effective Great People Level, instead of cubic root",
   CurrentLanguage: "Nederlands",
   CurrentPlatform: "Current Platform",
   CursorBigOldFashioned: "3D (Big)",
   CursorOldFashioned: "3D",
   CursorStyle: "Cursor Style",
   CursorStyleDescHTML: "Change the style of the cursor. <b>Require restarting your game to take effect</b>",
   CursorSystem: "System",
   Cycle: "Cycle",
   CyrusII: "Cyrus II",
   DairyFarm: "Dairy Farm",
   DefaultBuildingLevel: "Default Builing Level",
   DefaultConstructionPriority: "Standaard Bouwprioriteit",
   DefaultProductionPriority: "Standaard Productieprioriteit",
   DefaultStockpileMax: "Standaard Max Voorraad",
   DefaultStockpileSettings: "Standaard Voorraad Invoercapaciteit",
   DeficitResources: "Deficit Resources",
   Democracy: "Democratie",
   DemolishAllBuilding: "Demolish All %{building} Within %{tile} Tile",
   DemolishAllBuildingConfirmContent: "Are you sure about demolishing %{count} %{name}?",
   DemolishAllBuildingConfirmTitle: "Demolish %{count} Building(s)?",
   DemolishBuilding: "Demolish Building",
   DennisRitchie: "Dennis Ritchie",
   Deposit: "Bron",
   DepositTileCountDesc: "%{count} tegel(s) van %{deposit} kunnen worden gevonden in %{city}",
   Dido: "Dido",
   Diplomacy: "Diplomacy",
   DistanceInfinity: "Unlimited",
   DistanceInTiles: "Distance (In Tiles)",
   DolmabahcePalace: "Dolmabahçe Palace",
   Drilling: "Boren",
   DukeOfZhou: "Hertog van Zhou",
   DuneOfPilat: "Dune of Pilat",
   DuneOfPilatDesc: "In each age, double the age wisdom for the previous age",
   DynamicMultiplierTooltip: "This multiplier is dynamic - it will not affect workers and storage",
   Dynamite: "Dynamiet",
   DynamiteWorkshop: "Dynamiet Workshop",
   DysonSphere: "Dyson Sphere",
   DysonSphereDesc: "All buildings get +5 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to all buildings",
   EasterBunny: "Easter Bunny",
   EasterBunnyDesc: "Once constructed, 10% of the extra Great People at Rebirth from this run will carry forward to the next run and are born after building the Easter Bunny in the new run. This wonder can only be constructed during April",
   EastIndiaCompany: "East India Company",
   EastIndiaCompanyDescV2:
      "This wonder accumulates the total value of your completed player trade transactions. For every 2,000 trade value, all buildings adjacent to caravansaries get a +0.5 Production Multiplier for 1 cycle. This wonder can be upgraded and each upgrade provides an additional +0.5 Production Multiplier. A trade transaction counts when you either fulfill another player's trade request or when your own trade request is fulfilled. Multiple boosts stack by extending the duration",
   Education: "Onderwijs",
   EffectiveGreatPeopleLevel: "Effective Great People Level",
   EffectiveGreatPeopleLevelDesc: "Effective great people level is the sum of all permanent great people level and age wisdom level. It measures the effect boost provided by great people and age wisdom",
   Egyptian: "Egyptian",
   EiffelTower: "Eiffeltoren",
   EiffelTowerDesc: "Alle aangrenzende staalfabrieken krijgen +N Productie, Opslag en Werknemer Vermenigvuldiger. N = Aantal aangrenzende staalfabrieken",
   Elbphilharmonie: "Elbphilharmonie",
   ElbphilharmonieDesc: "All buildings within 3 tile range get +1 Production Multiplier for each adjacent working building that has different tier",
   Electricity: "Elektriciteit",
   Electrification: "Elektrificatie",
   ElectrificationPowerRequired: "Power Required",
   ElectrificationStatusActive: "Actief",
   ElectrificationStatusDesc: "Both buildings that require power and buildings that do not require power can be electrified. However, buildings that require power provides higher electrification efficiency",
   ElectrificationStatusNoPowerV2: "Not Enough Power",
   ElectrificationStatusNotActive: "Niet Actief",
   ElectrificationStatusV2: "Electrification Status",
   ElectrificationUpgrade: "Ontgrendel elektrificatie. Sta gebouwen toe om stroom te verbruiken om de productie te verhogen",
   Electrolysis: "Elektrolyse",
   ElvisPresley: "Elvis Presley",
   ElyseePalace: "Élysée Palace",
   EmailDeveloper: "Email Developer",
   Embassy: "Embassy",
   EmperorWuOfHan: "Emperor Wu of Han",
   EmpireValue: "Empire Value",
   EmpireValueByHour: "Empire Value By Hour",
   EmpireValueFromBuilding: "Empire Value from Building",
   EmpireValueFromBuildingsStat: "From Buildings",
   EmpireValueFromResources: "From Resources",
   EmpireValueFromResourcesStat: "From Resources",
   EmpireValueIncrease: "Empire Value Increase",
   EmptyTilePageBuildLastBuilding: "Bouw Laatste Gebouw",
   EndConstruction: "Einde Constructie",
   EndConstructionDescHTML: "Wanneer je de bouw beëindigt, zullen alle reeds gebruikte bronnen <b>niet worden teruggegeven</b>",
   Engine: "Motor",
   Engineering: "Ontwikkelen",
   English: "English",
   Enlightenment: "Verlichting",
   Enrichment: "Enrichment",
   EnricoFermi: "Enrico Fermi",
   EstimatedTimeLeft: "Geschatte Tijd Over",
   EuphratesRiver: "Euphrates River",
   EuphratesRiverDesc:
      "Every 10% of busy workers that in production (not transporting) provides +1 Production Multiplier to all buildings that do not produce workers (max = number of unlocked ages / 2). When the Hanging Garden is built next to it, the Hanging Garden gets +1 effect for each age after the Hanging Garden is unlocked. When discovered, spawn water on all adjacent tiles that do not have deposits",
   ExpansionLevelX: "Expansion %{level}",
   Exploration: "Verkenning",
   Explorer: "Explorer",
   ExplorerRangeUpgradeDesc: "Increase the explorer's range to %{range}",
   ExploreThisTile: "Send An Explorer",
   ExploreThisTileHTML: "An explorer will explore <b>this tile and its adjacent tiles</b>. Explorers are generated in %{name}. You have %{count} explorers left",
   ExtraGreatPeople: "%{count} Extra Great People",
   ExtraGreatPeopleAtReborn: "Extra Grote Personen Bij Herboren",
   ExtraTileInfoType: "Extra Tile Info",
   ExtraTileInfoTypeDesc: "Choose what information is shown below each tile",
   ExtraTileInfoTypeEmpireValue: "Empire Value",
   ExtraTileInfoTypeNone: "None",
   ExtraTileInfoTypeStoragePercentage: "Storage Percentage",
   Faith: "Geloof",
   Farming: "Landbouwen",
   FavoriteBuildingAdd: "Toevoegen Aan Favorieten",
   FavoriteBuildingEmptyToast: "Je hebt geen favoriete gebouwen",
   FavoriteBuildingRemove: "Verwijderen Uit Favorieten",
   FeatureRequireQuaestorOrAbove: "This feature requires Quaestor rank or above",
   Festival: "Festival",
   FestivalCycle: "Festival Cycle",
   FestivalTechTooltipV2: "Positive Happiness (max. 50) is converted into festival points. For every %{point} festival points, your empire enters a festival cycle, granting a significant map-specific boost. The festival on this map is %{desc}",
   FestivalTechV2: "Unlock festival - positive Happiness (max. 50) is converted into festival points. For every %{point} festival points, your empire enters a festival cycle, granting a significant map-specific boost",
   Feudalism: "Feodalisme",
   Fibonacci: "Fibonacci",
   FibonacciDescV2: "+%{idle} Science from Idle Workers. +%{busy} Science from Busy Workers. Fibonacci's permanent upgrade cost follows Fibonacci sequence",
   FighterJet: "Fighter Jet",
   FighterJetPlant: "Fighter Jet Plant",
   FilterByAge: "Filter by Age",
   FinancialArbitrage: "Financial Arbitrage",
   FinancialLeverage: "Financial Leverage",
   Fire: "Vuur",
   Firearm: "Vuurwapen",
   FirstTimeGuideNext: "Volgende",
   FirstTimeTutorialWelcome: "Welcome to CivIdle",
   FirstTimeTutorialWelcome1HTML:
      "Welcome to CivIdle. In this game, you will run your own empire: <b>manage productions, unlock technologies, trade resources with other players, create great people and build world wonders</b>.<br><br>Drag your mouse to move around. Use the scroll wheel to zoom in or out. Click an empty tile to build new buildings, click a building to inspect it.<br><br>Certain buildings like Stone Quarry and Logging Camp need to be built on top of the resource tile. I recommend placing a Hut, which provides worker, next to the fog - the building will take some time to build. After the completion, it will reveal the fog nearby.",
   FirstTimeTutorialWelcome2HTML:
      "Buildings can be upgraded - it costs resources and takes time. When a buildings is being upgraded, <b>it will no longer produce</b>. This includes buildings that provide workers, <b>so never upgrade all your buildings at the same time!</b><br><br>As your empire grows, you will get more science and unlock new technologies. I will tell you more about it when we get there but you can go to View -> Research to take a quick look<br><br>",
   FirstTimeTutorialWelcome3HTML: "Now you know all the basics of the game, you can start building your empire. But before I let you go, you should <b>choose yourself a player handle</b> and say hi in the in-game chat. We have an amazingly helpful community: if you get lost, don't be afraid to ask!",
   Fish: "Vis",
   FishPond: "Visvijver",
   FlorenceNightingale: "Florence Nightingale",
   FlorenceNightingaleDesc: "+%{value} Geluk",
   Flour: "Bloem",
   FlourMill: "Meelfabriek",
   FontSizeScale: "Font Size Scale",
   FontSizeScaleDescHTML: "Change the font size scale of the game's UI. <b>Setting the scale greater than 1x might break some UI layouts</b>",
   ForbiddenCity: "Verboden Stad",
   ForbiddenCityDesc: "Alle Papiermakers, Schrijversgilden en Drukkerijen krijgen +1 Productie Vermenigvuldiger, Werknemer Capaciteit Vermenigvuldiger en Opslag Vermenigvuldiger",
   Forex: "Forex",
   ForexMarket: "Forex Market",
   FrankLloydWright: "Frank Lloyd Wright",
   FrankLloydWrightDesc: "+%{value} Builder Capacity Multiplier",
   FrankWhittle: "Frank Whittle",
   FreeThisWeek: "Free This Week",
   FreeThisWeekDescHTMLV2: "<b>Every week</b>, one of the premium civilizations is free to play. This week's free civilization is <b>%{city}</b>",
   French: "French",
   Frigate: "Fregat",
   FrigateBuilder: "Fregatbouwer",
   Furniture: "Meubilair",
   FurnitureWorkshop: "Meubelworkshop",
   Future: "Future",
   GabrielGarciaMarquez: "Gabriel García Márquez",
   GabrielGarciaMarquezDesc: "+%{value} Happiness",
   GalileoGalilei: "Galileo Galilei",
   GalileoGalileiDesc: "+%{value} Science From Idle Workers",
   Galleon: "Galjoen",
   GalleonBuilder: "Galjoenbouwer",
   Gameplay: "Spelverloop",
   Garment: "Kledingstuk",
   GarmentWorkshop: "Kledingmakerij",
   GasPipeline: "Gaspipeline",
   GasPowerPlant: "Gas Power Plant",
   GatlingGun: "Gatlinggeweer",
   GatlingGunFactory: "Gatlinggeweerfabriek",
   Genetics: "Genetica",
   Geography: "Geografie",
   GeorgeCMarshall: "George C. Marshall",
   GeorgeWashington: "George Washington",
   GeorgiusAgricola: "Georgius Agricola",
   German: "German",
   Glass: "Glas",
   Glassworks: "Glasfabriek",
   GlobalBuildingDefault: "Global Builing Default",
   Globalization: "Globalisering",
   GoBack: "Ga Terug",
   Gold: "Goud",
   GoldenGateBridge: "Golden Gate Bridge",
   GoldenGateBridgeDesc: "Golden Gate Bridge",
   GoldenPavilion: "Golden Pavilion",
   GoldenPavilionDesc: "All buildings within 3 tile range get +1 Production Multiplier for each adjacent building that produces any of its consumed resources (excluding Clone Lab and Clone Factory and the building cannot be turned off)",
   GoldMiningCamp: "Goudmijnkamp",
   GordonMoore: "Gordon Moore",
   GrandBazaar: "Grand Bazaar",
   GrandBazaarDesc: "Control all markets in one place!. All adjacent buildings get +5 Storage Multiplier",
   GrandBazaarFilters: "Filters",
   GrandBazaarFilterWarningHTML: "You must select a filter before any market trades are shown",
   GrandBazaarFilterYouGet: "You Get",
   GrandBazaarFilterYouPay: "You Pay",
   GrandBazaarSeach: "Search",
   GrandBazaarSearchGet: "Get",
   GrandBazaarSearchPay: "Pay",
   GrandBazaarTabActive: "Active",
   GrandBazaarTabTrades: "Trades",
   GrandCanyon: "Grand Canyon",
   GrandCanyonDesc: "Buildings unlocked in the current age get +2 Production Multiplier. Double the effect of J.P. Morgan",
   GraphicsDriver: "Grafische Driver: %{driver}",
   GreatDagonPagoda: "Grote Dagon Pagode",
   GreatDagonPagodaDescV2: "All pagodas are exempt from -1 happiness. Generate science based on faith production of all pagodas",
   GreatMosqueOfSamarra: "Grote Moskee van Samarra",
   GreatMosqueOfSamarraDescV2: "+1 building vision range. Reveal 5 random unexplored deposit tiles and build a level 10 resource extraction building on each",
   GreatPeople: "Grote Personen",
   GreatPeopleEffect: "Effect",
   GreatPeopleFilter: "Type name or age to filter great people",
   GreatPeopleName: "Naam",
   GreatPeoplePermanentColumn: "Permanent",
   GreatPeoplePermanentShort: "Permanent",
   GreatPeoplePickPerRoll: "Great People Pick Per Roll",
   GreatPeopleThisRun: "Grote Personen Van Deze Run",
   GreatPeopleThisRunColumn: "Deze Run",
   GreatPeopleThisRunShort: "Deze Run",
   GreatPersonLevelRequired: "Permanent Great People Level Required",
   GreatPersonLevelRequiredDescV2: "%{city} civilization requires %{required} permanent great people levels. You currently have %{current}",
   GreatPersonPromotionPromote: "Promote",
   GreatPersonThisRunEffectiveLevel: "You currently have %{count} %{person} from this run. An additional %{person} will have 1/%{effect} of the effect",
   GreatPersonWildCardBirth: "Birth",
   GreatSphinx: "Great Sphinx",
   GreatSphinxDesc: "All Tier II or above buildings within 2 tiles get +N Consumption, Production Multiplier. N = Number of its adjacent buildings of the same type",
   GreatWall: "Great Wall",
   GreatWallDesc: "All buildings within 1 tile range get +N Production, Worker Capacity and Storage Multiplier. N = the number of the different ages between the current age and the age where the building is first unlocked. When constructed next to Forbidden City, the range increases to 2 tile",
   GreedyTransport: "Construction/Upgrade Greedy Transport",
   GreedyTransportDescHTML: "This will make buildings keep transporting resources even if it has enough resources for the current upgrade, which can make upgrading multiple levels <b>faster</b> but end up transport <b>more resources than needed</b>",
   Greek: "Greek",
   GrottaAzzurra: "Grotta Azzurra",
   GrottaAzzurraDescV2: "When discovered, all your Tier I buildings get +5 Level and +1 Production, Worker Capacity and Storage Multiplier",
   Gunpowder: "Buskruit",
   GunpowderMill: "Buskruit Fabriek",
   GuyFawkesNightV2: "Guy Fawkes Night: East India Company provides double the Production Multiplier to buildings adjacent to caravansaries. Tower Bridge generates great people 20% faster",
   HagiaSophia: "Hagia Sophia",
   HagiaSophiaDescV2: "+5 Happiness. Buildings with 0% Production Capacity are exempt from -1 happiness. During the game bootstrap, provide extra happiness to avoid production halt",
   HallOfFame: "Hall of Fame",
   HallOfSupremeHarmony: "Hall of Supreme Harmony",
   Hammurabi: "Hammurabi",
   HangingGarden: "Hangende Tuinen",
   HangingGardenDesc: "+1 Bouwcapaciteit Vermenigvuldiger. Aangrenzende aquaducten krijgen +1 Productie, Opslag en Werknemer Capaciteit Vermenigvuldiger",
   Happiness: "Geluk",
   HappinessFromBuilding: "Van Gebouwen (excl. Wereldwonderen)",
   HappinessFromBuildingTypes: "Van Goed Bevoorraadde Gebouwtypen",
   HappinessFromHighestTierBuilding: "Van Hoogste Niveau Werkend Gebouw",
   HappinessFromUnlockedAge: "Van Ontgrendeld Tijdperk",
   HappinessFromUnlockedTech: "Van Ontgrendelde Technologie",
   HappinessFromWonders: "Van Wereldwonderen (incl. Natuurlijk)",
   HappinessUncapped: "Happiness (Uncapped)",
   HarryMarkowitz: "Harry Markowitz",
   HarunAlRashid: "Haroen al-Rashid",
   Hatshepsut: "Hatshepsut",
   HatshepsutTemple: "Tempel van Hatsjepsoet",
   HatshepsutTempleDesc: "Onthult alle watertegels op de kaart bij voltooiing. Tarweboerderijen krijgen +1 Productie Vermenigvuldiger voor elke aangrenzende watertegel",
   Headquarter: "Hoofdkantoor",
   HedgeFund: "Hedge Fund",
   HelpMenu: "Help",
   HenryFord: "Henry Ford",
   Herding: "Veehoeden",
   Herodotus: "Herodotus",
   HighlightBuilding: "Highlight %{building}",
   HimejiCastle: "Kasteel Himeji",
   HimejiCastleDesc: "Alle Karveelbouwers, Galjoenbouwers en Fregatbouwers krijgen +1 Productie Vermenigvuldiger, Werknemer Capaciteit Vermenigvuldiger en Opslag Vermenigvuldiger",
   Hollywood: "Hollywood",
   HollywoodDesc: "+5 Happiness. +1 Happiness for each well-stocked building that consumes or produces culture within 2 tile range",
   HolyEmpire: "Heilig Rijk",
   Homer: "Homer",
   Honor4UpgradeHTML: "Double the effect of <b>Zheng He</b> (Great Person)",
   HonorLevelX: "Honor %{level}",
   Horse: "Paard",
   HorsebackRiding: "Paardrijden",
   House: "Huis",
   Housing: "Huisvesting",
   Hut: "Hut",
   HydroDam: "Hydro Dam",
   Hydroelectricity: "Hydroelectricity",
   HymanGRickover: "Hyman G. Rickover",
   IdeologyDescHTML: "Choose from <b>Liberalism, Conservatism, Socialism or Communism</b> as your empire ideology. You <b>cannot switch ideology</b> after it is chosen. You can unlock more boost within each ideology",
   IMPei: "I. M. Pei",
   IMPeiDesc: "+%{value} Builder Capacity Multiplier",
   Imperialism: "Imperialisme",
   ImperialPalace: "Imperial Palace",
   IndustrialAge: "Industriële Tijdperk",
   InformationAge: "Informatietijdperk",
   InputResourceForCloning: "Input Resource For Cloning",
   InternationalSpaceStation: "International Space Station",
   InternationalSpaceStationDesc: "All buildings get +5 Storage Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Storage Multiplier to all buildings",
   Internet: "Internet",
   InternetServiceProvider: "Internet Service Provider",
   InverseSelection: "Omgekeerde Selectie",
   Iron: "IJzer",
   IronAge: "IJzertijd",
   Ironclad: "Gepantserd Schip",
   IroncladBuilder: "Gepantserde Schipbouwer",
   IronForge: "IJzersmederij",
   IronMiningCamp: "IJzermijnkamp",
   IronTech: "Ijzer",
   IsaacNewton: "Isaac Newton",
   IsaacNewtonDescV2: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   IsambardKingdomBrunel: "Isambard Kingdom Brunel",
   IsidoreOfMiletus: "Isidore of Miletus",
   IsidoreOfMiletusDesc: "+%{value} Builder Capacity Multiplier",
   Islam5UpgradeHTML: "When unlocked, generate one-time science equivalent to the cost of the most expensive <b>Industrial</b> technology",
   IslamLevelX: "Islam %{level}",
   ItsukushimaShrine: "Itsukushima-schrijn",
   ItsukushimaShrineDescV2: "When all technologies within an age are unlocked, generate one-time science equivalent to the cost of the cheapest technology in the next age",
   JamesWatson: "James Watson",
   JamesWatsonDesc: "+%{value} Science From Busy Workers",
   JamesWatt: "James Watt",
   Japanese: "Japanese",
   JetPropulsion: "Jet Propulsion",
   JohannesGutenberg: "Johannes Gutenberg",
   JohannesKepler: "Johannes Kepler",
   JohnCarmack: "John Carmack",
   JohnDRockefeller: "John D. Rockefeller",
   JohnMcCarthy: "John McCarthy",
   JohnVonNeumann: "John von Neumann",
   JohnVonNeumannDesc: "+%{value} Science From Busy Workers",
   JoinDiscord: "Word lid van Discord",
   JosephPulitzer: "Joseph Pulitzer",
   Journalism: "Journalistiek",
   JPMorgan: "J.P. Morgan",
   JRobertOppenheimer: "J. Robert Oppenheimer",
   JuliusCaesar: "Julius Caesar",
   Justinian: "Justinianus",
   Kanagawa: "Kanagawa",
   KanagawaDesc: "All great people of the current age get an additional level for this run (excluding Zenobia)",
   KarlMarx: "Karl Marx",
   Knight: "Ridder",
   KnightCamp: "Ridder Kamp",
   Koti: "Koti",
   KotiInStorage: "Koti In Storage",
   KotiProduction: "Koti Production",
   LandTrade: "Landhandel",
   Language: "Taal",
   Lapland: "Lapland",
   LaplandDesc: "When discovered, reveal the whole map. All buildings within 2-tile range get +5 Production Multiplier. This natural wonder can only be discovered in December",
   LargeHadronCollider: "Large Hadron Collider",
   LargeHadronColliderDescV2: "All Information Age great people get +2 level for this run. This wonder can be upgraded and each additional upgrade provides +1 level to all Information Age great people for this run",
   Law: "Wet",
   Lens: "Lens",
   LensWorkshop: "Lenzen Workshop",
   LeonardoDaVinci: "Leonardo da Vinci",
   Level: "Niveau",
   LevelX: "Niveau %{level}",
   Liberalism: "Liberalism",
   LiberalismLevel3DescHTML: "Free transport <b>from</b> and <b>to</b> warehouses",
   LiberalismLevel5DescHTML: "<b>Double</b> the electrification effect",
   LiberalismLevelX: "Liberalism Level %{level}",
   Library: "Bibliotheek",
   LighthouseOfAlexandria: "Vuurtoren van Alexandrië",
   LighthouseOfAlexandriaDesc: "Alle aangrenzende gebouwen krijgen +5 Opslag Vermenigvuldiger",
   LinusPauling: "Linus Pauling",
   LinusPaulingDesc: "+%{value} Science From Idle Workers",
   Literature: "Literatuur",
   LiveData: "Live Value",
   LocomotiveFactory: "Locomotieffabriek",
   Logging: "Houtkappen",
   LoggingCamp: "Houtkapkamp",
   LouisSullivan: "Louis Sullivan",
   LouisSullivanDesc: "+%{value} Builder Capacity Multiplier",
   Louvre: "Louvre",
   LouvreDesc: "For every 10 Extra Great People at Rebirth, one great person from all unlocked ages is born",
   Lumber: "Planken",
   LumberMill: "Zagerij",
   LunarNewYear: "Lunar New Year: Great Wall provides double the boost to buildings. Porcelain Tower provides +1 level to all great people from this run",
   LuxorTemple: "Tempel van Luxor",
   LuxorTempleDescV2: "+1 Science From Busy Workers. Choose an empire religion, unlock more boost with each choice",
   Machinery: "Machines",
   Magazine: "Tijdschrift",
   MagazinePublisher: "Tijdschriftuitgever",
   Maglev: "Maglev",
   MaglevFactory: "Maglev Factory",
   MahatmaGandhi: "Mahatma Gandhi",
   ManageAgeWisdom: "Manage Age Wisdom",
   ManagedImport: "Managed Import",
   ManagedImportDescV2: "This building will automatically import resources produced within %{range} tile range. Resource transports for this building cannot be manually changed. Max transport distance will be ignored",
   ManageGreatPeople: "Beheer Grote Personen",
   ManagePermanentGreatPeople: "Beheer Permanente Grote Personen",
   ManageSave: "Manage Save",
   ManageWonders: "Beheer Wereldwonderen",
   Manhattan: "Manhattan",
   ManhattanProject: "Manhattan Project",
   ManhattanProjectDesc: "Manhattan Project",
   Marble: "Marmer",
   Marbleworks: "Marmerwerkplaats",
   MarcoPolo: "Marco Polo",
   MarieCurie: "Marie Curie",
   MarinaBaySands: "Marina Bay Sands",
   MarinaBaySandsDesc: "All buildings get +5 Worker Capacity Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Worker Capacity Multiplier to all buildings",
   Market: "Markt",
   MarketDesc: "Wissel een grondstof uit met een andere, beschikbare grondstoffen worden elk uur bijgewerkt",
   MarketRefreshMessage: "Trades in %{count} markets has been refreshed",
   MarketSell: "Verkoop",
   MarketSettings: "Marktinstellingen",
   MarketValueDesc: "%{value} vergeleken met de gemiddelde prijs",
   MarketYouGet: "Je Krijgt",
   MarketYouPay: "Je Betaalt",
   MartinLuther: "Maarten Luther",
   MaryamMirzakhani: "Maryam Mirzakhani",
   MaryamMirzakhaniDesc: "+%{value} Science From Idle Workers",
   Masonry: "Metselwerken",
   MatrioshkaBrain: "Matrioshka Brain",
   MatrioshkaBrainDescV2: "Allow Science to be counted when calculating empire value (5 Science = 1 Empire Value). +5 Science Per Busy and Idle Worker. This wonder can be upgraded and each additional upgrade provides +1 Science Per Busy and Idle Worker and +1 Production Multiplier for buildings that produce Science",
   MausoleumAtHalicarnassus: "Mausoleum bij Halicarnassus",
   MausoleumAtHalicarnassusDescV2: "Transports from or to buildings within 2 tile range do not cost workers",
   MaxExplorers: "Max Explorers",
   MaxTransportDistance: "Max Transport Distance",
   Meat: "Vlees",
   Metallurgy: "Metaalkunde",
   Michelangelo: "Michelangelo",
   MiddleAge: "Middeleeuwen",
   MilitaryTactics: "Military Tactics",
   Milk: "Melk",
   Moai: "Moai",
   MoaiDesc: "Moai",
   MobileOverride: "Mobile Override",
   MogaoCaves: "Mogao Grotten",
   MogaoCavesDescV3: "+1 happiness for every 10% of busy workers. All adjacent buildings that produce faith are exempt from -1 happiness",
   MonetarySystem: "Monetary System",
   MontSaintMichel: "Mont Saint-Michel",
   MontSaintMichelDesc: "Generate Culture from Idle Workers. Provide +1 Storage Multiplier to all buildings within 2-tile range. This wonder can be upgraded using the generated Culture and each level provides addtional +1 Storage Multiplier",
   Mosque: "Moskee",
   MotionPicture: "Film",
   MountArarat: "Mount Ararat",
   MountAraratDesc: "All buildings within 2 tile range get +X Production, Worker Capacity and Storage Multiplier. X = cubic root of Effective Great People Level",
   MountFuji: "Mount Fuji",
   MountFujiDescV2: "When Petra is built next to it, Petra gets +8h Warp storage. When the game is running, generate 20 warp every minute in Petra (not accelerated by Petra itself, not generating when the game is offline)",
   MountSinai: "Mount Sinai",
   MountSinaiDesc: "When discovered, a great person of the current age is born. All buildings that produce faith get +5 Storage Multiplier",
   MountTai: "Mount Tai",
   MountTaiDesc: "All buildings that produce science get +1 Production Multiplier. Double the effect of Confucious (Great Person). When discovered, generate one-time science equivalent to the cost of the most expensive unlocked technology",
   MoveBuilding: "Move Building",
   MoveBuildingFail: "Selected tile is not valid",
   MoveBuildingNoTeleport: "You don't have enough teleport",
   MoveBuildingSelectTile: "Select An Tile...",
   MoveBuildingSelectTileToastHTML: "Select <b>an empty explored tile</b> on the map as the target",
   Movie: "Movie",
   MovieStudio: "Movie Studio",
   Museum: "Museum",
   Music: "Muziek",
   MusiciansGuild: "Muzikantengilde",
   MutualAssuredDestruction: "Mutual Assured Destruction",
   MutualFund: "Mutual Fund",
   Name: "Naam",
   Nanotechnology: "Nanotechnologie",
   NapoleonBonaparte: "Napoleon Bonaparte",
   NaturalGas: "Aardgas",
   NaturalGasWell: "Aardgas Put",
   NaturalWonderName: "Natuurwonder: %{name}",
   NaturalWonders: "Natuurwonderen",
   Navigation: "Navigatie",
   NebuchadnezzarII: "Nebukadnezar II",
   Neuschwanstein: "Neuschwanstein",
   NeuschwansteinDesc: "+10 Bouwcapaciteit Vermenigvuldiger bij het bouwen van wonderen",
   Newspaper: "Krant",
   NextExplorersIn: "Next Explorers In",
   NextMarketUpdateIn: "Volgende Marktupdate In",
   NiagaraFalls: "Niagara Falls",
   NiagaraFallsDescV2: "All warehouses, markets and caravansaries get +N storage multiplier. N = number of unlocked ages. Albert Einstein provides +1 Production Multiplier to Research Fund (not affected by other boosts like Broadway)",
   NielsBohr: "Niels Bohr",
   NielsBohrDescV2: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   NileRiver: "Nile River",
   NileRiverDesc: "Double the effect of Hatshepsut. All wheat farms get +1 Production and Storage Multiplier. All adjacent wheat farms get +5 Production and Storage Multiplier",
   NoPowerRequired: "This building does not require power",
   NothingHere: "Niets hier",
   NotProducingBuildings: "Gebouwen Die Niet Produceren",
   NuclearFission: "Kernsplitsing",
   NuclearFuelRod: "Nuclear Fuel Rod",
   NuclearMissile: "Nuclear Missile",
   NuclearMissileSilo: "Nuclear Missile Silo",
   NuclearPowerPlant: "Nuclear Power Plant",
   NuclearReactor: "Nuclear Reactor",
   NuclearSubmarine: "Nuclear Submarine",
   NuclearSubmarineYard: "Nuclear Submarine Yard",
   OdaNobunaga: "Oda Nobunaga",
   OfflineErrorMessage: "Je bent momenteel offline, deze bewerking vereist een internetverbinding",
   OfflineProduction: "Offline Productie",
   OfflineProductionTime: "Offline Production Time",
   OfflineProductionTimeDescHTML: "For the <b>first %{time} offline time</b>, you can choose either offline production or time warp - you can set the split here. The <b>rest of the offline time</b> can only be converted to time warp",
   OfflineTime: "Offline Tijd",
   Oil: "Olie",
   OilPress: "Olijfoliepers",
   OilRefinery: "Olie Raffinaderij",
   OilWell: "Oliebron",
   Ok: "Oké",
   Oktoberfest: "Oktoberfest: Double the effect of Zugspitze",
   Olive: "Olijf",
   OlivePlantation: "Olijfplantage",
   Olympics: "Olympische Spelen",
   OnlyAvailableWhenPlaying: "Only available when playing %{city}",
   OpenLogFolder: "Open Log Folder",
   OpenSaveBackupFolder: "Open Backup Folder",
   OpenSaveFolder: "Open Save Folder",
   Opera: "Opera",
   OperationNotAllowedError: "Deze operatie is niet toegestaan",
   Opet: "Opet: Great Sphinx no longer increases Consumption Multiplier",
   OpticalFiber: "OpticalFiber",
   OpticalFiberPlant: "Optical Fiber Factory",
   Optics: "Optiek",
   OptionsMenu: "Opties",
   OptionsUseModernUIV2: "Use Anti-Aliased Font",
   OsakaCastle: "Osaka Castle",
   OsakaCastleDesc: "Provide power to all tiles within 2 tile range. Allow electrification of science producing buildings (including Clone Lab)",
   OtherPlatform: "Other Platform",
   Ottoman: "Ottoman",
   OttoVonBismarck: "Otto von Bismarck",
   OxfordUniversity: "Universiteit van Oxford",
   OxfordUniversityDescV3: "+10% science output for buildings that produce science. When completed, generate one-time science equivalent to the cost of the most expensive unlocked technology",
   PabloPicasso: "Pablo Picasso",
   Pagoda: "Pagoda",
   PaintersGuild: "Schildersgilde",
   Painting: "Schilderij",
   PalmJumeirah: "Palm Jumeirah",
   PalmJumeirahDesc: "+10 Builder Capacity. This wonder can be upgraded and each additional upgrade provides +2 Builder Capacity",
   Pamukkale: "Pamukkale",
   PamukkaleDesc: "When discovered, convert each one of the Permanent Great People Shards (except for Promotion and Wildcard) to the same Great Person From This Run",
   Panathenaea: "Panathenaea: Poseidon provides +1 Production Multiplier to all buildings",
   Pantheon: "Pantheon",
   PantheonDescV2: "All buildings within 2 tile range get +1 Worker Capaicity and Storage Multiplier. Generate science based on faith production of all shrines",
   Paper: "Papier",
   PaperMaker: "Papiermaker",
   Parliament: "Parlement",
   Parthenon: "Parthenon",
   ParthenonDescV2: "Two great people of Classical Age are born and you get 4 choices for each. Musician's Guilds and Painter's Guilds get +1 Production, Worker Capacity and Storage Multiplier and are exempt from -1 Happiness",
   Passcode: "Passcode",
   PasscodeToastHTML: "<b>%{code}</b> is your passcode and it's valid for 30 minutes",
   PatchNotes: "Patchnotities",
   Peace: "Peace",
   Peacekeeper: "Peacekeeper",
   Penthouse: "Penthouse",
   PercentageOfProductionWorkers: "Percentage of Production Workers",
   Performance: "Performance",
   PermanentGreatPeople: "Permanente Grote Personen",
   PermanentGreatPeopleAcquired: "Permanent Great People Acquired",
   PermanentGreatPeopleUpgradeUndo: "Undo permanent great people upgrade: this will convert upgraded level back to shards - you will get %{amount} shards",
   Persepolis: "Persepolis",
   PersepolisDesc: "Alle Kopermijnkampen, Houtkapkampen en Steenhouwerijen krijgen +1 Productie Vermenigvuldiger, Werknemer Capaciteit Vermenigvuldiger en Opslag Vermenigvuldiger",
   PeterHiggs: "Peter Higgs",
   PeterHiggsDesc: "+%{value} Science From Busy Workers",
   Petra: "Petra",
   PetraDesc: "Genereer tijd warp wanneer je offline bent, die je kunt gebruiken om je rijk te versnellen",
   PetraOfflineTimeReconciliation: "You have been credited %{count} warp after server offline time reconciliation",
   Petrol: "Petroleum",
   PhiloFarnsworth: "Philo Farnsworth",
   Philosophy: "Filosofie",
   Physics: "Natuurkunde",
   PierreDeCoubertin: "Pierre de Coubertin",
   Pizza: "Pizza",
   Pizzeria: "Pizzeria",
   PlanetaryRover: "Planetary Rover",
   Plastics: "Plastics",
   PlasticsFactory: "Plastics Factory",
   PlatformAndroid: "Android",
   PlatformiOS: "iOS",
   PlatformSteam: "Steam",
   PlatformSyncInstructionHTML: "If you want to sync your progress on this device to a new device, click <b>Sync To A New Device</b> and get a one-time passcode. On your new device, click <b>Connect To A Device</b> and type in the one-time passcode",
   Plato: "Plato",
   PlayerHandle: "Spelersnaam",
   PlayerHandleOffline: "Je bent momenteel offline",
   PlayerMapClaimThisTile: "Claim Deze Tegel",
   PlayerMapClaimTileCondition2: "Je bent niet verbannen door anti-cheat",
   PlayerMapClaimTileCondition3: "Je hebt de vereiste technologie ontgrendeld: %{tech}",
   PlayerMapClaimTileCondition4: "Je hebt nog geen tegel geclaimd of de afkoelperiode voor het verplaatsen van je tegel is voorbij",
   PlayerMapClaimTileCooldownLeft: "Afkoeltijd over: %{time}",
   PlayerMapClaimTileNoLongerReserved: "Deze tegel is niet langer gereserveerd. Je kunt <b>%{name}</b> verdrijven en deze tegel voor jezelf claimen",
   PlayerMapEstablishedSince: "Opgericht Sinds",
   PlayerMapLastSeenAt: "Laatst Gezien",
   PlayerMapMapTileBonus: "Trade Tile Bonus",
   PlayerMapMenu: "Handel",
   PlayerMapOccupyThisTile: "Occupy This Tile",
   PlayerMapOccupyTileCondition1: "This tile is adjacent to your home or occupied tiles",
   PlayerMapPageGoBackToCity: "Ga Terug Naar Stad",
   PlayerMapSetYourTariff: "Stel Je Tarief In",
   PlayerMapTariff: "Tarief",
   PlayerMapTariffApply: "Tarieftarief Toepassen",
   PlayerMapTariffDesc: "Elke handel die via jouw tegel gaat, betaalt een tarief aan jou. Het is een balans: als je het tarief verhoogt, zal je meer verdienen aan elke handel, maar zullen minder handelingen via jouw tegel gaan.",
   PlayerMapTileAvailableTilePoint: "Available Tile Point",
   PlayerMapTileFromOccupying: "From Owned/Occupied Tiles",
   PlayerMapTileFromOccupyingTooltipHTML: "An owned/occupied tile generates <b>%{point}</b> tile point per hour (up to %{max} days from the first claimed tile)",
   PlayerMapTileFromRank: "From Account Rank",
   PlayerMapTileTilePoint: "Tile Point",
   PlayerMapTileUsedTilePoint: "Used Tile Point",
   PlayerMapTileUsedTilePointTooltipHTML: "You need <b>1 tile point</b> to own/occupy a tile",
   PlayerMapTradesFrom: "Handel van %{name}",
   PlayerMapUnclaimedTile: "Ongeclaimde Tegel",
   PlayerMapYourTile: "Jouw Tegel",
   PlayerTrade: "Handel Met Spelers",
   PlayerTradeAddSuccess: "De handel is succesvol toegevoegd",
   PlayerTradeAddTradeCancel: "Annuleer",
   PlayerTradeAmount: "Aantal",
   PlayerTradeCancelDescHTML: "You will get <b>%{res}</b> back after cancelling this trade: <b>%{percent}</b> charged for refund and <b>%{discard}</b> discarded due to storage overflow<br><b>Are you sure you want to cancel?</b>",
   PlayerTradeCancelTrade: "Annuleer Handel",
   PlayerTradeClaim: "Claim",
   PlayerTradeClaimAll: "Alles Claimen",
   PlayerTradeClaimAllFailedMessageV2: "Failed to claim any trades - is the storage full?",
   PlayerTradeClaimAllMessageV2: "You have claimed: <b>%{resources}</b>",
   PlayerTradeClaimAvailable: "%{count} handel(s) zijn gevuld beschikbaar om te claimen",
   PlayerTradeClaimTileFirst: "Claim Je Tegel Op Handelskaart",
   PlayerTradeClaimTileFirstWarning: "Je kunt alleen handelen met andere spelers nadat je je tegel op de handelskaart hebt geclaimd",
   PlayerTradeClearAll: "Clear All Fills",
   PlayerTradeClearFilter: "Clear Filters",
   PlayerTradeDisabledBeta: "You can ony create player trades once the beta version is released",
   PlayerTradeFill: "Vul",
   PlayerTradeFill50: "Fill 50%",
   PlayerTradeFill95: "Fill 95%",
   PlayerTradeFillAmount: "Vul Hoeveelheid",
   PlayerTradeFillAmountMaxV2: "Fill Max",
   PlayerTradeFillBy: "Vul Door",
   PlayerTradeFillPercentage: "Fill Percentage",
   PlayerTradeFillSuccessV2: "<b>%{success}/%{total}</b> trades have been filled. You paid <b>%{fillAmount} %{fillResource}</b> and received <b>%{receivedAmount} %{receivedResource}</b>",
   PlayerTradeFillTradeButton: "Vul Handel",
   PlayerTradeFillTradeTitle: "Vul Handel",
   PlayerTradeFilters: "Filters",
   PlayerTradeFiltersApply: "Apply",
   PlayerTradeFiltersClear: "Clear",
   PlayerTradeFilterWhatIHave: "Filter By What I Have",
   PlayerTradeFrom: "Van",
   PlayerTradeIOffer: "Ik Bied",
   PlayerTradeIWant: "Ik Wil",
   PlayerTradeMaxAll: "Max All Fills",
   PlayerTradeMaxTradeAmountFilter: "Max Amount",
   PlayerTradeMaxTradeExceeded: "Je hebt het maximale aantal actieve handelingen voor je account rang overschreden",
   PlayerTradeNewTrade: "Nieuwe Handel",
   PlayerTradeNoFillBecauseOfResources: "No trade has been filled due to insufficient resources",
   PlayerTradeNoValidRoute: "Kan geen geldige handelsroute vinden tussen jou en %{name}",
   PlayerTradeOffer: "Bied",
   PlayerTradePlaceTrade: "Plaats Handel",
   PlayerTradePlayerNameFilter: "Player Name",
   PlayerTradeResource: "Bron",
   PlayerTradeStorageRequired: "Opslag Vereist",
   PlayerTradeTabImport: "Import",
   PlayerTradeTabPendingTrades: "Pending Trades",
   PlayerTradeTabTrades: "Trades",
   PlayerTradeTariffTooltip: "Verzameld Van een Handelstarief",
   PlayerTradeWant: "Wil",
   PlayerTradeYouGetGross: "Je Krijgt (Voor Tarief): %{res}",
   PlayerTradeYouGetNet: "Je Krijgt (Na Tarief): %{res}",
   PlayerTradeYouPay: "Je Betaalt: %{res}",
   Poem: "Gedicht",
   PoetrySchool: "Poetry School",
   Politics: "Politiek",
   PolytheismLevelX: "Polytheism %{level}",
   PorcelainTower: "Porcelain Tower",
   PorcelainTowerDesc: "+5 Happiness. When constructed, all your extra great people at rebirth will become available for this run (they are rolled following the same rule as permanent great people)",
   PorcelainTowerMaxPickPerRoll: "Prefer Max Pick Per Roll",
   PorcelainTowerMaxPickPerRollDescHTML: "When choosing great people after Porcelain Tower completed, prefer max pick per roll for the available amount",
   Poseidon: "Poseidon",
   PoseidonDescV2: "All adjacent buildings get free upgrades to Level 25 and +N Production, Worker Capacity and Storage Multiplier. N = Tier of the building",
   PoultryFarm: "Poultry Farm",
   Power: "Stroom",
   PowerAvailable: "Beschikbare Stroom",
   PowerUsed: "Gebruikte Stroom",
   PreciousMetal: "Precieus metaal",
   Printing: "Printen",
   PrintingHouse: "Drukkerij",
   PrintingPress: "Druk Pers",
   PrivateOwnership: "Privaatbezit",
   Produce: "Produceer",
   ProduceResource: "Productie: %{resource}",
   ProductionMultiplier: "Productievermenigvuldiger",
   ProductionPriority: "Productieprioriteit",
   ProductionPriorityDescV4: "Priority determins the order that buildings transport and produce - a bigger number means a building transports and produces before other buildings",
   ProductionWorkers: "Production Workers",
   Progress: "Voortgang",
   ProgressTowardsNextGreatPerson: "Vooruitgang Naar Volgende Grote Persoon bij Herboren",
   ProgressTowardsTheNextGreatPerson: "Progress Towards the Next Great Person",
   PromotionGreatPersonDescV2: "When consumed, promote any permanent great people of the same age to the next age",
   ProphetsMosque: "Prophet's Mosque",
   ProphetsMosqueDesc: "Double the effect of Harun al-Rashid. Generate science based on faith production of all mosques",
   Province: "Provincie",
   ProvinceAegyptus: "Egypte",
   ProvinceAfrica: "Afrika",
   ProvinceAsia: "Azië",
   ProvinceBithynia: "Bithynië",
   ProvinceCantabri: "Cantabri",
   ProvinceCappadocia: "Cappadocië",
   ProvinceCilicia: "Cilicië",
   ProvinceCommagene: "Commagene",
   ProvinceCreta: "Kreta",
   ProvinceCyprus: "Cyprus",
   ProvinceCyrene: "Cyrene",
   ProvinceGalatia: "Galatië",
   ProvinceGallia: "Gallia",
   ProvinceGalliaCisalpina: "Gallia Cisalpina",
   ProvinceGalliaTransalpina: "Gallia Transalpina",
   ProvinceHispania: "Hispania",
   ProvinceIllyricum: "Illyricum",
   ProvinceItalia: "Italia",
   ProvinceJudia: "Judea",
   ProvinceLycia: "Lycië",
   ProvinceMacedonia: "Macedonië",
   ProvinceMauretania: "Mauretanië",
   ProvinceNumidia: "Numidië",
   ProvincePontus: "Pontus",
   ProvinceSardiniaAndCorsica: "Sardinië en Corsica",
   ProvinceSicillia: "Sicilië",
   ProvinceSophene: "Sophene",
   ProvinceSyria: "Syrië",
   PublishingHouse: "Uitgeverij",
   PyramidOfGiza: "Piramide van Gizeh",
   PyramidOfGizaDesc: "Alle gebouwen die werknemers produceren krijgen +1 Productie Vermenigvuldiger",
   QinShiHuang: "Qin Shi Huang",
   Radio: "Radio",
   RadioStation: "Radio Station",
   Railway: "Spoorweg",
   RamessesII: "Ramses II",
   RamessesIIDesc: "+%{value} Bouwcapaciteit Vermenigvuldiger",
   RandomColorScheme: "Random Color Scheme",
   RapidFire: "Snelvuur",
   ReadFullPatchNotes: "Read Patch Notes",
   RebirthHistory: "Rebirth History",
   RebirthTime: "Rebirth Time",
   Reborn: "Herboren",
   RebornModalDescV3: "You will start a new empire but all your great people <b>from this run</b> becomes permanent shards, which can be used to upgrade your <b>permanent great people level</b>. You will also get extra great people shards based on your <b>total empire value</b>",
   RebornOfflineWarning: "Je bent momenteel offline. Je kunt alleen herboren als je verbonden bent met de server",
   RebornTradeWarning: "Je hebt actieve handel of handel die geclaimd kan worden. <b>Herboren zal deze verwijderen</b> - overweeg om eerst te annuleren of te claimen",
   RedistributeAmongSelected: "Herdistibueren Onder Geselecteerden",
   RedistributeAmongSelectedCap: "Cap",
   RedistributeAmongSelectedImport: "Importeren",
   Refinery: "Raffinaderij",
   Reichstag: "Reichstag",
   Religion: "Religie",
   ReligionBuddhism: "Buddhism",
   ReligionChristianity: "Christianity",
   ReligionDescHTML: "Choose from <b>Christianity, Islam, Buddhism or Polytheism</b> as your empire religion. You <b>cannot switch religion</b> after it is chosen. You can unlock more boost within each religion",
   ReligionIslam: "Islam",
   ReligionPolytheism: "Polytheism",
   Renaissance: "Renaissance",
   RenaissanceAge: "Renaissance",
   ReneDescartes: "René Descartes",
   RequiredDeposit: "Vereiste Bron",
   RequiredWorkersTooltipV2: "Required number of workers for production is equal to the sum of all resources consumed and produced after multipliers (excluding dynamic multipliers)",
   RequirePower: "Require Power",
   RequirePowerDesc: "This building needs to be built on a tile with power and can extend the power to its adjacent tiles",
   Research: "Onderzoek",
   ResearchFund: "Research Fund",
   ResearchLab: "Research Lab",
   ResearchMenu: "Onderzoek",
   ResourceAmount: "Aantal",
   ResourceBar: "Resource Bar",
   ResourceBarExcludeStorageFullHTML: "Exclude buildings that have <b>full storage</b> from Not Producing Buildings",
   ResourceBarExcludeTurnedOffOrNoActiveTransportHTML: "Exclude buildings that are <b>turned off</b> from Not Producing Buildings",
   ResourceBarShowUncappedHappiness: "Show Uncapped Happiness",
   ResourceCloneTooltip: "The production multiplier only applies to the cloned resource (i.e. the extra copy)",
   ResourceColor: "Bronkleur",
   ResourceExportBelowCap: "Export Below Cap",
   ResourceExportBelowCapTooltip: "Allow other buildings to transport a resource from this building even when its amount is below the cap",
   ResourceExportToSameType: "Export to the Same Type",
   ResourceExportToSameTypeTooltip: "Allow other buildings of the same type to transport a resource from this building",
   ResourceFromBuilding: "%{resource} van %{building}",
   ResourceImport: "Brontransport",
   ResourceImportCapacity: "Resource Transport Capacity",
   ResourceImportImportCapV2: "Max Amount",
   ResourceImportImportCapV2Tooltip: "This building will stop transporting thhis resource when the max amount is reached",
   ResourceImportImportPerCycleV2: "Per Cycle",
   ResourceImportImportPerCycleV2ToolTip: "The amount of this resource that is transported per cycle",
   ResourceImportPartialWarningHTML: "The total resource transport capacity has exceeds the maximum capacity: <b>each resource transport will only transport partially per cycle</b>",
   ResourceImportResource: "Bron",
   ResourceImportSettings: "Brontransport: %{res}",
   ResourceImportStorage: "Opslag",
   ResourceNeeded: "Extra %{resource} x%{amount} Needed",
   ResourceTransportPreference: "Transport Preference",
   RevealDeposit: "Onthullen",
   Revolution: "Revolutie",
   RhineGorge: "Rhine Gorge",
   RhineGorgeDesc: "+2 Happiness for each wonder within 2 tile range",
   RichardFeynman: "Richard Feynman",
   RichardFeynmanDesc: "+%{value} Science from All Workers if more than 50% of workers are busy and less than 50% of busy workers work in transportation",
   RichardJordanGatling: "Richard Jordan Gatling",
   Rifle: "Geweer",
   RifleFactory: "Geweerfabriek",
   Rifling: "Groeven",
   Rijksmuseum: "Rijksmuseum",
   RijksmuseumDesc: "+5 Geluk. Alle gebouwen die Cultuur verbruiken of produceren krijgen +1 Productie, Opslag en Werknemer Capaciteit",
   RoadAndWheel: "Weg & Wiel",
   RobertNoyce: "Robert Noyce",
   Robocar: "Robocar",
   RobocarFactory: "Robocar Factory",
   Robotics: "Robotica",
   RockefellerCenterChristmasTree: "Rockefeller Center Christmas Tree",
   RockefellerCenterChristmasTreeDesc: "+3 Happiness for each unlocked age. This natural wonder can only be discovered in December",
   Rocket: "Rocket",
   RocketFactory: "Rocket Factory",
   Rocketry: "Raketkunde",
   Roman: "Roman",
   RomanForum: "Romeins Forum",
   RudolfDiesel: "Rudolf Diesel",
   Rurik: "Rurik",
   RurikDesc: "+%{value} Geluk",
   SagradaFamilia: "Sagrada Família",
   SagradaFamiliaDesc: "Sagrada Família",
   SaintBasilsCathedral: "Kathedraal van Basilius de Gelukzalige",
   SaintBasilsCathedralDescV2: "Allow resource extraction buildings to work adjacent to a deposit. All tier I buildings get +1 Production Multiplier, Worker Capacity Multiplier, and Storage Multiplier",
   Saladin: "Saladin",
   Samsuiluna: "Samsu-iluna",
   Sand: "Zand",
   Sandpit: "Zandgroeve",
   SantaClausVillage: "Santa Claus Village",
   SantaClausVillageDesc: "When completed, a great person of the current age is born. This wonder can be upgraded and each additional upgrade provides an extra great person. When choosing great people from this wonder, 4 choices are provided. This wonder can only be constructed in December",
   SargonOfAkkad: "Sargon van Akkad",
   Satellite: "Satellite",
   SatelliteFactory: "Satellite Factory",
   SatoshiNakamoto: "Satoshi Nakamoto",
   Saturnalia: "Saturnalia: Alps no longer increases Consumption Multiplier",
   SaveAndExit: "Save And Exit",
   School: "School",
   Science: "Wetenschap",
   ScienceFromBusyWorkers: "Wetenschap Van Bezette Werknemers",
   ScienceFromIdleWorkers: "Wetenschap Van Inactieve Werknemers",
   SciencePerBusyWorker: "Per Bezette Werknemer",
   SciencePerIdleWorker: "Per Inactieve Werknemer",
   ScrollSensitivity: "Scroll Sensitivity",
   ScrollSensitivityDescHTML: "Adjust sensitivity when scrolling mousewheel. <b>Must be between 0.01 to 100. Default is 1</b>",
   ScrollWheelAdjustLevelTooltip: "You can use scroll wheel to adjust the level when your cursor is over this",
   SeaTradeCost: "Sea Trade Cost",
   SeaTradeUpgrade: "Trading with players across the sea. Tariff for each sea tile: %{tariff}",
   SelectCivilization: "Select Civilization",
   SelectedAll: "Selecteer Alles",
   SelectedCount: "%{count} Geselecteerd",
   Semiconductor: "Halfgeleider",
   SemiconductorFab: "Semiconductor Fab",
   SendExplorer: "Send Explorer",
   SergeiKorolev: "Sergei Korolev",
   SetAsDefault: "Instellen Als Standaard",
   SetAsDefaultBuilding: "Instellen Als Standaard Voor Alle %{building}",
   Shamanism: "Sjamanisme",
   Shelter: "Schuilplaats",
   Shortcut: "Sneltoets",
   ShortcutBuildingPageSellBuildingV2: "Demolish Building",
   ShortcutBuildingPageToggleBuilding: "Toggle Production",
   ShortcutBuildingPageToggleBuildingSetAllSimilar: "Toggle Production And Apply To All",
   ShortcutBuildingPageUpgrade1: "Upgrade Button 1 (+1)",
   ShortcutBuildingPageUpgrade2: "Upgrade Button 2 (+5)",
   ShortcutBuildingPageUpgrade3: "Upgrade Button 3 (+10)",
   ShortcutBuildingPageUpgrade4: "Upgrade Button 4 (+15)",
   ShortcutBuildingPageUpgrade5: "Upgrade Button 5 (+20)",
   ShortcutClear: "Wissen",
   ShortcutConflict: "Je sneltoets is in conflict met %{name}",
   ShortcutNone: "Geen",
   ShortcutPressShortcut: "Druk op Sneltoets...",
   ShortcutSave: "Opslaan",
   ShortcutScopeBuildingPage: "Gebouwenpagina",
   ShortcutScopeConstructionPage: "Consctruction/Upgrade Page",
   ShortcutScopeEmptyTilePage: "Lege Tegelpagina",
   ShortcutScopePlayerMapPage: "Handelskaartpagina",
   ShortcutScopeTechPage: "Technologiepagina",
   ShortcutScopeUnexploredPage: "Unexplored Page",
   ShortcutTechPageGoBackToCity: "Ga Terug Naar Stad",
   ShortcutTechPageUnlockTech: "Ontgrendel Geselecteerde Technologie",
   ShortcutUpgradePageCancelAllUpgrades: "Cancel All Upgrades",
   ShortcutUpgradePageCancelUpgrade: "Cancel Upgrade",
   ShortcutUpgradePageDecreaseLevel: "Verlaag Upgrade Niveau",
   ShortcutUpgradePageEndConstruction: "End Construction",
   ShortcutUpgradePageIncreaseLevel: "Verhoog Upgrade Niveau",
   ShowTransportArrow: "Show Transport Arrow",
   ShowTransportArrowDescHTML: "Turning this off will hide transport arrows. It might <i>slightly</i> improve performance on low end devices. Performance improvement takes effect <b>after restarting your game</b>",
   ShowUnbuiltOnly: "Only show buildings that haven't been built yet",
   Shrine: "Heiligdom",
   SidePanelWidth: "Side Panel Width",
   SidePanelWidthDescHTML: "Change the width of the side panel. <b>Require restarting your game to take effect</b>",
   SiegeRam: "Stormram",
   SiegeWorkshop: "Belegeringswerkplaats",
   Silicon: "Silicon",
   SiliconSmelter: "Silicon Smelter",
   Skyscraper: "Skyscraper",
   Socialism: "Socialism",
   SocialismLevel4DescHTMLV2: "Generate one-time science equivalent to the cost of the cheapest <b>World Wars Age</b> technology",
   SocialismLevel5DescHTMLV2: "Generate one-time science equivalent to the cost of the cheapest <b>Cold War Age</b> technology",
   SocialismLevelX: "Socialism Level %{level}",
   SocialNetwork: "Sociaal Netwerk",
   Socrates: "Socrates",
   SocratesDesc: "+%{value} Wetenschap Van Bezette Werknemers",
   Software: "Software",
   SoftwareCompany: "Software Company",
   Sound: "Geluid",
   SoundEffect: "Geluidseffect",
   SourceGreatPerson: "Groot Persoon: %{person}",
   SourceGreatPersonPermanent: "Permanente Groot Persoon: %{person}",
   SourceIdeology: "Ideology: %{ideology}",
   SourceReligion: "Religion: %{religion}",
   SourceResearch: "Onderzoek: %{tech}",
   SourceTradition: "Tradition: %{tradition}",
   SpaceCenter: "Space Center",
   Spacecraft: "Spacecraft",
   SpacecraftFactory: "Spacecraft Factory",
   SpaceNeedle: "Space Needle",
   SpaceNeedleDesc: "+1 Happiness for each wonder constructed",
   SpaceProgram: "Ruimteprogramma",
   Sports: "Sport",
   Stable: "Stal",
   Stadium: "Stadion",
   StartFestival: "Let the Festival Begin!",
   Stateship: "Staatschip",
   StatisticsBuildings: "Gebouwen",
   StatisticsBuildingsSearchText: "Type a building name to search",
   StatisticsEmpire: "Empire",
   StatisticsExploration: "Exploration",
   StatisticsOffice: "Statistieken Kantoor",
   StatisticsOfficeDesc: "Provide statistics of your empire. Generate explorers for exploring the map",
   StatisticsResources: "Bronnen",
   StatisticsResourcesDeficit: "Tekort",
   StatisticsResourcesDeficitDesc: "Productie: %{output} - Verbruik: %{input}",
   StatisticsResourcesRunOut: "Uitgeput",
   StatisticsResourcesSearchText: "Type a resource name to search",
   StatisticsScience: "Science",
   StatisticsScienceFromBuildings: "Science From Buildings",
   StatisticsScienceFromWorkers: "Science From Workers",
   StatisticsScienceProduction: "Science Production",
   StatisticsStalledTransportation: "Stalled Transportation",
   StatisticsTotalTransportation: "Total Transportation",
   StatisticsTransportation: "Transport",
   StatisticsTransportationPercentage: "Percentage van Transportwerkers",
   StatueOfLiberty: "Vrijheidsbeeld",
   StatueOfLibertyDesc: "Alle aangrenzende gebouwen krijgen +N Productie, Opslag en Werknemer Capaciteit Vermenigvuldiger. N = Aantal van zijn aangrenzende gebouwen van hetzelfde type",
   StatueOfZeus: "Standbeeld van Zeus",
   StatueOfZeusDesc: "Genereer willekeurige bronnen die op aangrenzende lege tegels zijn onthuld. Alle aangrenzende Gebouwen van Niveau I krijgen +5 Productie en Opslag Vermenigvuldiger",
   SteamAchievement: "Steam Prestatie",
   SteamAchievementDetails: "Bekijk Steam Prestatie",
   SteamEngine: "Stoommachine",
   Steamworks: "Stoomwerkplaats",
   Steel: "Staal",
   SteelMill: "Staalfabriek",
   StephenHawking: "Stephen Hawking",
   Stock: "Aandeel",
   StockExchange: "Aandelenbeurs",
   StockMarket: "Effectenbeurs",
   StockpileDesc: "Dit gebouw zal %{capacity}x invoerbronnen per productiecyclus vervoeren totdat het maximum is bereikt",
   StockpileMax: "Max Voorraad",
   StockpileMaxDesc: "Dit gebouw stopt met het vervoeren van een bron zodra er genoeg zijn voor %{cycle} productiecycli",
   StockpileMaxUnlimited: "Onbeperkt",
   StockpileMaxUnlimitedDesc: "Dit gebouw zal nooit stoppen met het vervoeren van bronnen, alleen totdat de opslag vol is",
   StockpileSettings: "Voorraad Invoercapaciteit",
   Stone: "Steen",
   StoneAge: "Steen Tijdperk",
   Stonehenge: "Stonehenge",
   StonehengeDesc: "Alle gebouwen die steen verbruiken of produceren krijgen +1 Productie Vermenigvuldiger",
   StoneQuarry: "Steenhouwerij",
   StoneTool: "Stenen Gereedschap",
   StoneTools: "Stenen Gereedschap",
   Storage: "Opslag",
   StorageBaseCapacity: "Basis Capaciteit",
   StorageMultiplier: "Opslagvermenigvuldiger",
   StorageUsed: "Opslag Gebruikt",
   StPetersBasilica: "Sint-Pietersbasiliek",
   StPetersBasilicaDescV2: "All churches get +5 Storage Multiplier. Generate science based on faith production of all churches",
   Submarine: "Submarine",
   SubmarineYard: "Submarine Yard",
   SuleimanI: "Suleiman I",
   SummerPalace: "Zomerpaleis",
   SummerPalaceDesc: "Alle aangrenzende gebouwen die buskruit verbruiken of produceren zijn vrijgesteld van -1 Geluk. Alle gebouwen die buskruit verbruiken of produceren krijgen +1 Productie, Opslag en Werknemer Capaciteit",
   Supercomputer: "Supercomputer",
   SupercomputerLab: "Supercomputer Lab",
   SupporterPackRequired: "Supporter Pack Required",
   SupporterThankYou: "CivIdle is kept afloat thanks to the generousity of the following supporter pack owners",
   SwissBank: "Swiss Bank",
   SwissBankDescV2: "Convert a chosen resource from adjacent warehouses to Koti (10 million in Sanskrit) - a tradeable resource that is worth 10M value. Each level of Swiss Bank add 1 Koti conversion per cycle, which is affected by Production Multiplier. Swiss Bank can store unlimited amount of Koti",
   Sword: "Zwaard",
   SwordForge: "Zwaardsmederij",
   SydneyOperaHouse: "Sydney Opera Huis",
   SydneyOperaHouseDescV2: "Sydney Opera House",
   SyncToANewDevice: "Sync To A New Device",
   Synthetics: "Synthetica",
   TajMahal: "Taj Mahal",
   TajMahalDescV2: "A great person of Classical Age and a great person of Middle Age are born. +5 Builder Capacity Multiplier when upgrading buildings over Level 20",
   TangOfShang: "Tang van Shang",
   TangOfShangDesc: "+%{value} Wetenschap Van Inactieve Werknemers",
   Tank: "Tank",
   TankFactory: "Tankfabriek",
   TechAge: "Age",
   TechGlobalMultiplier: "Boost",
   TechHasBeenUnlocked: "%{tech} is ontgrendeld",
   TechProductionPriority: "Ontgrendel gebouwprioriteit - sta toe om productieprioriteit voor elk gebouw in te stellen",
   TechResourceTransportPreference: "Unlock building transport preference - allow setting how a building transports resources needed for its production",
   TechResourceTransportPreferenceAmount: "Amount",
   TechResourceTransportPreferenceAmountTooltip: "This building will prefer transporting resources from buildings that have larger amount in storage",
   TechResourceTransportPreferenceDefault: "Default",
   TechResourceTransportPreferenceDefaultTooltip: "Do not override transport preference for this resource, will use the building's transport preference instead",
   TechResourceTransportPreferenceDistance: "Distance",
   TechResourceTransportPreferenceDistanceTooltip: "This building will prefer transporting resources from buildings that are closer in distance",
   TechResourceTransportPreferenceOverrideTooltip: "This resource has transport preference override: %{mode}",
   TechResourceTransportPreferenceStorage: "Storage",
   TechResourceTransportPreferenceStorageTooltip: "This building will prefer transporting resources from buildings that have higher percentage of used storage",
   TechStockpileMode: "Ontgrendel voorraadmodus - laat aanpassing van de voorraad voor elk gebouw toe",
   Teleport: "Teleport",
   TeleportDescHTML: "A teleport is generated <b>every %{time} seconds</b>. A teleport can be used to <b>move a building (wonders excluded)</b> once",
   Television: "Televisie",
   TempleOfArtemis: "Tempel van Artemis",
   TempleOfArtemisDesc: "Alle Zwaardsmederijen en Arsenaal krijgen +5 Niveau bij voltooiing. Alle Zwaardsmederijen en Arsenaal krijgen +1 Productie Vermenigvuldiger, Werknemer Capaciteit Vermenigvuldiger en Opslag Vermenigvuldiger",
   TempleOfHeaven: "Tempel van de Hemel",
   TempleOfHeavenDesc: "Alle gebouwen die niveau 10 of hoger zijn krijgen +1 Werknemer Capaciteit Vermenigvuldiger",
   TempleOfPtah: "Temple of Ptah",
   TerracottaArmy: "Terracottaleger",
   TerracottaArmyDesc: "Alle IJzermijnkampen krijgen +1 Productie Vermenigvuldiger, Werknemer Capaciteit Vermenigvuldiger en Opslag Vermenigvuldiger. IJzersmederijen krijgen +1 Productie Vermenigvuldiger voor elk aangrenzend IJzermijnkamp",
   Thanksgiving: "Thanksgiving: Wall Street provides double the boost to buildings and applies to Mutual Fund, Hedge Fund and Bitcoin Miner. Research Funds get +5 Production Multiplier",
   Theater: "Theater",
   Theme: "Thema",
   ThemeColor: "Themakleur",
   ThemeColorResearchBackground: "Onderzoeksachtergrond",
   ThemeColorReset: "Reset naar Standaard",
   ThemeColorResetBuildingColors: "Reset Gebouwkleuren",
   ThemeColorResetResourceColors: "Reset Bronkleuren",
   ThemeInactiveBuildingAlpha: "Alpha van Inactieve Gebouwen",
   ThemePremiumTile: "This tile is only available for Supporter Pack owners",
   ThemeResearchHighlightColor: "Onderzoek Highlight Kleur",
   ThemeResearchLockedColor: "Onderzoek Vergrendeld Kleur",
   ThemeResearchUnlockedColor: "Onderzoek Ontgrendeld Kleur",
   ThemeTransportIndicatorAlpha: "Transport Indicator Alpha",
   Theocracy: "Theocratie",
   TheoreticalData: "Theoretical Data",
   ThePentagon: "The Pentagon",
   ThePentagonDesc: "After constructed, generate teleports that can be used to move buildings. All buildings within 2 tile range get +1 Production, Worker Capacity and Storage Multiplier",
   TheWhiteHouse: "The White House",
   ThomasEdison: "Thomas Edison",
   ThomasGresham: "Thomas Gresham",
   Tile: "Tegel",
   TileBonusRefreshIn: "Tile bonus will refresh in <b>%{time}</b>",
   TimBernersLee: "Tim Berners-Lee",
   TimeWarp: "Tijd Warp",
   TimeWarpWarning: "Versnellen op een hogere snelheid dan je computer aankan kan leiden tot dataverlies: GEBRUIK OP EIGEN RISICO",
   ToggleWonderEffect: "Toggle Wonder Effect",
   Tool: "Gereedschap",
   TopkapiPalace: "Topkapı Palace",
   TopkapiPalaceDesc: "All buildings within 2 tile range get +X Storage Multiplier. X = 50% of its Production Multiplier (excluding Dynamic)",
   TotalEmpireValue: "Totale Rijkwaarde",
   TotalEmpireValuePerCycle: "Totale Rijkwaarde Per Cyclus",
   TotalEmpireValuePerCyclePerGreatPeopleLevel: "Total Empire Value Per Cycle Per Great People Level",
   TotalEmpireValuePerWallSecond: "Total Empire Value Wall Second",
   TotalEmpireValuePerWallSecondPerGreatPeopleLevel: "Total Empire Value Per Wall Second Per Great People Level",
   TotalGameTimeThisRun: "Total Game Time This Run",
   TotalScienceRequired: "Total Science Required",
   TotalStorage: "Totale Opslag",
   TotalWallTimeThisRun: "Total Wall Time This Run",
   TotalWallTimeThisRunTooltip: "Wall time (aka. elapsed real time) measures the actual time taken for this run. The differs from the game time in that Time Warp in Petra and Offline Production does not affect wall time but it does affect game time",
   TotalWorkers: "Total Workers",
   TowerBridge: "Tower Bridge",
   TowerBridgeDesc: "After constructed, a great person from unlocked ages is born every 3600 cycles (1h game time)",
   TowerOfBabel: "Tower of Babel",
   TowerOfBabelDesc: "Provides +2 Production Multiplier to all buildings that has at least one working building located adjacent to the wonder",
   TradeFillSound: "'Trade Filled' Sound",
   TradeValue: "Trade Value",
   TraditionCommerce: "Commerce",
   TraditionCultivation: "Cultivation",
   TraditionDescHTML: "Choose from <b>Cultivation, Commerce, Expansion and Honor</b> as your empire tradition. You <b>cannot switch tradition</b> after it is chosen. You can unlock more boost within each tradition",
   TraditionExpansion: "Expansion",
   TraditionHonor: "Honor",
   Train: "Trein",
   TranslationPercentage: "%{language} is %{percentage} vertaald. Help deze vertaling te verbeteren op GitHub",
   TranslatorCredit: "Thomasthegama",
   Translators: "Vertalers",
   TransportAllocatedCapacityTooltip: "Bouwcapaciteit toegewezen aan het transporteren van deze hulpbron",
   TransportationWorkers: "Transportation Workers",
   TransportCapacity: "Transportcapaciteit",
   TransportCapacityMultiplier: "Transportcapaciteit Vermenigvuldiger",
   TransportManualControlTooltip: "Transporteer deze hulpbron voor constructie/upgrade",
   TransportPlanCache: "Transport Plan Cache",
   TransportPlanCacheDescHTML:
      "Every cycle, each building calculates the best transport plan based on its settings - this process requires high CPU power. Enabling this will attempt to cache the result of the transport plan if it is still valid and therefore reduce CPU usage and frame rate drop. <b>Experimental Feature</b>",
   TribuneUpgradeDescGreatPeopleWarning: "Je huidige run heeft grote mensen. Je zou eerst <b>moeten herboren</b>. Upgraden naar Quaestor rang zal je huidige run resetten",
   TribuneUpgradeDescGreatPeopleWarningTitle: "Please Rebirth First",
   TribuneUpgradeDescV4:
      "You can play the full game as Tribune if you do not plan to participate in the <b>optional</b> online features. To acquire unrestricted access to the online features, you will need to upgrade to Quaestor. <b>This is an anti-bot measure to keep the game free for everyone.</b> However, <b>when upgrading to Quaestor</b> you can carry over great people: <ul><li>Up to Level <b>3</b> for Bronze, Iron and Classical Age</li><li>Up to Level <b>2</b> for Middle Age, Renaissance and Industrial Age</li><li>Up to Level <b>1</b> for World Wars, Cold War and Information Age</li></ul>Great People Shards above the level and <b>Age Wisdom</b> levels <b>cannot</b> be carried over",
   TurnOffFullBuildings: "Turn Off All %{building} With Full Storage",
   TurnOnTimeWarpDesc: "Kost %{speed} warps per seconde en versnelt je rijk om te draaien op %{speed}x snelheid.",
   Tutorial: "Tutorial",
   TutorialPlayerFlag: "Kies je spelersvlag",
   TutorialPlayerHandle: "Kies je spelersnaam",
   TV: "TV",
   TVStation: "TV Station",
   UnclaimedGreatPersonPermanent: "Je hebt ongeclaimde <b>Permanente Grote Personen</b>, klik hier om te claimen",
   UnclaimedGreatPersonThisRun: "Je hebt ongeclaimde <b>Grote Personen deze run</b>, klik hier om te claimen",
   UnexploredTile: "Onverkende Tegel",
   UNGeneralAssemblyCurrent: "Current UN General Assembly #%{id}",
   UNGeneralAssemblyMultipliers: "<b>+%{count}</b> Production, Worker Capacity and Storage Multpliers for <b>%{buildings}</b>",
   UNGeneralAssemblyNext: "Upcoming UN General Assembly #%{id}",
   UNGeneralAssemblyVoteEndIn: "You can change your vote any time before the voting ends in <b>%{time}</b>",
   UniqueBuildings: "Unieke Gebouwen",
   UniqueTechMultipliers: "Unique Tech Multipliers",
   UnitedNations: "United Nations",
   UnitedNationsDesc: "All Tier IV and V and VI buildings get +1 Production, Worker Capacity and Storage Multiplier. Participate in UN General Assembly and vote for an additional boost each week",
   University: "Universiteit",
   UnlockableResearch: "Ontgrendelbaar Onderzoek",
   UnlockBuilding: "Ontgrendelen",
   UnlockTechProgress: "Voortgang",
   UnlockXHTML: "Unlock <b>%{name}</b>",
   Upgrade: "Upgrade",
   UpgradeBuilding: "Upgrade",
   UpgradeBuildingNotProducingDescV2: "This building is being upgraded - <b>production will halt until upgrade is complete</b>",
   UpgradeTo: "Upgrade To Level %{level}",
   Uranium: "Uranium",
   UraniumEnrichmentPlant: "Uranium Enrichment Plant",
   UraniumMine: "Uranium Mine",
   Urbanization: "Verstedelijking",
   UserAgent: "User Agent: %{driver}",
   View: "Bekijk",
   ViewMenu: "Weergave",
   ViewTechnology: "Bekijk Technologie",
   Vineyard: "Wijngaard",
   VirtualReality: "Virtuele Realiteit",
   Voltaire: "Voltaire",
   WallOfBabylon: "Wall of Babylon",
   WallOfBabylonDesc: "All buildings get +N Storage Multiplier. N = number of unlocked ages / 2",
   WallStreet: "Wall Street",
   WallStreetDesc: "All buildings that produce coin, banknote, bond, stock and forex within 2 tile range get +N production multiplier. N = Random value between 1 to 5 which is different per building and changes with every market refresh. Double the effect of John D. Rockefeller",
   WaltDisney: "Walt Disney",
   Warehouse: "Magazijn",
   WarehouseAutopilotSettings: "Autopilot Settings",
   WarehouseAutopilotSettingsEnable: "Enable Autopilot",
   WarehouseAutopilotSettingsRespectCapSetting: "Require Storage < Cap",
   WarehouseAutopilotSettingsRespectCapSettingTooltip: "Autopilot will only transport resources whose amount in storage is below the cap",
   WarehouseDesc: "Vervoer specifieke bronnen en bied extra opslag",
   WarehouseExtension: "Unlock warehouse caravansary extension mode. Allow warehouses adjacent to caravansaries to be included in player trading",
   WarehouseSettingsAutopilotDesc: "Dit magazijn zal zijn ongebruikte capaciteit gebruiken om grondstoffen te transporteren van gebouwen die volle opslag hebben. Huidige ongebruikte capaciteit: %{capacity}",
   WarehouseUpgrade: "Ontgrendel magazijn autopiloot modus. Gratis transport tussen een magazijn en zijn aangrenzende gebouwen",
   WarehouseUpgradeDesc: "Gratis transport tussen dit magazijn en zijn aangrenzende tegels",
   Warp: "Warp",
   WarpSpeed: "Warp Speed",
   Water: "Water",
   WellStockedTooltip: "Well-stocked buildings are buildings that have enough resources for its production, which include buildings that are producing, that have full storage or not producing due to lack of workers",
   WernherVonBraun: "Wernher von Braun",
   Westminster: "Westminster",
   Wheat: "Tarwe",
   WheatFarm: "Tarweboerderij",
   WildCardGreatPersonDescV2: "When consumed, become any great person of the same age",
   WilliamShakespeare: "William Shakespeare",
   Wine: "Wijn",
   Winery: "Wijnmakerij",
   WishlistSpaceshipIdle: "Wishlist Spaceship Idle",
   Wonder: "Wonder",
   WonderBuilderCapacityDescHTML: "<b>Bouwcapaciteit</b> bij het bouwen van wonderen wordt beïnvloed door het <b>tijdperk</b> en de <b>technologie</b> die het wonder ontgrendelt",
   WondersBuilt: "Wereldwonderen Gebouwd",
   WondersUnlocked: "Wereldwonderen Ontgrendeld",
   WonderUpgradeLevel: "Wonder Level",
   Wood: "Hout",
   Worker: "Werknemer",
   WorkerCapacityMultiplier: "Worker Capacity Multiplier",
   WorkerHappinessPercentage: "Geluk Vermenigvuldiger",
   WorkerMultiplier: "Werknemerscapaciteit",
   WorkerPercentagePerHappiness: "%{value}% Vermenigvuldiger voor Elk Geluk",
   Workers: "Werknemers",
   WorkersAvailableAfterHappinessMultiplier: "Werknemers Na Geluk Vermenigvuldiger",
   WorkersAvailableBeforeHappinessMultiplier: "Werknemers Voor Geluk Vermenigvuldiger",
   WorkersBusy: "Bezette Werknemers",
   WorkerScienceProduction: "Werknemer Wetenschapsproductie",
   WorkersRequiredAfterMultiplier: "Vereiste Werknemers",
   WorkersRequiredBeforeMultiplier: "Vereiste Werknemerscapaciteit",
   WorkersRequiredForProductionMultiplier: "Productiecapaciteit Per Werknemer",
   WorkersRequiredForTransportationMultiplier: "Transportcapaciteit Per Werknemer",
   WorkersRequiredInput: "Transport",
   WorkersRequiredOutput: "Productie",
   WorldWarAge: "Wereldoorlogen",
   WorldWideWeb: "World Wide Web",
   WritersGuild: "Schrijversgilde",
   Writing: "Schrijven",
   WuZetian: "Keizerin Wu Zetian",
   WuZetianDesc: "+%{value} Transportcapaciteit Vermenigvuldiger",
   Xuanzang: "Xuanzang",
   YangtzeRiver: "Yangtze River",
   YangtzeRiverDesc: "All buildings that consume water get +1 Production, Worker Capacity and Storage Multiplier. Double the effect of Zheng He (Great Person). Each level of Permanent Empress Wu Zetian (Great Person) provides +1 Storage Multiplier to all buildings",
   YearOfTheSnake: "Year of the Snake",
   YearOfTheSnakeDesc:
      "After completed, when entering a new age, instead of getting one great person of each unlocked age, get the same amount of great people in the current age. All buildings within 2-tile range get +1 Production Multiplier. This wonder can be upgraded and each additional upgrade provides +1 Production Multiplier to buildings within 2-tile range. This wonder can only be constructed during the lunar new year period (1.20 ~ 2.10)",
   YellowCraneTower: "Yellow Crane Tower",
   YellowCraneTowerDesc: "+1 choice when choosing great people. All buildings within 1 tile range get +1 Production, Worker Capacity and Storage Multiplier. When constructed next to Yangtze River, the range increases to 2 tile",
   YuriGagarin: "Yuri Gagarin",
   ZagrosMountains: "Zagros Mountains",
   ZagrosMountainsDesc: "All adjacent buildings that have less than 5 Production Multiplier get +2 Production Multiplier. Double the effect of Nebuchadnezzar II (Great Person)",
   ZahaHadid: "Zaha Hadid",
   ZahaHadidDesc: "+%{value} Builder Capacity Multiplier",
   Zenobia: "Zenobia",
   ZenobiaDesc: "+%{value}h Petra Warp Storage",
   ZhengHe: "Zheng He",
   ZigguratOfUr: "Ziggurat of Ur",
   ZigguratOfUrDescV2: "Every 10 happiness (capped) provides +1 Production Multiplier to all buildings that do not produce workers and are unlocked in previous ages (max = number of unlocked ages / 2). Wonders (incl. Natural) no longer provide +1 Happiness. The effect can be turned off",
   Zoroaster: "Zoroaster",
   Zugspitze: "Zugspitze",
   ZugspitzeDesc: "For each unlocked age, get one point that can be used to provide one extra level to any Great Person that is born from this run",
};
