export const ZH_TW = {
   About: "關於放置文明",
   AbuSimbel: "阿布辛貝神殿",
   AbuSimbelDesc: "基於拉美西斯二世(永恆偉人)的能力，獲得等同其100%的效果。相鄰的 奇觀 獲得 +1 幸福感",
   AccountActiveTrade: "活躍中的訂單",
   AccountChatBadge: "聊天徽章",
   AccountCustomColor: "自訂帳戶顏色",
   AccountCustomColorDefault: "預設",
   AccountGreatPeopleLevelRequirement: "需求偉人等級",
   AccountLevel: "帳號等級",
   AccountLevelAedile: "市政官",
   AccountLevelConsul: "執政官",
   AccountLevelMod: "主持人",
   AccountLevelPlayTime: "帳號在線遊戲時間 > %{requiredTime} (你的遊戲時間為 %{actualTime})",
   AccountLevelPraetor: "裁判官",
   AccountLevelQuaestor: "財務官",
   AccountLevelSupporterPack: "支持者",
   AccountLevelTribune: "軍官",
   AccountLevelUpgradeConditionAnyHTML: "若要升級您的帳戶，需滿足<b>以下條件之一</b>：",
   AccountPlayTimeRequirement: "需求遊戲時間",
   AccountRankUp: "升級帳戶等級",
   AccountRankUpDesc: "您的所有進度將被保留到新的等級",
   AccountRankUpTip: "恭喜，您的帳戶符合更高等級的條件——點選此處升級！",
   AccountSupporter: "擁有支持者包",
   AccountTradePriceRange: "貿易價格範圍",
   AccountTradeTileReservationTime: "貿易地塊保留時間",
   AccountTradeTileReservationTimeDesc: "這是從你上次在線之後開始計時，你的玩家貿易地塊將被保留的時間。在保留期間結束後，其他玩家可以獲取你的占領地塊。",
   AccountTradeValuePerMinute: "每分鐘貿易值",
   AccountTypeShowDetails: "顯示帳號詳細資料",
   AccountUpgradeButton: "升至財務官等級",
   AccountUpgradeConfirm: "帳號升級",
   AccountUpgradeConfirmDescV2: "升級您的帳戶將<b>強制重生</b>並保留已擁有的永恆偉人。 此操作<b>無法取消</b>，確定升級嗎？",
   Acknowledge: "收到",
   Acropolis: "雅典衛城",
   ActorsGuild: "演員公會",
   AdaLovelace: "愛達·勒芙蕾絲",
   AdamSmith: "亞當·史密斯",
   AdjustBuildingCapacity: "生產能力",
   AdvisorElectricityContent:
      "發電廠建築為你提供了兩種新系統。第一種是「電力」，與電廠相鄰地塊以閃電圖示顯示。一些建築物（如世界大戰時期的建築廣播電台）在其輸入列表中會顯示「需要電力」的指示。<b>這意味著它們必須建在具有閃電圖示的地塊上才能運作</b>。需要電力的建築物如果有電力支持，也會將電力傳輸到該建築物相鄰的地塊，因此只要有一個建築物接觸到電廠，其它建築物就可以彼此供電。<br><br>另一個系統是「電氣化」，只要不是生產科學或工人的建築物，就可以將其應用到<b>地圖上的任何建築物</b>。這會消耗電廠產生的電力，以增加建築物的消耗和生產。更高的電氣化等級需要消耗越來越多的電力。對「需要電力」的建築物進行電氣化，比不需要電力的建築物效率更高。",
   AdvisorElectricityTitle: "電力與電氣化",
   AdvisorGreatPeopleContent:
      "每當你進入新的科技時代時，你可以從該時代及所有先前的時代中各選擇一位偉人。這些偉人會提供全域加成，提升生產力、科學、幸福感及其他等多種數值。<br><br>這些加成在整個重生之前是永久有效的。當你重生時，所有的偉人將成為永恆偉人，並且他們的加成效果會永久生效。<br><br>在之後的回合中選擇相同的偉人會讓永久加成和當前回合的加成疊加，重生時擁有重複的偉人時，重複的偉人會被儲存，並可用於升級永恆偉人。這可以在你的主建築物中的<b>管理永恆偉人</b>選單中查看。",
   AdvisorGreatPeopleTitle: "偉人",
   AdvisorHappinessContent:
      "幸福感是放置文明中限制擴張的核心機制。你可以透過解鎖新科技、進入新時代、建造奇觀、偉人提供的加成，以及你在學習中發現的一些其他方法來獲得幸福感。<b>每建造一個新建築物需要消耗 1 點幸福感</b>。每多出或少於 0 點幸福感，會為你的總工人提供 2% 的加成或懲罰（上限為 -50 和 +50 點幸福感）。你可以在<b>主建築物的幸福感部分</b>查看幸福感的詳細資訊。",
   AdvisorHappinessTitle: "維持人民的幸福感",
   AdvisorOkay: "明白，謝謝！",
   AdvisorScienceContent:
      "忙碌的工人會產生科學，這使你可以解鎖新科技並推進你的文明。你可以通過多種方式訪問研究選單，包括點選((資源欄)科學計量器、訪問主建築物中的可解鎖科技，或使用「查看」選單。這些操作都會將你帶到科技樹，顯示所有科技以及每項科技所需的科學量。如果你有足夠的科學來學習新科技，只需點選它並在側邊欄選單中按下「解鎖」即可。<b>每個新科技層級和時代需要越來越多的科學，但你也會解鎖新的、更好方法來獲得科學。</b>",
   AdvisorScienceTitle: "科學發現！",
   AdvisorSkipAllTutorials: "跳過所有教學",
   AdvisorStorageContent:
      "雖然建築物有一定的儲存容量，但如果長時間閒置，仍可能會被填滿。<b>當建築物滿載時，它們將無法再生產</b>。這不一定是個問題，因為當建築物滿載時，表示你已經有了大量的存貨。但通常來說，讓建築物持續生產會更好。<br><br>解決滿載問題的一種方法是建造倉庫。當你建造倉庫時，你可以看到一個所有已發現產品的清單，並可以設置庫存量以存入任何產品，只要總量在倉庫的等級和儲存倍率範圍內。<br><br>一個簡單的倉庫設置方法是勾選每個想要導入倉庫的產品，並使用「在選定項目中重新分配」按鈕來平均分配進口速率和儲存量。如果希望建築物也能從倉庫中提取，請確保開啟「低於限額仍運輸」選項。",
   AdvisorStorageTitle: "儲存與倉庫",
   AdvisorTraditionContent:
      "一些奇觀（如洽高。占比爾、盧克索神廟、大笨鐘）提供了一組新選項，讓你制定重生之路。每個奇觀允許你從文明的傳統、宗教和意識形態中分別選擇 1 個選項。<br><br>選定後，該選擇會在本次重生中鎖定，無法更改，但你可以在未來的重生中選擇其他選項。一旦選擇，每個選項都可以透過提供必要資源多次升級。每一層的獎勵是累計的，例如第 1 層提供 +1 生產，第 2 層也提供 +1 生產，那麼在第 2 層時，你總共會有 +2 生產。",
   AdvisorTraditionTitle: "具有路徑選擇且可升級的世界奇觀",
   AdvisorWonderContent:
      "奇觀是提供全域效果的特殊建築物，對你的遊戲進程有重大影響。除了列出的功能外，所有奇觀還會提供 +1 點幸福感。但你需要小心，因為<b>建造奇觀需要大量資源，並且有比正常更高的建造者容量</b>。這意味著它們可能會迅速消耗你的資源庫存，導致其他建築物資源短缺。<b>你可以自由設定每種輸入資源是否運輸</b>，這讓你可以分階段建造奇觀，同時儲備足夠的資源來維持所有建築物的運作。",
   AdvisorWonderTitle: "世界奇觀",
   AdvisorWorkerContent:
      "每當建築物生產或運輸物品時，都需要勞工。如果你沒有足夠的勞工可用，某些建築物將無法在該周期運作。最明顯的解決方法是通過建造或升級建築物（小屋/房屋/公寓/公寓大廈）來增加總工人數量。<br><br><b>請注意，建築物在升級時會暫停運作，無法提供任何資源，包括工人，因此你可能只想一次升級一個住宅建築。</b>遊戲早期階段的一個好目標是保持大約 70% 的工人忙碌。如果超過 70% 的工人在忙碌，則升級/建造住宅建築。如果少於 70% 的工人在忙碌，則擴大生產。",
   AdvisorWorkerTitle: "勞工管理",
   Aeschylus: "艾斯奇勒斯",
   Agamemnon: "阿加曼農",
   AgeWisdom: "時代智慧",
   AgeWisdomDescHTML: "每級的時代智慧提供該時代符合資格的永久偉人的<b>同等級加成</b> - 可使用符合資格的永久偉人碎片進行升級",
   AgeWisdomGreatPeopleShardsNeeded: "您還需要 %{amount} 個偉人碎片才能進行下一次時代智慧升級",
   AgeWisdomGreatPeopleShardsSatisfied: "您已擁有足夠的偉人碎片來進行下一次時代智慧升級",
   AgeWisdomNeedMoreGreatPeopleShards: "需要更多偉人碎片",
   AgeWisdomNotEligible: "此偉人不符合時代智慧的資格",
   AgeWisdomSource: "%{age} 智慧: %{person}",
   AgeWisdomUpgradeWarningHTMLV3: "當帳戶等級為軍官 (Tribune)，升級至財務官 (Quaestor) 時，時代智慧<b>無法繼承</b>",
   AGreatPersonIsBorn: "一位偉人誕生了",
   AircraftCarrier: "航空母艦",
   AircraftCarrierYard: "航空母艦船塢",
   Airplane: "飛機",
   AirplaneFactory: "飛機工廠",
   Akitu: "阿基圖 (Akitu): 烏爾大塔廟 (Ziggurat Of Ur) 和幼發拉底河 (Euphrates River) 可作用於當前時代已解鎖的建築",
   AlanTuring: "艾倫·圖靈",
   AlanTuringDesc: "+%{value} 來自閒置工人的科學",
   AlbertEinstein: "阿爾伯特·愛因斯坦",
   Alcohol: "酒",
   AldersonDisk: "奧爾德森磁碟(Alderson Disk)",
   AldersonDiskDesc: "+25 幸福。這個奇蹟可以升級，每次額外升級都會提供 +5 幸福值",
   Alloy: "合金",
   Ally: "同盟國",
   Alps: "阿爾卑斯山",
   AlpsDesc: "建築每 10 級，獲得 +1 生產能力( +1 消費乘數， +1 生產乘數)",
   Aluminum: "鋁",
   AluminumSmelter: "鋁廠",
   AmeliaEarhart: "愛蜜莉亞·艾爾哈特",
   American: "美國",
   AndrewCarnegie: "安德魯·卡內基",
   AngkorWat: "吳哥窟",
   AngkorWatDesc: "所有相鄰建築，獲得 +1 工人能力乘數，並增加 1000 名工人",
   AntiCheatFailure: "您的帳戶等級由於<b>未能通過反作弊檢查</b>而受到限制，若想提出申訴，請聯絡開發商",
   AoiMatsuri: "葵祭: 富士山產生的時間扭曲效果加倍",
   Apartment: "公寓",
   Aphrodite: "阿芙蘿黛蒂女神",
   AphroditeDescV2: "建築物升級至 20 級以上時，每級獲得 +2 建築者能力乘數。所有已解鎖的古典時代永久偉人在本次運行中等級 +1",
   ApolloProgram: "阿波羅計劃",
   ApolloProgramDesc: "所有火箭工廠產量、工人能力和儲存乘數均 +2 。 每個相鄰火箭工廠的衛星工廠、太空船工廠和核子飛彈發射井獲得 +1 生產乘數",
   ApplyToAll: "套用到全部",
   ApplyToAllBuilding: "套用到全部 %{building}",
   ApplyToBuildingInTile: "套用到 %{tile} 地塊內的所有 %{building}",
   ApplyToBuildingsToastHTML: "成功套用到 <b>%{count} %{building}</b>",
   Aqueduct: "水渠",
   ArcDeTriomphe: "凱旋門",
   ArcDeTriompheDescV2: "每 1 幸福（最多 50）為所有建築物提供 +1 建造者能力乘數。",
   Archimedes: "阿基米德",
   Architecture: "建築學",
   Aristophanes: "阿里斯托芬",
   AristophanesDesc: "+%{value} 幸福感",
   Aristotle: "亞里斯多德",
   Arithmetic: "算術",
   Armor: "盔甲",
   Armory: "軍火庫",
   ArtificialIntelligence: "人工智慧",
   Artillery: "火炮",
   ArtilleryFactory: "火炮工廠",
   AshokaTheGreat: "阿育王",
   Ashurbanipal: "亞述巴尼拔",
   Assembly: "組裝",
   Astronomy: "天文學",
   AtomicBomb: "原子彈",
   AtomicFacility: "原子能設施",
   AtomicTheory: "原子理論",
   Atomium: "原子球塔",
   AtomiumDescV2: "所有在 2 格範圍內產生科學的建築獲得 +5 生產乘數。產出與 2 格範圍內所有建築產出科學的加總。建築完工時獲得現已解鎖最昂貴所需科學之值",
   Autocracy: "制度",
   Aviation: "航空學",
   Babylonian: "巴比倫",
   BackToCity: "返回城市",
   BackupRecovery: "還原備份",
   Bakery: "烘焙坊",
   Ballistics: "彈道學",
   Bank: "銀行",
   Banking: "銀行業",
   BankingAdditionalUpgrade: "所有 10 級以上建築，獲得 +1 儲存乘數",
   Banknote: "鈔票",
   BaseCapacity: "基礎運輸能力",
   BaseConsumption: "基礎消耗",
   BaseMultiplier: "基礎乘數",
   BaseProduction: "基礎產出",
   BastilleDay: "國慶日：蓬皮杜中心與凱旋門的效果變為 2 倍，聖米歇爾山的文化產出也變為 2 倍。",
   BatchModeTooltip: "已選擇 %{count} 座建築，將升級所有選擇建築",
   BatchSelectAllSameType: "所有相同類型",
   BatchSelectAnyType1Tile: "相鄰 1 格內的任意類型",
   BatchSelectAnyType2Tile: "相鄰 2 格內的任意類型",
   BatchSelectAnyType3Tile: "相鄰 3 格內的任意類型",
   BatchSelectSameType1Tile: "相鄰 1 格內的相同類型",
   BatchSelectSameType2Tile: "相鄰 2 格內的相同類型",
   BatchSelectSameType3Tile: "相鄰 3 格內的相同類型",
   BatchSelectSameTypeSameLevel: "相同類型相同等級",
   BatchSelectThisBuilding: "僅此建築",
   BatchStateSelectActive: "啟用",
   BatchStateSelectAll: "所有",
   BatchStateSelectTurnedFullStorage: "儲存已滿",
   BatchStateSelectTurnedOff: "未啟用",
   BatchUpgrade: "批量升級",
   Battleship: "戰艦",
   BattleshipBuilder: "戰艦造船廠",
   BecomeAllyTooltip: "要成為鄰國的同盟國，請選擇與他們相同的旗幟（地球旗幟除外）",
   BigBen: "大笨鐘",
   BigBenDesc: "+2 科學來自忙碌的工人。選擇一個帝國政治意識形態，每次選擇都可解鎖更多加成",
   Biplane: "雙翼機",
   BiplaneFactory: "雙翼機工廠",
   Bitcoin: "比特幣",
   BitcoinMiner: "比特幣礦工",
   BlackForest: "黑森林",
   BlackForestDesc: "被發現時，顯示地圖上所有木材地塊。在相鄰地塊生成木材。所有消耗木材或木板的建築物獲得 +5 生產倍數",
   Blacksmith: "工匠鋪",
   Blockchain: "區塊鏈",
   BlueMosque: "藍色清真寺",
   BlueMosqueDesc: "該奇觀使所有奇觀（自然奇觀除外）為相鄰的所有建築提供 +1 生產、勞動者能力以及存儲乘數。當聖索菲亞大教堂建造完成在旁邊時該奇觀雙倍作用效果",
   BobHope: "鮑伯·霍伯",
   BobHopeDesc: "+%{value} 幸福感",
   Bond: "債券",
   BondMarket: "債券市場",
   Book: "書",
   BoostCyclesLeft: "剩餘加成生效週期",
   BoostDescription: "%{buildings} +%{value} %{multipliers}",
   Borobudur: "婆羅浮屠",
   BorobudurDesc: "婆羅浮屠",
   BranCastle: "布蘭城堡",
   BranCastleDesc: "位於羅馬尼亞布蘭鄉的城堡",
   BrandenburgGate: "布蘭登堡門",
   BrandenburgGateDesc: "所有煤礦與油井，獲得 +1 生產、儲存以及工人能力乘數。與煉油廠相鄰的每個含原油資源地塊，使煉油廠獲得 +1 生產、儲存以及工人能力乘數",
   Bread: "麵包",
   Brewery: "釀酒廠",
   Brick: "磚",
   Brickworks: "磚窯",
   BritishMuseum: "大英博物館",
   BritishMuseumChooseWonder: "選擇一個世界奇觀",
   BritishMuseumDesc: "建造完成後，可以變更為其他文明的可建造世界奇觀（只有一次機會）",
   BritishMuseumTransform: "變更",
   Broadway: "百老匯",
   BroadwayCurrentlySelected: "當前選擇的",
   BroadwayDesc: "誕生一位當前與上一時代偉人。選擇一位使其效果翻倍",
   BronzeAge: "青銅時代",
   BronzeTech: "青銅",
   BuddhismLevelX: "佛教 %{level}",
   Build: "建造",
   BuilderCapacity: "建造者能力",
   BuildingColor: "建築色彩",
   BuildingColorMatchBuilding: "從建築複製顏色",
   BuildingColorMatchBuildingTooltip: "複製這資源的建築顏色。如果有多座建築生產此資源，將隨機選擇其中一座。",
   BuildingDefaults: "建築預設值",
   BuildingDefaultsCount: "%{count} 屬性在建立預設值時被覆蓋",
   BuildingDefaultsRemove: "清除所有建築設定",
   BuildingEmpireValue: "建築帝國價值 / 資源帝國價值",
   BuildingMultipliers: "乘數",
   BuildingName: "建築名稱",
   BuildingNoMultiplier: "%{building} <b>不受任何乘數影響</b>(生產、勞動能力、儲存等)",
   BuildingSearchText: "輸入建築名稱搜尋",
   BuildingTier: "階",
   Cable: "電纜",
   CableFactory: "電纜工廠",
   Calendar: "曆法",
   CambridgeUniversity: "劍橋大學",
   CambridgeUniversityDesc: "文藝復興及之後的時代智慧額外獲得 +1 等級",
   CambridgeUniversitySource: "劍橋大學 (%{age})",
   Cancel: "取消",
   CancelAllUpgradeDesc: "取消所有 %{building} 升級",
   CancelUpgrade: "取消升級",
   CancelUpgradeDesc: "所有已被運送的資源將保留在此建築中。",
   Cannon: "大炮",
   CannonWorkshop: "大炮工廠",
   CannotEarnPermanentGreatPeopleDesc: "因為這次是試經營，所以無法得到永恆偉人。",
   Capitalism: "資本主義",
   Cappadocia: "卡帕多西亞",
   CappadociaDesc: "周圍 3 地塊範圍內所有的建築，當升級超過等級 30 的建築時，超過的每個等級獲得 +1 生產、勞動者能力以及存儲乘數",
   Car: "汽車",
   Caravansary: "商隊旅館",
   CaravansaryDesc: "與其他玩家交易資源，同時提供額外儲存空間。",
   Caravel: "輕快帆船",
   CaravelBuilder: "輕快帆船船塢",
   CarFactory: "汽車工廠",
   CarlFriedrichGauss: "卡爾·弗瑞德呂希·高斯",
   CarlFriedrichGaussDesc: "+%{idle} 來自閒置工人的科學。 +%{busy} 來自忙碌工作者的科學",
   CarlSagan: "卡爾·薩根(Carl Sagan)",
   Census: "人口普查",
   CentrePompidou: "蓬皮杜中心",
   CentrePompidouDesc:
      "建造完成後，所有建築物將獲得 +1 的生產加成與 +2 的儲存加成倍數。若當前遊戲已進入資訊時代，且下一輪遊戲選擇不同文明，則此奇觀將會保留。每當以一個獨特文明達到資訊時代並重生時，該奇觀將在重生時提升 1 級，每個等級提供額外 +1 生產與 +2 儲存加成倍數。此奇觀的價值不會被計入帝國總價值中，且大英博物館無法轉換為此奇觀。",
   CentrePompidouWarningHTML: "蓬皮杜中心將消失，如果你重生在 <b>%{civ}</b>",
   CerneAbbasGiant: "塞那阿巴斯巨人像",
   CerneAbbasGiantDesc: "每建造完成一座奇觀時，獲得一位當前時代的偉人",
   ChangePlayerHandle: "更換",
   ChangePlayerHandleCancel: "取消",
   ChangePlayerHandledDesc: "您的玩家名稱只能包含 5 ~ 16 個字母與數字，且不與其他玩家重複",
   Chariot: "戰車",
   ChariotWorkshop: "戰車工坊",
   Charlemagne: "查理曼大帝",
   CharlesDarwin: "查爾斯·達爾文",
   CharlesDarwinDesc: "忙碌工人產出科學 +%{value}",
   CharlesMartinHall: "查爾斯·馬丁·霍爾 (Charles Martin Hall)",
   CharlesParsons: "查爾斯·阿爾傑農·帕爾森斯(Charles Parsons)",
   CharlieChaplin: "查理·卓別林",
   CharlieChaplinDesc: "+%{value} 幸福感",
   Chat: "聊天",
   ChatChannel: "聊天頻道",
   ChatChannelLanguage: "語言",
   ChatHideLatestMessage: "隱藏最新消息內容",
   ChatNoMessage: "沒有消息",
   ChatReconnect: "網路已中斷，正在重新連線…",
   ChatSend: "傳送",
   CheckInAndExit: "登入並離開",
   CheckInCloudSave: "登入雲端存檔",
   CheckOutCloudSave: "登出雲端存檔",
   Cheese: "奶酪",
   CheeseMaker: "奶酪作坊",
   Chemistry: "化學",
   ChesterWNimitz: "切斯特·威廉·尼米茲(Chester W. Nimitz)",
   ChichenItza: "奇琴伊察",
   ChichenItzaDesc: "相鄰的建築，獲得 +1 生產乘數、儲存以及工人能力乘數",
   Chinese: "中國",
   ChoghaZanbil: "恰高·占比爾",
   ChoghaZanbilDescV2: "選擇帝國傳統，每次選擇都會解鎖更多提升",
   ChooseGreatPersonChoicesLeft: "你還有%{count}個選擇",
   ChristianityLevelX: "基督教 %{level}",
   Church: "禮拜堂",
   CircusMaximus: "馬克西穆斯競技場",
   CircusMaximusDescV2: "+5 幸福感。所有音樂家公會、作家公會和畫家公會的生產和儲存倍數增加+1",
   CityState: "城邦國家",
   CityViewMap: "城市",
   CivGPT: "Civ聊天生成預訓練轉換器(CivGPT)",
   CivIdle: "放置文明",
   CivIdleInfo: "由 Fish Pond 工作室榮譽出品",
   Civilization: "文明",
   CivilService: "體制",
   CivOasis: "Civ虛擬實境(CivOasis)",
   CivTok: "CivTok",
   ClaimedGreatPeople: "已選用偉人",
   ClaimedGreatPeopleTooltip: "您重生時有 %{total} 個偉人，其中 %{claimed} 個已被選用",
   ClassicalAge: "古典時代",
   ClearAfterUpdate: "在市場更新後，清除所有交易",
   ClearSelected: "清除選中",
   ClearSelection: "取消選擇",
   ClearTransportPlanCache: "清除運輸計劃快取",
   Cleopatra: "克麗奧佩脫拉",
   CloneFactory: "克隆工廠(Clone Factory)",
   CloneFactoryDesc: "克隆任何資源",
   CloneFactoryInputDescHTML: "克隆工廠只能複製從<b>%{buildings}</b>直接運輸的<b>%{res}</b>",
   CloneLab: "克隆實驗室(Clone Lab)",
   CloneLabDesc: "將任何資源轉化為科學",
   CloneLabScienceMultiplierHTML: "克隆實驗室僅適用於<b>生產乘數加成<b>不適用於</b>科學生產建築加成<b>（例如 原子球塔 的生產乘數）",
   Cloth: "織物",
   CloudComputing: "雲端運算",
   CloudSaveRefresh: "刷新",
   CloudSaveReturnToGame: "返回遊戲",
   CNTower: "加拿大國家電視塔",
   CNTowerDesc: "所有電影製片廠、廣播電台和電視台均不受-1 幸福度影響。 所有在世界大戰和冷戰中解鎖的​​建築都會獲得+N 生產、工人容量和儲存乘數。 N = 建築的層數和時代之間的差異",
   Coal: "煤",
   CoalMine: "煤礦",
   CoalPowerPlant: "燃煤電廠",
   Coin: "硬幣",
   CoinMint: "鑄幣局",
   ColdWarAge: "冷戰",
   CologneCathedral: "科隆大教堂",
   CologneCathedralDesc: "建造完成時，產生一次性科技值，相當於當前時代最昂貴技術的成本。所有生產科學的建築（不包括複製實驗室）獲得 +1 生產倍數。此奇觀可升級，每次升級額外提供 +1 生產倍數給所有生產科學的建築（不包括複製實驗室）",
   Colonialism: "殖民主義",
   ColorBlue: "藍色",
   ColorCinereous: "灰色",
   ColorChocolate: "巧克力",
   ColorCyan: "青色",
   ColorDarkBlue: "深藍",
   ColorFlamingo: "火烈鳥",
   ColorGlaucous: "灰藍",
   ColorGreen: "綠色",
   ColorNone: "默認",
   ColorPink: "粉紅色",
   ColorPurple: "紫色",
   ColorOrange: "橙色",
   ColorRed: "紅色",
   ColorSeaGreen: "海洋綠",	
   ColorTurquoise: "寶石綠",
   Colosseum: "羅馬競技場",
   ColosseumDescV2: "戰車工坊免於 -1 幸福感懲罰。消耗 10 輛戰車並產生 10 點幸福感。每解鎖一個時代額外獲得 2 點幸福感",
   ColossusOfRhodes: "羅得島太陽神銅像",
   ColossusOfRhodesDesc: "所有不生產工人的相鄰建築，獲得 +1 幸福感",
   Combustion: "內燃機",
   Commerce4UpgradeHTMLV2: "解鎖後，所有<b>鄰近銀行</b>可免費升級至<b>30等級</b>",
   CommerceLevelX: "商業 %{level}",
   Communism: "共產主義",
   CommunismLevel4DescHTML: "誕生一位<b>工業時代</b>偉人和一位<b>世界大戰時代</b>偉人",
   CommunismLevel5DescHTML: "誕生一位<b>冷戰時代</b>偉人。進入新時代時，額外獲得<b>2 位</b>該時代的偉人",
   CommunismLevelX: "共產主義等級 %{level}",
   Computer: "電腦",
   ComputerFactory: "電腦工廠",
   ComputerLab: "電腦實驗室(Computer Lab)",
   Concrete: "混凝土",
   ConcretePlant: "混凝土廠",
   Condo: "公寓大廈",
   ConfirmDestroyResourceContent: "你將銷毀 %{amount} %{resource}，此操作不能取消",
   ConfirmNo: "取消",
   ConfirmYes: "確認",
   Confucius: "孔子",
   ConfuciusDescV2: "+%{value} 來自所有工人的科學，如果超過 50% 的工人忙碌且少於 50% 的忙碌工人從事交通運輸工作",
   ConnectToADevice: "連接到設備",
   Conservatism: "保守主義",
   ConservatismLevelX: "保守主義等級 %{level}",
   Constitution: "憲法",
   Construction: "結構",
   ConstructionBuilderBaseCapacity: "基礎能力",
   ConstructionBuilderCapacity: "建造者能力",
   ConstructionBuilderMultiplier: "能力乘數",
   ConstructionBuilderMultiplierFull: "建造者能力乘數",
   ConstructionCost: "建造消耗：%{cost}",
   ConstructionDelivered: "已運送",
   ConstructionPriority: "建造優先等級",
   ConstructionProgress: "進度",
   ConstructionResource: "資源",
   Consume: "消耗",
   ConsumeResource: "消耗： %{resource}",
   ConsumptionMultiplier: "消費者乘數",
   ContentInDevelopment: "內容開發中",
   ContentInDevelopmentDesc: "此遊戲內容仍在開發中，可在將來的遊戲更新中獲取，敬請期待！",
   Copper: "銅",
   CopperMiningCamp: "銅礦",
   CosimoDeMedici: "科西莫·德·麥地奇",
   Cotton: "棉花",
   CottonMill: "紡織廠",
   CottonPlantation: "植棉場",
   Counting: "林業",
   Courthouse: "法院",
   CristoRedentor: "救世基督像",
   CristoRedentorDesc: "鄰近2格建築，免除-1幸福感",
   CrossPlatformAccount: "跨平台帳戶",
   CrossPlatformConnect: "連接",
   CrossPlatformSave: "跨平台存檔",
   CrossPlatformSaveLastCheckIn: "上次登入",
   CrossPlatformSaveStatus: "當前狀態",
   CrossPlatformSaveStatusCheckedIn: "已登入",
   CrossPlatformSaveStatusCheckedOut: "已在 %{platform} 上登出",
   CrossPlatformSaveStatusCheckedOutTooltip: "您的跨平台存檔已在另一個平台上登出，您需要先在該平台登入，然後才能在當前平台登出",
   Cultivation4UpgradeHTML: "一位<b>文藝復興時代</b>的偉人誕生",
   CultivationLevelX: "耕作 %{level}",
   Culture: "文化",
   Culus: "登基大典：卡帕多西亞的效果加倍。亞拉拉特山的效果將基於有效偉人等級的平方根，而不是立方根。",
   CurrentLanguage: "繁體中文",
   CurrentPlatform: "當前平台",
   CursorBigOldFashioned: "3D (大)",
   CursorOldFashioned: "3D",
   CursorStyle: "游標樣式",
   CursorStyleDescHTML: "變更游標樣式後，<b>需重啟遊戲才會生效</b>",
   CursorSystem: "系統",
   Cycle: "Cycle",
   CyrusII: "居魯士二世",
   DairyFarm: "乳製品農場",
   DefaultBuildingLevel: "建造預設等級",
   DefaultConstructionPriority: "預設建造優先等級",
   DefaultProductionPriority: "預設生產優先等級",
   DefaultStockpileMax: "預設最大儲存庫容量",
   DefaultStockpileSettings: "預設儲存庫輸入容量",
   DeficitResources: "資源赤字",
   Democracy: "律法",
   DemolishAllBuilding: "拆除 %{tile} 範圍內的所有 %{building}",
   DemolishAllBuildingConfirmContent: "您確定要拆除 %{count} 個 %{name} 嗎？",
   DemolishAllBuildingConfirmTitle: "要拆除 %{count} 個建築物？",
   DemolishBuilding: "拆除此建築",
   DennisRitchie: "丹尼斯·里奇(Dennis Ritchie)",
   Deposit: "地塊資源",
   DepositTileCountDesc: "在 %{city} 有 %{count} 個地塊含有 %{deposit}",
   Dido: "狄多",
   Diplomacy: "外交",
   DistanceInfinity: "無限制",
   DistanceInTiles: "距離格數",
   DolmabahcePalace: "多爾瑪巴赫切宮",
   Drilling: "鑽井",
   DukeOfZhou: "周公旦",
   DuneOfPilat: "比拉沙丘",
   DuneOfPilatDesc: "在每個時代中，上個時代的時代智慧等級加成將提高一倍。",
   DynamicMultiplierTooltip: "這個乘數是動態的——它不會影響工人和存儲",
   Dynamite: "炸藥",
   DynamiteWorkshop: "炸藥工廠",
   DysonSphere: "戴森球(Dyson Sphere)",
   DysonSphereDesc: "所有建築物都獲得+5 生產乘數。這個奇蹟可以升級，每次額外升級都會為所有建築物提供 +1 生產乘數",
   EasterBunny: "復活節兔子",
   EasterBunnyDesc: "建造完成後，重生時10％額外偉人將以此生偉人方式進入下輪遊玩不再以永恆碎片，並在下輪遊玩中建造復活節兔子後根據已解鎖時代誕生這些偉人。此奇觀僅在 4 月可建造。",
   EastIndiaCompany: "東印度公司",
   EastIndiaCompanyDescV2:
      "這座世界奇觀會累積您與其他玩家完成交易的總貿易價值。每累積 2000 點貿易價值可獲得一個周期，使所有建築每與一座運作中的商隊旅館相鄰，獲得 +0.5 的生產加成倍數。此奇觀可升級，每升級一次，額外獲得 +0.5 的生產加成倍數。當您完成其他玩家提出的交易請求，或您的交易請求被其他玩家完成時，透過交易所獲得的物品價值才會被計入（包含稅收部分）。多次交易可延長加成效果的持續時間，且加成效果可疊加。",
   Education: "教育學",
   EffectiveGreatPeopleLevel: "有效偉人等級",
   EffectiveGreatPeopleLevelDesc: "有效偉人等級是所有永恆偉人等級與時代智慧等級的總和。此數值用來評估偉人和時代智慧提供的效果增益",
   Egyptian: "埃及",
   EiffelTower: "巴黎鐵塔",
   EiffelTowerDesc: "所有相鄰煉鋼廠，獲得 +N 生產、儲存以及工人乘數。 N 取決於其相鄰煉鋼廠數量",
   Elbphilharmonie: "易北愛樂廳",
   ElbphilharmonieDesc: "3 個地塊範圍內的所有建築物，每個相鄰的不同級別建築物可獲得 +1 生產倍數",
   Electricity: "電力",
   Electrification: "電氣化",
   ElectrificationPowerRequired: "需要供電",
   ElectrificationStatusActive: "啟用",
   ElectrificationStatusDesc: "需要與不需要電力的建築都可以電氣化。然而，需要電力的建築提供更高的電氣化效率",
   ElectrificationStatusNoPowerV2: "沒有足夠的電力",
   ElectrificationStatusNotActive: "未啟用",
   ElectrificationStatusV2: "電氣化狀態",
   ElectrificationUpgrade: "解鎖電氣化以允許建築消耗電力提高生產",
   Electrolysis: "電解",
   ElvisPresley: "貓王",
   ElyseePalace: "愛麗舍宮",
   EmailDeveloper: "寫電子郵件給開發者",
   Embassy: "大使館",
   EmperorWuOfHan: "漢武帝",
   EmpireValue: "帝國價值",
   EmpireValueByHour: "每小時帝國價值",
   EmpireValueFromBuilding: "此建築帝國價值",
   EmpireValueFromBuildingsStat: "來自建築",
   EmpireValueFromResources: "此建築資源價值",
   EmpireValueFromResourcesStat: "來自資源",
   EmpireValueIncrease: "帝國價值增長",
   EmptyTilePageBuildLastBuilding: "建造最後一次建造的建築",
   EndConstruction: "取消建造",
   EndConstructionDescHTML: "當您完成建造時，已經使用的所有資源<b>將不會被退還</b>。</b>",
   Engine: "發動機",
   Engineering: "工程學",
   English: "英國",
   Enlightenment: "啟蒙運動",
   Enrichment: "鈾濃縮技術",
   EnricoFermi: "恩里科·費米",
   EstimatedTimeLeft: "預計剩餘時間",
   EuphratesRiver: "幼發拉底河(Euphrates River)",
   EuphratesRiverDesc: "每 10% 從事生產（非運輸）的忙碌工人為所有不生產工人的建築物提供 +1 生產乘數（最大值 = 解鎖時代數 / 2）。當旁邊建造空中花園時，空中花園解鎖後每個時代 獲得+1效果。發現後，在所有相鄰的沒有沉積物的方塊上產生水",
   ExpansionLevelX: "擴張 %{level}",
   Exploration: "探索",
   Explorer: "探險家",
   ExplorerRangeUpgradeDesc: "將探險者的範圍提升至 %{range}",
   ExploreThisTile: "派出一位探險家",
   ExploreThisTileHTML: "一位探險家將探索<b>此地塊及其相鄰的地塊</b>。探險家在%{name}中生成。您還剩下%{count}位探險家。",
   ExtraGreatPeople: "%{count}額外偉人",
   ExtraGreatPeopleAtReborn: "重生時的額外偉人",
   ExtraTileInfoType: "額外地塊資訊",
   ExtraTileInfoTypeDesc: "選擇每個地塊下方顯示資訊",
   ExtraTileInfoTypeEmpireValue: "帝國價值",
   ExtraTileInfoTypeNone: "不顯示",
   ExtraTileInfoTypeStoragePercentage: "儲存百分比",
   Faith: "信仰",
   Farming: "農業",
   FavoriteBuildingAdd: "加入至我的最愛",
   FavoriteBuildingEmptyToast: "你目前沒有將任何建築加入最愛。",
   FavoriteBuildingRemove: "從我的最愛刪除",
   FeatureRequireQuaestorOrAbove: "此功能需要財務官 (Quaestor) 或以上等級",
   Festival: "節慶",
   FestivalCycle: "節慶週期",
   FestivalTechTooltipV2: "正向幸福值（最多 50）轉換為節慶點數。每累積 %{point} 節慶點數，可以讓您的帝國進入節慶週期，獲得特定地圖的重大加成。此地圖的節慶加成為 %{desc}",
   FestivalTechV2: "解鎖節慶功能 - 正向幸福值（最多 50）轉換為節慶點數。每累積 %{point} 節慶點數，可以讓您的帝國進入節慶週期，獲得特定地圖的重大加成",
   Feudalism: "封建主義",
   Fibonacci: "費波拉契",
   FibonacciDescV2: "+%{idle} 科學來自閒置的工人。+%{busy} 科學來自忙碌的工人。費波那契的升級成本遵循費波那契數列",
   FighterJet: "戰鬥機",
   FighterJetPlant: "戰鬥機工廠",
   FilterByAge: "按時代過濾",
   FinancialArbitrage: "金融套利(Financial Arbitrage)",
   FinancialLeverage: "資金槓桿",
   Fire: "鑽木取火",
   Firearm: "火器",
   FirstTimeGuideNext: "下一步",
   FirstTimeTutorialWelcome: "歡迎來到放置文明",
   FirstTimeTutorialWelcome1HTML:
      "歡迎來到放置文明。在這款遊戲中，您將經營自己的帝國：<b>管理生產、解鎖科技、與其他玩家交易資源、獲取偉人與建造世界奇觀</b>。<br><br>拖動滑鼠來移動，使用滑鼠滾輪縮放。點選空白地塊以建造新建築，點選建築物以查看它。<br><br>某些建築物，如石礦和伐木營地，需要建造在資源地塊上。開發者建議：將小屋放置在迷霧旁邊，這樣它會提供勞工——建築會需要一些時間來完成。完成後，它將清除附近的迷霧。",
   FirstTimeTutorialWelcome2HTML:
      "建築物可以升級——這需要花費資源與時間。當建築物升級時，<b>它將不再生產</b>。這包括提供勞動者的建築，<b>所以永遠不要同時升級所有建築物！</b><br><br>隨著您的帝國擴展，您將獲得更多科學點數得以解鎖新科技。系統會在達到該階段時告訴您更多有關這方面的內容，但您可以前往 場景 -> 研究 來快速查看。<br><br>",
   FirstTimeTutorialWelcome3HTML: "現在您已經了解了遊戲的所有基本知識，您可以開始建設您的帝國了(可以在 [幫助 -> 新手教學] 重新導覽)。但在開始之前，您應該<b>選擇一個玩家名稱</b>並在遊戲內聊天室打個招呼。我們擁有一個非常有幫助的社群（企鹅群907371958）：如果您有問題或需求，別害怕詢問！",
   Fish: "魚",
   FishPond: "魚塘",
   FlorenceNightingale: "弗羅倫絲．南丁格爾",
   FlorenceNightingaleDesc: "幸福感 +%{value}",
   Flour: "麵粉",
   FlourMill: "麵粉磨坊",
   FontSizeScale: "字體大小比例",
   FontSizeScaleDescHTML: "更改介面字體大小。 <b>比例大於 1 倍可能會影響或破壞遊戲介面</b>",
   ForbiddenCity: "紫禁城",
   ForbiddenCityDesc: "所有造紙廠、作家公會以及印刷廠， 獲得 +1 生產乘數、工人能力乘數以及儲存乘數。與北京的奇觀萬里長城有搭配效果",
   Forex: "外匯",
   ForexMarket: "外匯市場",
   FrankLloydWright: "法蘭克·洛伊·萊特",
   FrankLloydWrightDesc: "+%{value}建造者能力乘數",
   FrankWhittle: "法蘭克·惠特爾",
   FreeThisWeek: "本週免費",
   FreeThisWeekDescHTMLV2: "<b>每週</b>，其中一個付費文明將免費開放遊玩。本週的免費文明是<b>%{city}</b>",
   French: "法國",
   Frigate: "護衛艦",
   FrigateBuilder: "護衛艦船塢",
   Furniture: "家具",
   FurnitureWorkshop: "家具工廠",
   Future: "未來",
   GabrielGarciaMarquez: "加布列·賈西亞·馬奎斯",
   GabrielGarciaMarquezDesc: "+%{value} 幸福感",
   GalileoGalilei: "伽利略·伽利萊",
   GalileoGalileiDesc: "+%{value} 科學來自閒置工人",
   Galleon: "大帆船",
   GalleonBuilder: "大帆船船塢",
   Gameplay: "遊戲設定",
   Garment: "服裝",
   GarmentWorkshop: "製衣廠",
   GasPipeline: "天然氣管道",
   GasPowerPlant: "天然氣發電廠",
   GatlingGun: "加特林機槍",
   GatlingGunFactory: "加特林機槍工廠",
   Genetics: "遺傳學",
   Geography: "地理學",
   GeorgeCMarshall: "喬治·卡特萊特·馬歇爾",
   GeorgeWashington: "喬治·華盛頓",
   GeorgiusAgricola: "格奧爾格·阿格里科拉",
   German: "德國",
   Glass: "玻璃",
   Glassworks: "玻璃工廠",
   GlobalBuildingDefault: "全部建築預設值",
   Globalization: "全球化",
   GoBack: "返回",
   Gold: "黃金",
   GoldenGateBridge: "金門大橋",
   GoldenGateBridgeDesc: "所有 發電廠 生產乘數+1。鄰近2格地塊供電(電力連接)",
   GoldenPavilion: "金閣寺",
   GoldenPavilionDesc: "周圍 3 格地塊範圍內的建築，每個相鄰的建築物如果生產任何消耗資源，將獲得 +1 生產倍數（不包括複製實驗室和複製工廠，且該建築不可關閉）",
   GoldMiningCamp: "金礦",
   GordonMoore: "高登·摩爾(Gordon Moore)",
   GrandBazaar: "大巴扎(伊斯坦堡大市集)",
   GrandBazaarDesc: "提供市場整合介面。相鄰奇觀的商隊旅館 +5 生產、儲存倍數，相鄰的市場提供不同的交易內容。",
   GrandBazaarFilters: "篩選器",
   GrandBazaarFilterWarningHTML: "必須先使用篩選器以顯示交易內容",
   GrandBazaarFilterYouGet: "獲得",
   GrandBazaarFilterYouPay: "支付",
   GrandBazaarSeach: "搜尋",
   GrandBazaarSearchGet: "獲得",
   GrandBazaarSearchPay: "支付",
   GrandBazaarTabActive: "啟用中的交易",
   GrandBazaarTabTrades: "交易篩選清單",
   GrandCanyon: "大峽谷",
   GrandCanyonDesc: "當前時代解鎖的建築物獲得 +2 生產倍數。J.P. 摩根效果加倍",
   GraphicsDriver: "顯示卡驅動程式： %{driver}",
   GreatDagonPagoda: "仰光大金塔",
   GreatDagonPagodaDescV2: "所有寶塔都免除-1幸福值。根據所有佛塔的信仰產生科學",
   GreatMosqueOfSamarra: "薩邁拉大清真寺",
   GreatMosqueOfSamarraDescV2: "+1 建築視野範圍。隨機顯示 5 個地圖上尚未探索的資源地塊，並在上面建造 10 級的建築",
   GreatPeople: "偉人",
   GreatPeopleEffect: "效果",
   GreatPeopleFilter: "輸入名字或時代以篩選偉人",
   GreatPeopleName: "名字",
   GreatPeoplePermanentColumn: "永恆",
   GreatPeoplePermanentShort: "永恆",
   GreatPeoplePickPerRoll: "偉人每次選擇數(Great People Pick Per Roll)",
   GreatPeopleThisRun: "此生獲得偉人",
   GreatPeopleThisRunColumn: "此生",
   GreatPeopleThisRunShort: "此生",
   GreatPersonLevelRequired: "需要永恆偉人等級",
   GreatPersonLevelRequiredDescV2: "%{city} 文明需要 %{required} 永恆偉人等級。您目前擁有 %{current}",
   GreatPersonPromotionPromote: "升級",
   GreatPersonThisRunEffectiveLevel: "你目前在此輪遊戲中有%{count}個%{person}，新獲得的%{person}將具有1/%{effect}的效果",
   GreatPersonWildCardBirth: "出生",
   GreatSphinx: "獅身人面像",
   GreatSphinxDesc: "範圍 2 格內所有二階或以上建築 +N 消耗、生產能力乘數，N = 與受益建築相鄰的同類建築數量。",
   GreatWall: "萬里長城",
   GreatWallDesc: "範圍 1 格內所有建築 +N 生產、工人能力和儲存乘數，N = 當前科技時代 與 建築所屬時代 所相差的值，若與<b>紫禁城</b>相鄰，地塊範圍增加至 2 格",
   GreedyTransport: "用於建設或升級的貪婪運輸",
   GreedyTransportDescHTML: "將使建築物即使擁有足夠的資源來進行當前升級，也繼續運輸資源，這樣可以使多級升級<b>更快</b>，但最終會運輸<b>多餘的資源</b>",
   Greek: "希臘",
   GrottaAzzurra: "卡普里島藍洞",
   GrottaAzzurraDescV2: "被發現時，所有您的 I 級建築物獲得 +5 等級以及 +1 生產、工人容量和儲存倍數",
   Gunpowder: "火藥",
   GunpowderMill: "火藥廠",
   GuyFawkesNightV2: "蓋伊·福克斯之夜：東印度公司提供雙倍加成效果。倫敦塔橋獲取偉人的速度提升 20%。 ",
   HagiaSophia: "聖索菲亞大教堂",
   HagiaSophiaDescV2: "+5 幸福感，未生產之建築免去 -1 幸福感。隨發展提供額外幸福感以避免生產暫停 ",
   HallOfFame: "名人堂",
   HallOfSupremeHarmony: "太和殿",
   Hammurabi: "漢摩拉比",
   HangingGarden: "空中花園",
   HangingGardenDesc: "+1 建造者能力乘數。相鄰水渠獲得 +1 生產、儲存以及工人能力乘數",
   Happiness: "幸福感",
   HappinessFromBuilding: "來自建築(奇觀除外)",
   HappinessFromBuildingTypes: "來自具有良好庫存的建築種類",
   HappinessFromHighestTierBuilding: "來自最高階的工作建築",
   HappinessFromUnlockedAge: "來自已解鎖時代",
   HappinessFromUnlockedTech: "來自已解鎖科技",
   HappinessFromWonders: "來自奇觀(包括自然奇觀)",
   HappinessUncapped: "幸福感(實際值)",
   HarryMarkowitz: "哈利·馬可維茲(Harry Markowitz)",
   HarunAlRashid: "哈倫·拉希德",
   Hatshepsut: "哈特謝普蘇特",
   HatshepsutTemple: "哈特謝普蘇特神殿",
   HatshepsutTempleDesc: "完成建造時，地圖顯現所有水資源地塊。與小麥農場相鄰的每個含水資源地塊，使小麥農場獲得 +1 生產乘數",
   Headquarter: "總部",
   HedgeFund: "對沖基金(Hedge Fund)",
   HelpMenu: "幫助",
   HenryFord: "亨利·福特",
   Herding: "畜牧",
   Herodotus: "希羅多德",
   HighlightBuilding: "高亮 %{building}",
   HighlightMoreBuildings: "高亮更多貿易地塊加成",
   HimejiCastle: "姬路城",
   HimejiCastleDesc: "所有輕快帆船船塢、大帆船船塢以及護衛艦船塢，獲得 +1 生產乘數、工人能力乘數以及儲存乘數",
   Hollywood: "好萊塢",
   HollywoodDesc: "+5 幸福感。 鄰近2格範圍的消耗或產生文化 儲備充足的建築，都可以增加 +1 幸福感。",
   HolyEmpire: "神職",
   Homer: "荷馬",
   Honor4UpgradeHTML: "<b>鄭和</b>(偉人)效果加倍",
   HonorLevelX: "榮譽 %{level}",
   Horse: "馬匹",
   HorsebackRiding: "馬術",
   House: "房屋",
   Housing: "住宅",
   Hut: "小屋",
   HydroDam: "水壩",
   Hydroelectricity: "水力發電",
   HymanGRickover: "海曼·李高佛(Hyman G. Rickover)",
   IdeologyDescHTML: "從<b>自由主義、保守主義、社會主義或共產主義</b>中選擇作為您的帝國政治意識形態。選擇後，您<b>無法切換意識形態</b>。您可以在每個政治意識形態中解鎖更多的增益。",
   IMPei: "貝聿銘",
   IMPeiDesc: "+%{value}建造者能力乘數",
   Imperialism: "帝國主義",
   ImperialPalace: "皇宮",
   IndustrialAge: "工業時代",
   InformationAge: "資訊時代",
   InputResourceForCloning: "用於克隆的輸入資源",
   InternationalSpaceStation: "國際太空站(International Space Station)",
   InternationalSpaceStationDesc: "所有建築物都獲得 +5 儲存乘數。這個奇蹟可以升級，每次額外升級都會為所有建築物提供 +1 儲存乘數",
   Internet: "網路",
   InternetServiceProvider: "網際網路服務供應商(Internet Service Provider)",
   InverseSelection: "反選",
   Iron: "鐵",
   IronAge: "鐵器時代",
   Ironclad: "鐵甲艦",
   IroncladBuilder: "鐵甲艦船塢",
   IronForge: "鐵鍛鋪",
   IronMiningCamp: "鐵礦",
   IronTech: "生鐵",
   IsaacNewton: "艾薩克·牛頓",
   IsaacNewtonDescV2: "+%{value} 來自所有工人的科學，如果超過 50% 的工人忙碌且少於 50% 的忙碌工人從事交通運輸工作",
   IsambardKingdomBrunel: "伊桑巴德·金德姆·布魯內爾",
   IsidoreOfMiletus: "米利都的伊西多爾",
   IsidoreOfMiletusDesc: "+%{value} 建造者能力乘數",
   Islam5UpgradeHTML: "解鎖時獲得科學，數量等同於解鎖<b>工業時代</b>最昂貴科技所需",
   IslamLevelX: "伊斯蘭教 %{level}",
   ItsukushimaShrine: "嚴島神社",
   ItsukushimaShrineDescV2: "當一個時代的所有科技都被解鎖時，產生一次性科學點數，相當於下一個時代中最便宜科技的成本",
   JamesWatson: "詹姆斯·杜威·華生",
   JamesWatsonDesc: "+%{value} 來自忙碌工作者的科學",
   JamesWatt: "詹姆斯·瓦特",
   Japanese: "日本",
   JetPropulsion: "噴射推進",
   JohannesGutenberg: "古騰堡",
   JohannesKepler: "約翰尼斯·克卜勒",
   JohnCarmack: "約翰·卡馬克(John Carmack)",
   JohnDRockefeller: "約翰·洛克斐勒",
   JohnMcCarthy: "約翰·麥卡錫(John McCarthy)",
   JohnVonNeumann: "約翰·馮諾伊曼",
   JohnVonNeumannDesc: "忙碌工人產出科學 +%{value} ",
   JoinDiscord: "加入Discord",
   JosephPulitzer: "約瑟夫·普立茲",
   Journalism: "新聞學",
   JPMorgan: "約翰·皮爾龐特·摩根",
   JRobertOppenheimer: "羅伯特·奧本海默",
   JuliusCaesar: "尤利烏斯·凱撒",
   Justinian: "查士丁尼一世",
   Kanagawa: "神奈川",
   KanagawaDesc: "當前時代的所有偉人在本輪遊戲中，額外獲得 +1 等級（不包括澤諾比亞）",
   KarlMarx: "卡爾·馬克思",
   Knight: "騎士",
   KnightCamp: "騎士營地",
   Koti: "💵瑞士貨幣",
   KotiInStorage: "已存儲",
   KotiProduction: "💵瑞士貨幣",
   LandTrade: "商學",
   Language: "語言",
   Lapland: "拉普蘭",
   LaplandDesc: "被發現時，顯示整張地圖。範圍內 2 格內的所有建築獲得 +N 的生產加成倍數，其中 N 為已解鎖的時代數。此自然奇觀僅可在 12 月被發現。",
   LargeHadronCollider: "大型強子對撞機",
   LargeHadronColliderDescV2: "資訊時代的所有偉人本輪遊戲獲得 +2 等級。此奇觀可升級，每次升級額外提供 +1 等級",
   Law: "法律",
   Lens: "鏡頭",
   LensWorkshop: "鏡頭工廠",
   LeonardoDaVinci: "李奧納多·達·文西",
   Level: "等級",
   LevelX: "等級 %{level}",
   Liberalism: "自由主義",
   LiberalismLevel3DescHTML: "對於倉庫<b>輸出</b>與<b>輸入</b>的資源調用，免費運輸",
   LiberalismLevel5DescHTML: "<b>雙倍</b>電氣化效果",
   LiberalismLevelX: "自由主義等級 %{level}",
   Library: "圖書館",
   LighthouseOfAlexandria: "亞歷山大燈塔",
   LighthouseOfAlexandriaDesc: "所有相鄰建築，獲得 +5 儲存乘數",
   LinusPauling: "萊納斯·鮑林",
   LinusPaulingDesc: "閒置工人產出科學 +%{value} ",
   Literature: "文學",
   LiveData: "居住價值",
   LocomotiveFactory: "火車頭工廠",
   Logging: "森林",
   LoggingCamp: "伐木場",
   LouisSullivan: "路易斯·蘇利文(Louis Sullivan)",
   LouisSullivanDesc: "+%{value} 建造者能力乘數",
   Louvre: "盧浮宮",
   LouvreDesc: "每累積 10 位額外的偉人，將從所有已解鎖的時代中誕生一位偉人。",
   Lumber: "木材",
   LumberMill: "鋸木場",
   LunarNewYear: "農曆新年：長城對建築物的提升效果加倍。大報恩寺琉璃塔使本輪遊戲中的所有偉人獲得 +1 等級",
   LuxorTemple: "盧克索神殿",
   LuxorTempleDescV2: "+1 忙碌工人科學產出。選擇帝國信奉之宗教，選項將解鎖更多增益",
   Machinery: "機械",
   Magazine: "雜誌",
   MagazinePublisher: "雜誌出版社",
   Maglev: "磁浮列車(Maglev)",
   MaglevFactory: "磁浮列車工廠(Maglev Factory)",
   MahatmaGandhi: "聖雄甘地",
   ManageAgeWisdom: "管理時代智慧",
   ManagedImport: "自動儲存",
   ManagedImportDescV2: "自動將%{range}範圍內生產的資源運送到此建築。建築的資源傳輸無法手動變更，並忽略最大運輸距離",
   ManageGreatPeople: "偉人管理",
   ManagePermanentGreatPeople: "管理永恆偉人",
   ManageSave: "管理存檔",
   ManageWonders: "奇觀管理",
   Manhattan: "曼哈頓",
   ManhattanProject: "曼哈頓計畫",
   ManhattanProjectDesc: "所有鈾礦生產、工人和儲存能力乘數 +2 。與鈾礦相鄰的鈾濃縮場與原子能設施的生產乘數 +1 ",
   Marble: "石雕",
   Marbleworks: "石雕鋪",
   MarcoPolo: "馬可·波羅",
   MarieCurie: "瑪麗·居禮(居禮夫人)",
   MarinaBaySands: "濱海灣金沙酒店(Marina Bay Sands)",
   MarinaBaySandsDesc: "所有建築物均獲得 +5 工人能力乘數。這個奇蹟可以升級，每次額外升級都會為所有建築物提供 +1 工人能力乘數",
   Market: "市場",
   MarketDesc: "能將資源交換為另一種資源，每小時更新一次交易資源。",
   MarketRefreshMessage: " %{count} 個市場的交易已刷新",
   MarketSell: "交易",
   MarketSettings: "設定",
   MarketValueDesc: "利潤 %{value}",
   MarketYouGet: "購買",
   MarketYouPay: "賣出",
   MartinLuther: "馬丁·路德",
   MaryamMirzakhani: "瑪麗安·米爾札哈尼",
   MaryamMirzakhaniDesc: "+%{value} 來自閒置工人的科學",
   Masonry: "石匠技術",
   MatrioshkaBrain: "俄羅斯娃娃大腦(套腦)(Matrioshka Brain)",
   MatrioshkaBrainDescV2: "允許科學在計算帝國價值時被計入（5 科學 = 1 帝國價值）。每個忙碌和閒置的工人提供 +5 科學。此奇觀可升級，每次升級額外提供 +1 科學和生產倍數",
   MausoleumAtHalicarnassus: "摩索拉斯王陵墓",
   MausoleumAtHalicarnassusDescV2: "周圍 2 地塊範圍內所有建築，對於範圍內建築 “輸出” 與 “輸入” 的資源調用，免費運輸",
   MaxExplorers: "最大探險家數量",
   MaxTransportDistance: "最大運輸距離",
   Meat: "肉",
   Metallurgy: "冶煉",
   Michelangelo: "米開朗基羅",
   MiddleAge: "中世紀",
   MiddleClick: "滑鼠中鍵複製藍圖",
   MiddleClickDescHTML: "選取你要<b>複製藍圖的建築</b>點擊，別點擊任何地塊！直接<b>按滑鼠中鍵</b>指著想要建設同類建築的地塊",
   MilitaryTactics: "軍事戰略",
   Milk: "牛奶",
   Moai: "復活節島摩艾石像",
   MoaiDesc: "由玻里尼西亞東部復活節島上的拉帕努伊人雕刻的石像",
   MobileOverride: "行動裝置覆蓋",
   MogaoCaves: "莫高窟(敦煌石窟)",
   MogaoCavesDescV3: "忙碌工人占比每 10% 獲得 +1 幸福感。所有產出信仰的相鄰建築免去 -1 幸福感",
   MonetarySystem: "貨幣制度",
   MontSaintMichel: "聖米歇爾山",
   MontSaintMichelDesc: "從閒置勞動者處產生額外的文化值。為 2 格範圍內的所有建築物提供 +1 的儲存加成倍數。此奇觀可使用所產生的文化進行升級，每次額外升級都會為 2 格範圍內的所有建築物額外提供 +1 的儲存加成倍數。",
   Mosque: "清真寺",
   MotionPicture: "電影",
   MountArarat: "亞拉拉特山",
   MountAraratDesc: "周圍 2 地塊範圍內所有的建築，獲得 +X 生產、勞動者能力以及存儲乘數。X = 有效偉人等級的立方根",
   MountFuji: "富士山",
   MountFujiDescV2: "當佩特拉古城建造在旁邊時，佩特拉古城獲得 +8h 時間扭曲儲存。當遊戲運行時，每分鐘在佩特拉古城生成 20 時間扭曲（不受佩特拉古城本身加速影響，離線時不生效）",
   MountSinai: "西奈山",
   MountSinaiDesc: "當被發現時，根據帝國所處時代誕生一位偉人。所有產出信仰的建築獲得 +5 儲存乘數",
   MountTai: "泰山",
   MountTaiDesc: "所有產生科學的建築獲得 +1 生產乘數。 孔子(偉人)效果加倍。當被發現時，一次性獲得等同解鎖最昂貴科技所需的科學點數",
   MoveBuilding: "移動建築",
   MoveBuildingFail: "無效選擇此地塊",
   MoveBuildingNoTeleport: "沒有足夠的傳送能力",
   MoveBuildingSelectTile: "選擇一個地塊",
   MoveBuildingSelectTileToastHTML: "選擇地圖上的<b>已探索的未建築地塊</b>作為目標",
   Movie: "電影",
   MovieStudio: "電影製片廠",
   Museum: "博物館",
   Music: "音樂",
   MusiciansGuild: "音樂家公會",
   MutualAssuredDestruction: "相互保證毀滅",
   MutualFund: "共同基金(Mutual Fund)",
   Name: "名字",
   Nanotechnology: "奈米技術",
   NapoleonBonaparte: "拿破崙·波拿巴",
   NaturalGas: "天然氣",
   NaturalGasWell: "天然氣井",
   NaturalWonderName: "自然奇觀： %{name}",
   NaturalWonders: "自然奇觀",
   Navigation: "航海術",
   NebuchadnezzarII: "尼布甲尼撒二世",
   Neighbor: "鄰國",
   Neighbors: "鄰國",
   NeighborsDescHTML:
      "對於每個鄰國擁有的地塊，你得到<b> +%{neighbor} 生產乘數的貿易地塊加成</b>，對於每個同盟國擁有的地塊，你得到<b> +%{ally} 生產乘數的貿易地塊加成</b>。要成為鄰國的盟友，請選擇與他們相同的旗幟（地球旗幟除外）",
   Neuschwanstein: "新天鵝堡",
   NeuschwansteinDesc: "建造奇觀時 +10 建造者能力乘數",
   Newspaper: "報紙",
   NextExplorersIn: "探險家誕生剩餘時間",
   NextMarketUpdateIn: "市場刷新剩餘時間",
   NiagaraFalls: "尼加拉大瀑布",
   NiagaraFallsDescV2: "所有倉庫、市場和商隊都獲得 +N 儲存倍數。 N = 已解鎖時代數。愛因斯坦為研究基金提供 +1 生產乘數(不受百老匯等其他增益效果影響)",
   NielsBohr: "尼爾斯·波耳",
   NielsBohrDescV2: "+%{value} 來自所有工人的科學，如果超過 50% 的工人忙碌且少於 50% 的忙碌工人從事交通運輸工作",
   NileRiver: "尼羅河",
   NileRiverDesc: "哈特謝普蘇特(偉人)效果加倍。所有小麥農場獲得 +1 生產、儲存乘數。相鄰小麥農場獲得 +5 生產、儲存乘數",
   NoPowerRequired: "此建築不需要電力",
   NothingHere: "此處沒有東西",
   NotProducingBuildings: "未生產建築",
   NuclearFission: "核裂變",
   NuclearFuelRod: "核燃料棒",
   NuclearMissile: "核子飛彈",
   NuclearMissileSilo: "核子飛彈發射井",
   NuclearPowerPlant: "核電廠",
   NuclearReactor: "核反應器",
   NuclearSubmarine: "核子動力潛艇",
   NuclearSubmarineYard: "核子動力潛艇工廠",
   OdaNobunaga: "織田信長",
   OfflineErrorMessage: "目前離線中，此操作需要網路",
   OfflineProduction: "離線生產",
   OfflineProductionTime: "離線生產時間",
   OfflineProductionTimeDescHTML: "對於 <b> 首個 %{time} 離線時間</b> , 您可以在這裡選擇分配離線生產與扭曲時間的比例。把 <b> 超過 4 小時的離線時間 </b> 全部轉化為扭曲時間",
   OfflineTime: "離線時間",
   Oil: "原油",
   OilPress: "榨油機",
   OilRefinery: "煉油廠",
   OilWell: "油井",
   Ok: "確認",
   Oktoberfest: "啤酒節：楚格峰效果加倍",
   Olive: "橄欖",
   OlivePlantation: "橄欖農場",
   Olympics: "奧運會",
   OnlyAvailableWhenPlaying: "僅在遊玩 %{city} 時可用",
   OpenLogFolder: "開啟記錄資料夾",
   OpenSaveBackupFolder: "開啟備份資料夾",
   OpenSaveFolder: "開啟存檔資料夾",
   Opera: "歌劇",
   OperationNotAllowedError: "無法進行此操作",
   Opet: "歐佩特：人面獅身像不再增加消耗倍數",
   OpticalFiber: "光纖",
   OpticalFiberPlant: "光纖工廠",
   Optics: "光學",
   OptionsMenu: "選項",
   OptionsUseModernUIV2: "使用抗鋸齒字體",
   OsakaCastle: "大阪城",
   OsakaCastleDesc: "為範圍內 2 格的所有地塊供電。允許產生科學的建築物電氣化（包括克隆實驗室）",
   OtherPlatform: "其他平台",
   Ottoman: "奧斯曼帝國",
   OttoVonBismarck: "奧托·馮·俾斯麥",
   OxfordUniversity: "牛津大學",
   OxfordUniversityDescV3: "產生等同其他所有產出科學建築科學的 10% 。建築完成時，獲得等同現已解鎖最昂貴科技所花費的科學點數",
   PabloPicasso: "巴勃羅·畢卡索",
   Pagoda: "佛塔",
   PaintersGuild: "畫家公會",
   Painting: "繪畫",
   PalmJumeirah: "朱美拉棕櫚島(Palm Jumeirah)",
   PalmJumeirahDesc: "+10 建造者能力。這個奇蹟可以升級，每次額外升級都會提供 +2 建造者能力",
   Pamukkale: "棉花城堡",
   PamukkaleDesc: "當此奇觀被發現時，將每個偉人（萬能偉人和媒介偉人除外）消耗 1 偉人碎片（可用偉人碎片需要大於0）轉換為本輪遊玩中的同一偉人（此生偉人）",
   Panathenaea: "泛雅典娜節：波塞頓為所有建築提供 +1 生產倍數",
   Pantheon: "萬神殿",
   PantheonDescV2: " 2 地塊範圍內的所有建築獲得 +1 工人能力與儲存乘數。產生的科學基於所有神社的信仰總產出",
   Paper: "紙張",
   PaperMaker: "造紙廠",
   Parliament: "議會",
   Parthenon: "帕德嫩神殿",
   ParthenonDescV2: "兩位古典時代偉人誕生，每位將有 4 個選項提供選擇。所有音樂家、畫家公會的生產、儲存倍數 +1，並免除 -1 幸福感",
   Passcode: "一次性密碼",
   PasscodeToastHTML: "<b>%{code}</b> 是您的一次性密碼，有效期為 30 分鐘",
   PatchNotes: "更新記錄",
   Peace: "和平(Peace)",
   Peacekeeper: "和平衛士(Peacekeeper)",
   Penthouse: "都市",
   PercentageOfProductionWorkers: "生產工人比例(Percentage of Production Workers)",
   Performance: "效能",
   PermanentGreatPeople: "永恆偉人",
   PermanentGreatPeopleAcquired: "獲得永恆的偉人",
   PermanentGreatPeopleUpgradeUndo: "撤銷永恆偉人升級：這將把升級等級轉回碎片，您將獲得 %{amount} 碎片",
   Persepolis: "波斯波利斯",
   PersepolisDesc: "所有銅礦、伐木場以及採石場 +1 生產乘數、工人能力乘數、儲存乘數",
   PeterHiggs: "彼得·希格斯",
   PeterHiggsDesc: "+%{value} 來自忙碌工人的科學",
   Petra: "佩特拉古城",
   PetraDesc: "當你離線時生成扭曲時間，你可以用它來加速你的帝國",
   PetraOfflineTimeReconciliation: "你已獲得 %{count} 扭曲時間",
   Petrol: "汽油",
   PhiloFarnsworth: "費羅·法恩斯沃斯",
   Philosophy: "哲學",
   Physics: "物理學",
   PierreDeCoubertin: "皮耶·德·古柏坦",
   PinResourceTab: "懸浮窗",
   Pizza: "披薩",
   Pizzeria: "披薩店",
   PlanetaryRover: "行星漫遊者(Planetary Rover)",
   Plastics: "塑膠",
   PlasticsFactory: "塑膠工廠",
   PlatformAndroid: "Android",
   PlatformiOS: "iOS",
   PlatformSteam: "Steam",
   PlatformSyncInstructionHTML: "如果您希望將此設備上的進度同步到新設備，請點選<b>同步到新設備</b>以獲取一次性密碼。在新設備上，點選<b>連接到設備</b>並輸入一次性密碼",
   Plato: "柏拉圖",
   PlayerHandle: "玩家資訊",
   PlayerHandleOffline: "你目前離線中",
   PlayerMapClaimThisTile: "佔領此地塊",
   PlayerMapClaimTileCondition2: "你沒有被反作弊系統查禁",
   PlayerMapClaimTileCondition3: "已解鎖所需科學： %{tech}",
   PlayerMapClaimTileCondition4: "未佔領任何地塊或已過變更佔領的冷卻時間",
   PlayerMapClaimTileCooldownLeft: "冷卻時間剩餘：%{time}",
   PlayerMapClaimTileNoLongerReserved: "此地塊已不再受保留，你可以驅逐 <b>%{name}</b> 並佔領它。",
   PlayerMapEstablishedSince: "佔領時間",
   PlayerMapLastSeenAt: "上次最後在線時間",
   PlayerMapMapAllyTileBonus: "同盟國的貿易地塊加成",
   PlayerMapMapNeighborTileBonus: "鄰國的貿易地塊加成",
   PlayerMapMapTileBonus: "貿易地塊加成",
   PlayerMapMenu: "貿易",
   PlayerMapOccupyThisTile: "佔領此地塊",
   PlayerMapOccupyTileCondition1: "此地塊與您的宣稱地塊或佔領地塊相鄰",
   PlayerMapPageGoBackToCity: "返回城市",
   PlayerMapSetYourTariff: "設定你的關稅",
   PlayerMapTariff: "關稅",
   PlayerMapTariffApply: "套用關稅稅率",
   PlayerMapTariffDesc: "每項經過你佔領地的貿易，都將會向你繳納關稅。如果你增加關稅，將從每項途經貿易中獲取更多，但貿易經過的數量會更少。",
   PlayerMapTileAvailableTilePoint: "可用地塊點數",
   PlayerMapTileFromOccupying: "來自宣稱地塊或佔領地塊",
   PlayerMapTileFromOccupyingTooltipHTML: "宣稱地塊或佔領地塊每個將每小時生成<b>%{point}</b>地塊點數（從第一個宣稱地塊開始，每個地塊最多累計生成%{max}天）",
   PlayerMapTileFromRank: "來自帳號等級",
   PlayerMapTileTilePoint: "地塊點數（重新宣稱將不保留生成的地塊點數）",
   PlayerMapTileUsedTilePoint: "已使用地塊點數",
   PlayerMapTileUsedTilePointTooltipHTML: "你需要<b> 1 地塊點數</b>來宣稱地塊或佔領地塊",
   PlayerMapTradesFrom: "%{name} 的貿易訂單",
   PlayerMapUnclaimedTile: "無玩家佔領地塊",
   PlayerMapYourTile: "佔領地",
   PlayerTrade: "玩家貿易",
   PlayerTradeAddSuccess: "成功新增訂單",
   PlayerTradeAddTradeCancel: "取消",
   PlayerTradeAmount: "數量",
   PlayerTradeCancelDescHTML: "取消訂單後，獲得<b>%{res}</b>退款：<b>%{percent}</b>收取退款費用，<b>%{discard}</b>因儲存空間不足而無法獲取<br><b>確定取消訂單嗎？",
   PlayerTradeCancelTrade: "取消訂單",
   PlayerTradeClaim: "領取",
   PlayerTradeClaimAll: "領取全部訂單",
   PlayerTradeClaimAllFailedMessageV2: "無法領取任何交易，請確認儲存空間是否已滿？",
   PlayerTradeClaimAllMessageV2: "您已發起：<b>%{resources}</b>",
   PlayerTradeClaimAvailable: "共 %{count} 筆已完成訂單可領取",
   PlayerTradeClaimTileFirst: "需在貿易地圖上佔領地塊",
   PlayerTradeClaimTileFirstWarning: "在貿易地圖上佔領地塊後，你才能與其他玩家貿易。",
   PlayerTradeClearAll: "清除所有數值",
   PlayerTradeClearFilter: "清除篩選",
   PlayerTradeDisabledBeta: "只有在測試版發布後才能創建和玩家交易",
   PlayerTradeFill: "交貨",
   PlayerTradeFill50: "交貨 50%",
   PlayerTradeFill95: "交貨 95%",
   PlayerTradeFillAmount: "交貨數量",
   PlayerTradeFillAmountMaxV2: "填充最大",
   PlayerTradeFillBy: "交單者",
   PlayerTradeFillPercentage: "填充百分比",
   PlayerTradeFillSuccessV2: "<b>%{success}/%{total}</b> 貿易已經完成。你已支付 <b>%{fillAmount} %{fillResource}</b> 並收到 <b>%{receivedAmount} %{receivedResource}</b>",
   PlayerTradeFillTradeButton: "交貨",
   PlayerTradeFillTradeTitle: "交貨訂單",
   PlayerTradeFilters: "篩選器",
   PlayerTradeFiltersApply: "套用",
   PlayerTradeFiltersClear: "清除",
   PlayerTradeFilterWhatIHave: "根據我所擁有的進行篩選",
   PlayerTradeFrom: "訂單發起人",
   PlayerTradeIOffer: "支付",
   PlayerTradeIWant: "購買",
   PlayerTradeMaxAll: "最大所有填充",
   PlayerTradeMaxTradeAmountFilter: "最大購買數量",
   PlayerTradeMaxTradeExceeded: "已達帳號等級最大訂單數",
   PlayerTradeNewTrade: "新的貿易",
   PlayerTradeNoFillBecauseOfResources: "由於資源不足，未完成任何交易",
   PlayerTradeNoValidRoute: "無法在你和 %{name} 之間找到有效貿易路線",
   PlayerTradeOffer: "支付",
   PlayerTradePlaceTrade: "送出訂單",
   PlayerTradePlayerNameFilter: "玩家名稱",
   PlayerTradeResource: "資源",
   PlayerTradeStorageRequired: "所需儲存空間",
   PlayerTradeTabImport: "進口",
   PlayerTradeTabPendingTrades: "待處理",
   PlayerTradeTabTrades: "貿易",
   PlayerTradeTariffTooltip: "獲得自貿易關稅",
   PlayerTradeWant: "購買",
   PlayerTradeYouGetGross: "稅前所得： %{res}",
   PlayerTradeYouGetNet: "稅後所得： %{res}",
   PlayerTradeYouPay: "支付： %{res}",
   Poem: "文章",
   PoetrySchool: "詩歌學校",
   Politics: "政治",
   PolytheismLevelX: "多神教 %{level}",
   PorcelainTower: "大報恩寺琉璃塔",
   PorcelainTowerDesc: "+5 幸福。 建造完成後，您當前所有額外的偉人都將可以參加此世界(加成衰減的倍率與 此生偉人 相同的規則)",
   PorcelainTowerMaxPickPerRoll: "每輪優先最大選擇",
   PorcelainTowerMaxPickPerRollDescHTML: "在完成大報恩寺琉璃塔後選擇偉人時，每輪優先最大選擇可用數量",
   Poseidon: "海神波塞頓",
   PoseidonDescV2: "所有相鄰建築獲得等級 25 的免費升級和 +N 生產、工人容量與儲存倍數（N = 建築等級）",
   PoultryFarm: "家禽農場",
   Power: "電力",
   PowerAvailable: "可用電力",
   PowerUsed: "已使用電力",
   PreciousMetal: "貴金屬",
   Printing: "印刷",
   PrintingHouse: "印刷廠",
   PrintingPress: "印刷術",
   PrivateOwnership: "資產",
   Produce: "產出",
   ProduceResource: "產出： %{resource}",
   ProductionMultiplier: "生產乘數",
   ProductionPriority: "生產優先等級",
   ProductionPriorityDescV4: "生產優先等級決定建築的運輸和生產順序——較高數字表示建築會先於其他建築進行運輸和生產。然而，較高的優先等級並不保證建築會優先運輸資源。例如：煤電廠 > 煤礦 > 鋼廠的優先順序中，鋼廠將在同一周期內獲得所有煤礦生產的煤",
   ProductionWorkers: "生產工人",
   Progress: "進度",
   ProgressTowardsNextGreatPerson: "接近下一位將於重生時出現的偉人進度",
   ProgressTowardsTheNextGreatPerson: "距離獲得下一位偉人的進度",
   PromotionGreatPersonDescV2: "將任一同時代的永久偉人升級到下一時代",
   ProphetsMosque: "先知寺",
   ProphetsMosqueDesc: "哈倫·拉希德(偉人)效果翻倍。其產生的科學，基於所有先知(清真)寺的信仰總產出",
   Province: "行政區",
   ProvinceAegyptus: "埃及",
   ProvinceAfrica: "非洲",
   ProvinceAsia: "亞洲",
   ProvinceBithynia: "比提尼亞",
   ProvinceCantabri: "坎塔布里亞",
   ProvinceCappadocia: "卡帕多西亞",
   ProvinceCilicia: "奇利西亞",
   ProvinceCommagene: "科馬吉內",
   ProvinceCreta: "克里特島",
   ProvinceCyprus: "塞浦路斯",
   ProvinceCyrene: "昔蘭尼",
   ProvinceGalatia: "加拉太",
   ProvinceGallia: "加利亞",
   ProvinceGalliaCisalpina: "南高盧",
   ProvinceGalliaTransalpina: "北高盧",
   ProvinceHispania: "伊斯帕尼亞",
   ProvinceIllyricum: "伊利里庫姆",
   ProvinceItalia: "義大利",
   ProvinceJudia: "猶地亞",
   ProvinceLycia: "利西亞",
   ProvinceMacedonia: "馬其頓",
   ProvinceMauretania: "毛里塔尼亞",
   ProvinceNumidia: "努米底亞",
   ProvincePontus: "本都",
   ProvinceSardiniaAndCorsica: "撒丁島和科西嘉島",
   ProvinceSicillia: "西西里島",
   ProvinceSophene: "索芬妮",
   ProvinceSyria: "敘利亞",
   PublishingHouse: "出版社",
   PyramidOfGiza: "吉薩金字塔",
   PyramidOfGizaDesc: "所有產出工人的建築，獲得 +1 生產乘數",
   QinShiHuang: "秦始皇",
   Radio: "收音機",
   RadioStation: "廣播電台",
   Railway: "鐵路",
   RamessesII: "拉美西斯二世",
   RamessesIIDesc: "建造者能力乘數 +%{value}",
   RandomColorScheme: "隨機配色方案",
   RapidFire: "速射",
   ReadFullPatchNotes: "開啟網頁，閱讀更新說明",
   RebirthHistory: "重生的歷史",
   RebirthTime: "重生時間",
   Reborn: "重生",
   RebornModalDescV3: "您將開始一個新帝國，但所有本次運行中的偉人將成為永恆偉人碎片，可用於升級您的<b>永恆偉人等級</b>。依據<b>總帝國價值</b>，您還將獲得額外偉人碎片。",
   RebornOfflineWarning: "離線中。你只能在連接上伺服器時重生。",
   RebornTradeWarning: "你有發起或可領取的貿易訂單。<b>重生將刪除所有訂單。</b>考慮先取消與領取訂單。",
   RedistributeAmongSelected: "在選取當中重新分配",
   RedistributeAmongSelectedCap: "限額",
   RedistributeAmongSelectedImport: "運送速度",
   Refinery: "煉油廠",
   Reichstag: "國會大廈",
   Religion: "宗教",
   ReligionBuddhism: "佛教",
   ReligionChristianity: "基督教",
   ReligionDescHTML: "從<b>基督教、伊斯蘭教、佛教與多神教</b>中選擇一個，作為帝國宗教。你將<b>不能更換宗教</b>。不同宗教將能解鎖不同增益效果",
   ReligionIslam: "伊斯蘭教",
   ReligionPolytheism: "多神教",
   Renaissance: "文藝復興",
   RenaissanceAge: "文藝復興時期",
   ReneDescartes: "勒內·笛卡爾",
   RequiredDeposit: "所需沉積資源",
   RequiredWorkersTooltipV2: "生產所需的工人數量等於乘數後消耗和生產的所有資源的總和（不包括動態乘數）",
   RequirePower: "需要供電",
   RequirePowerDesc: "此建築需要建在有電力的地塊上，並且可以將電力延伸到其相鄰的地塊",
   Research: "研究",
   ResearchFund: "研究基金",
   ResearchLab: "研究實驗室",
   ResearchMenu: "研究",
   ResourceAmount: "數量",
   ResourceBar: "資源欄",
   ResourceBarExcludeStorageFullHTML: "將<b>儲存空間已滿<b>的建築從<b>未生產建築<b>中排除",
   ResourceBarExcludeTurnedOffOrNoActiveTransportHTML: "將<b>關閉<b>的建築從<b>未生產建築<b>中排除",
   ResourceBarShowUncappedHappiness: "顯示幸福上限(超過50)",
   ResourceCloneTooltip: "生產乘數僅適用於克隆資源（即額外副本）",
   ResourceColor: "資源色彩",
   ResourceExportBelowCap: "低於限額仍運輸",
   ResourceExportBelowCapTooltip: "即使該建築的資源量低於限額，也允許其他建築從該建築運輸資源",
   ResourceExportToSameType: "運輸到同類型",
   ResourceExportToSameTypeTooltip: "允許同類型其他建築從該建築運輸資源",
   ResourceFromBuilding: "%{resource} 來自 %{building}",
   ResourceImport: "資源運送",
   ResourceImportCapacity: "資源運輸能力",
   ResourceImportImportCapV2: "最大數量",
   ResourceImportImportCapV2Tooltip: "當達到最大數量時，此建築將停止運輸此資源",
   ResourceImportImportPerCycleV2: "每週期",
   ResourceImportImportPerCycleV2ToolTip: "每週期運輸的此資源的數量",
   ResourceImportPartialWarningHTML: "總資源運輸能力超過了最大運輸能力：<b>每個資源運輸在每個周期內將僅部分運輸</b>",
   ResourceImportResource: "資源",
   ResourceImportSettings: "運送資源： %{res}",
   ResourceImportStorage: "現存量",
   ResourceNeeded: "需要額外的 %{resource} x%{amount}",
   ResourceTransportPreference: "運送偏好",
   RevealDeposit: "顯現",
   Revolution: "革命",
   RhineGorge: "萊茵峽谷",
   RhineGorgeDesc: "2 格地塊範圍內的每個奇觀提供 +2 幸福感",
   RichardFeynman: "理查·費曼",
   RichardFeynmanDesc: "+%{value} 來自所有工人的科學，如果超過 50% 的工人忙碌且少於 50% 的忙碌工人從事交通運輸工作",
   RichardJordanGatling: "理查·格林",
   Rifle: "步槍",
   RifleFactory: "步槍工廠",
   Rifling: "膛線",
   Rijksmuseum: "國家博物館",
   RijksmuseumDesc: "+5 幸福感。所有消耗或產出文化的建築，獲得 +1 生產、儲存以及工人能力乘數",
   RoadAndWheel: "土木工程學",
   RobertNoyce: "勞勃·諾伊斯",
   Robocar: "無人車(Robocar)",
   RobocarFactory: "無人車工廠(Robocar Factory)",
   Robotics: "機器人技術",
   RockefellerCenterChristmasTree: "洛克菲勒中心聖誕樹",
   RockefellerCenterChristmasTreeDesc: "每解鎖一個時代獲得 +3 幸福感。此自然奇觀僅可於十二月被發現",
   Rocket: "火箭",
   RocketFactory: "火箭工廠",
   Rocketry: "火箭學",
   Roman: "羅馬",
   RomanForum: "古羅馬議院廣場",
   RudolfDiesel: "魯道夫·狄塞爾",
   Rurik: "留里克",
   RurikDesc: "幸福感 +%{value}",
   SagradaFamilia: "聖家堂",
   SagradaFamiliaDesc: "鄰近 2 格建築 +N 生產、工人和儲存乘數。 N = 相鄰建築的最大階級差",
   SaintBasilsCathedral: "瓦西里升天教堂",
   SaintBasilsCathedralDescV2: "與資源地塊相鄰的採集建築可進行生產。相鄰的 I 階建築生產、儲存、工人能力乘數增加 +1",
   Saladin: "薩拉丁",
   Samsuiluna: "薩姆蘇·伊路那",
   Sand: "沙",
   Sandpit: "採沙坑",
   SantaClausVillage: "聖誕老人村",
   SantaClausVillageDesc: "建成時，誕生一位當前時代的偉人。此奇觀可升級，每次升級額外提供一位偉人。選擇此奇觀的偉人時，提供 4 個選擇。此奇觀僅在 12 月可建造",
   SargonOfAkkad: "薩爾貢大帝",
   Satellite: "衛星",
   SatelliteFactory: "衛星工廠",
   SatoshiNakamoto: "中本聰(Satoshi Nakamoto)",
   Saturnalia: "農神節：阿爾卑斯山不再增加消耗倍數",
   SaveAndExit: "儲存並離開",
   School: "學校",
   Science: "科學",
   ScienceFromBusyWorkers: "忙碌工人產出科學",
   ScienceFromIdleWorkers: "閒置工人產出科學",
   SciencePerBusyWorker: "每個忙碌工人",
   SciencePerIdleWorker: "每個閒置工人",
   ScrollSensitivity: "滾動靈敏度",
   ScrollSensitivityDescHTML: "調整滾動滑鼠滾輪靈敏度。<b>值位於 0.01 與 100 間，預設值為 1 </b>",
   ScrollWheelAdjustLevelTooltip: "你可以使用滑鼠滾輪調整等級",
   SeaTradeCost: "海上貿易成本",
   SeaTradeUpgrade: "從海上航行與玩家進行貿易。每個海洋地塊的關稅：%{tariff}",
   SelectCivilization: "選擇文明",
   SelectedAll: "全選",
   SelectedCount: "已選擇 %{count} 項",
   Semiconductor: "半導體",
   SemiconductorFab: "半導體製造廠",
   SendExplorer: "派出探險家",
   SergeiKorolev: "謝爾蓋·科羅廖夫",
   SetAsDefault: "設為預設",
   SetAsDefaultBuilding: "設為預設於全部的 %{building}",
   Shamanism: "信仰",
   Shelter: "避難所",
   Shortcut: "快捷鍵",
   ShortcutBuildingPageSellBuildingV2: "拆除建築",
   ShortcutBuildingPageToggleBuilding: "切換生產",
   ShortcutBuildingPageToggleBuildingSetAllSimilar: "切換生產並套用於所有相似",
   ShortcutBuildingPageUpgrade1: "建築升級快捷鍵1 (+1)",
   ShortcutBuildingPageUpgrade2: "建築升級快捷鍵2 (+5)",
   ShortcutBuildingPageUpgrade3: "建築升級快捷鍵3 (+10)",
   ShortcutBuildingPageUpgrade4: "建築升級快捷鍵4 (+15)",
   ShortcutBuildingPageUpgrade5: "建築升級快捷鍵5 (+20)",
   ShortcutClear: "清除",
   ShortcutConflict: "你的快捷鍵與 %{name} 衝突",
   ShortcutNone: "無",
   ShortcutPressShortcut: "按下快捷鍵以設定",
   ShortcutSave: "保存",
   ShortcutScopeBuildingPage: "建築",
   ShortcutScopeConstructionPage: "建造升級頁面",
   ShortcutScopeEmptyTilePage: "空地塊",
   ShortcutScopePlayerMapPage: "貿易地圖",
   ShortcutScopeTechPage: "研究",
   ShortcutScopeUnexploredPage: "未探索科技頁面",
   ShortcutTechPageGoBackToCity: "回到城市",
   ShortcutTechPageUnlockTech: "解鎖選中科技",
   ShortcutUpgradePageCancelAllUpgrades: "取消所有升級",
   ShortcutUpgradePageCancelUpgrade: "取消升級",
   ShortcutUpgradePageDecreaseLevel: "減少預升級等級",
   ShortcutUpgradePageEndConstruction: "結束建造",
   ShortcutUpgradePageIncreaseLevel: "增加預升級等級",
   ShowTransportArrow: "顯示運輸箭頭",
   ShowTransportArrowDescHTML: "關閉此選項將隱藏運輸箭頭。這可能會<i>稍微</i>改善低端設備上的效能。效能改善會在<b>重新啟動遊戲後</b>生效",
   ShowUnbuiltOnly: "只顯示尚未建造(數量為0)的建築",
   Shrine: "神殿",
   SidePanelWidth: "側邊欄介面寬度",
   SidePanelWidthDescHTML: "更改側邊欄的寬度。<b>需要重新啟動您的遊戲才能生效</b>。",
   SiegeRam: "衝車",
   SiegeWorkshop: "衝車工廠",
   Silicon: "矽",
   SiliconSmelter: "矽冶煉廠",
   Skyscraper: "摩天大樓",
   Socialism: "社會主義",
   SocialismLevel4DescHTMLV2: "產生一次性科學點數，相當於最便宜的<b>世界大戰時代</b>技術成本",
   SocialismLevel5DescHTMLV2: "產生一次性科學點數，相當於最便宜的<b>冷戰時代</b>技術成本",
   SocialismLevelX: "社會主義等級 %{level}",
   SocialNetwork: "社群網路",
   Socrates: "蘇格拉底",
   SocratesDesc: "忙碌工人產出科學 +%{value}",
   Software: "軟體(Software)",
   SoftwareCompany: "軟體公司(Software Company)",
   Sound: "聲音",
   SoundEffect: "音效",
   SourceGreatPerson: "偉人： %{person}",
   SourceGreatPersonPermanent: "永恆偉人： %{person}",
   SourceIdeology: "政治意識形態: %{ideology}",
   SourceReligion: "宗教：%{religion}",
   SourceResearch: "已研究： %{tech}",
   SourceTradition: "傳統(Tradition): %{tradition}",
   SpaceCenter: "太空中心",
   Spacecraft: "太空船",
   SpacecraftFactory: "太空船工廠",
   SpaceNeedle: "太空針塔",
   SpaceNeedleDesc: "每建造一個奇蹟 +1 幸福",
   SpaceProgram: "太空計劃",
   Sports: "體育運動",
   Stable: "馬廄",
   Stadium: "體育場",
   StartFestival: "節慶狂歡！",
   Stateship: "村鎮",
   StatisticsBuildings: "建築",
   StatisticsBuildingsSearchText: "輸入建築名稱搜尋",
   StatisticsEmpire: "帝國",
   StatisticsExploration: "探索",
   StatisticsOffice: "統計局",
   StatisticsOfficeDesc: "提供您帝國的統計數據。生成探險家用以探索地圖",
   StatisticsResources: "資源",
   StatisticsResourcesDeficit: "赤字",
   StatisticsResourcesDeficitDesc: "生產：%{output} - 消耗：%{input}",
   StatisticsResourcesRunOut: "離耗盡",
   StatisticsResourcesSearchText: "輸入資源名稱搜尋",
   StatisticsScience: "科學",
   StatisticsScienceFromBuildings: "建築產出科學",
   StatisticsScienceFromWorkers: "工人產出科學",
   StatisticsScienceProduction: "產出科學",
   StatisticsStalledTransportation: "運輸停滯",
   StatisticsTotalTransportation: "總運輸量",
   StatisticsTransportation: "運輸",
   StatisticsTransportationPercentage: "運輸工人所占百分比",
   StatueOfLiberty: "自由女神像",
   StatueOfLibertyDesc: "所有相鄰建築，獲得 +N 生產、儲存以及工人能力乘數。 N = 相鄰同類型建築數量",
   StatueOfZeus: "宙斯神像",
   StatueOfZeusDesc: "在相鄰的空地塊隨機產生並顯現沉積資源。所有相鄰 I 階建築 +5 生產以及儲存乘數",
   SteamAchievement: "Steam 成就",
   SteamAchievementDetails: "查看 Steam 成就",
   SteamEngine: "蒸汽機",
   Steamworks: "蒸汽機廠",
   Steel: "鋼",
   SteelMill: "煉鋼廠",
   StephenHawking: "史蒂芬·霍金",
   Stock: "股票",
   StockExchange: "證券交易所",
   StockMarket: "股市",
   StockpileDesc: "這座建築將在每個生產週期運送 %{capacity}x 所需資源，直到最大庫存",
   StockpileMax: "最大庫存",
   StockpileMaxDesc: "一旦這座建築有能滿足其進行 %{cycle} 個生產週期所需的全部資源，停止運送資源",
   StockpileMaxUnlimited: "無限制",
   StockpileMaxUnlimitedDesc: "直到儲存空間填滿前，建築將持續運送資源",
   StockpileSettings: "庫存輸入係數",
   Stone: "石頭",
   StoneAge: "石器時代",
   Stonehenge: "巨石陣",
   StonehengeDesc: "所有消耗或產出石頭的建築，獲得 +1 生產乘數",
   StoneQuarry: "採石場",
   StoneTool: "石器",
   StoneTools: "岩石",
   Storage: "儲存空間",
   StorageBaseCapacity: "基礎儲存能力",
   StorageMultiplier: "儲存乘數",
   StorageUsed: "已用儲存空間",
   StPetersBasilica: "聖彼得大教堂",
   StPetersBasilicaDescV2: "所有教堂獲得 +5 儲存乘數。其產生的科學，基於所有教堂的信仰總產出",
   Submarine: "潛水艇",
   SubmarineYard: "潛水艇工廠",
   SuleimanI: "蘇萊曼一世",
   SummerPalace: "頤和園",
   SummerPalaceDesc: "所有消耗或產出火藥的相鄰建築，免除 -1 幸福感。所有消耗或產出火藥的建築，獲得 +1 生產、儲存以及工人能力乘數",
   Supercomputer: "超級電腦(Supercomputer)",
   SupercomputerLab: "超級電腦實驗室(Supercomputer Lab)",
   SupporterPackRequired: "需要支持者包",
   SupporterThankYou: "由於以下支持者包所有者的慷慨解囊，放置文明得以維持下去",
   SwissBank: "瑞士銀行",
   SwissBankDescV2: "將選定資源從相鄰倉庫中轉換為💵瑞士貨幣——價值 10M 的可交易資源貨幣。每個週期基礎產出1個💵瑞士貨幣（可被生產乘數影響），該奇觀可被升級，並且每一次額外升級增加 1 基礎產出（無論基礎產出提升還是生產乘數提升都只是提升每週期等價兌換最大總數量）。瑞士銀行可以存儲無限量的💵瑞士貨幣",
   Sword: "劍",
   SwordForge: "鑄劍廠",
   SydneyOperaHouse: "雪梨歌劇院",
   SydneyOperaHouseDescV2: "雪梨歌劇院",
   SyncToANewDevice: "同步到新裝置",
   Synthetics: "合成材料",
   TajMahal: "泰姬瑪哈陵",
   TajMahalDescV2: "建造完成時，誕生古典和中世紀各一位偉人。 升級 20 級以上的建築時 +5 建造者能力乘數",
   TangOfShang: "商湯",
   TangOfShangDesc: "閒置工人產出科學 +%{value}",
   Tank: "坦克",
   TankFactory: "坦克工廠",
   TechAge: "時代",
   TechGlobalMultiplier: "乘數",
   TechHasBeenUnlocked: "已解鎖 %{tech} ",
   TechProductionPriority: "解鎖建築優先等級(允許對每座建築設定生產優先等級)",
   TechResourceTransportPreference: "解鎖建築運送偏好(允許設定建築如何運送所需資源)。",
   TechResourceTransportPreferenceAmount: "數量",
   TechResourceTransportPreferenceAmountTooltip: "這座建築將優先運送來自儲存量更大的建築的資源。",
   TechResourceTransportPreferenceDefault: "預設",
   TechResourceTransportPreferenceDefaultTooltip: "不要覆蓋此資源的運送偏好，將使用建築的運送偏好。",
   TechResourceTransportPreferenceDistance: "距離",
   TechResourceTransportPreferenceDistanceTooltip: "這座建築將從距離較近的建築的運輸資源。",
   TechResourceTransportPreferenceOverrideTooltip: "這個資源已經覆蓋運送偏好。: %{mode}",
   TechResourceTransportPreferenceStorage: "儲存",
   TechResourceTransportPreferenceStorageTooltip: "這座建築會優先選擇從儲存使用百分比較高的建築運送資源。",
   TechStockpileMode: "解鎖庫存模式(允許調整每座建築庫存模式)",
   Teleport: "傳送",
   TeleportDescHTML: "<b>每 %{time} 秒</b>產生一次傳送者。傳送者可用於<b>移動建築(奇蹟除外)</b>一次",
   Television: "電視",
   TempleOfArtemis: "阿耳忒彌斯神殿",
   TempleOfArtemisDesc: "當完成建造時，所有鑄劍廠與軍火庫，獲得 +5 等級。所有鑄劍廠與軍火庫，獲得 +1 生產乘數、工人能力乘數以及儲存乘數",
   TempleOfHeaven: "天壇",
   TempleOfHeavenDesc: "所有等級 10 或更高的建築 +1 工人能力乘數",
   TempleOfPtah: "卜塔神殿",
   TerracottaArmy: "兵馬俑",
   TerracottaArmyDesc: "所有鐵礦 +1 生產乘數、工人能力乘數以及儲存乘數。與鐵鍛鋪相鄰的每個含鐵資源地塊的鐵礦，使鐵鍛鋪獲得 +1 生產乘數",
   Thanksgiving: "感恩節：華爾街對建築物的提升效果加倍，並適用於共同基金、對沖基金和比特幣礦工。研究基金獲得 +5 生產倍數",
   Theater: "劇院",
   Theme: "主題",
   ThemeColor: "主題色彩",
   ThemeColorResearchBackground: "研究背景",
   ThemeColorReset: "重置為預設",
   ThemeColorResetBuildingColors: "重置建築色彩",
   ThemeColorResetResourceColors: "重置資源色彩",
   ThemeInactiveBuildingAlpha: "未運轉建築亮度",
   ThemePremiumTile: "此貼圖僅購買了支持者包的玩家使用",
   ThemeResearchHighlightColor: "研究高亮色彩",
   ThemeResearchLockedColor: "未解鎖研究色彩",
   ThemeResearchUnlockedColor: "已解鎖研究色彩",
   ThemeTransportIndicatorAlpha: "運送指示亮度",
   Theocracy: "神權",
   TheoreticalData: "理論數據",
   ThePentagon: "五角大廈",
   ThePentagonDesc: "建造完成後，產生可用於移動建築的傳送者。 2 格範圍內的所有建築 +1 生產、工人容量和儲存乘數",
   TheWhiteHouse: "白宮",
   ThomasEdison: "湯瑪斯·愛迪生",
   ThomasGresham: "托馬斯·格雷沙姆",
   Tile: "地塊",
   TileBonusRefreshIn: "貿易地塊加成將於 <b>%{time}</b> 後刷新",
   TimBernersLee: "提姆·柏內茲-李(Tim Berners-Lee)",
   TimeWarp: "時間扭曲倍數",
   TimeWarpWarning: "以超出電腦處理能力的速度加速，可能會導致數據丟失。<b>使用風險自負！</b>",
   ToggleWonderEffect: "切換奇蹟效果(Toggle Wonder Effect)",
   Tool: "工具",
   TopkapiPalace: "托普卡帕宮",
   TopkapiPalaceDesc: "周圍 2 地塊範圍內的所有建築，獲得 +X 存儲乘數。X = 被加成建築的 50% 生產乘數（不包括動態乘數）",
   TotalEmpireValue: "帝國總價值",
   TotalEmpireValuePerCycle: "每個周期的總帝國價值",
   TotalEmpireValuePerCyclePerGreatPeopleLevel: "帝國總價值/永恆偉人",
   TotalEmpireValuePerWallSecond: "每秒實際時間的總帝國價值",
   TotalEmpireValuePerWallSecondPerGreatPeopleLevel: "每個偉人等級的每秒實際時間總帝國價值",
   TotalGameTimeThisRun: "本次運行的總遊戲時間",
   TotalScienceRequired: "所需科學點數",
   TotalStorage: "總儲存空間",
   TotalWallTimeThisRun: "本次運行的總實際時間",
   TotalWallTimeThisRunTooltip: "實際時間（又稱牆上時間）測量本次運行所花費的實際時間。與遊戲時間不同，佩特拉古城的時間扭曲和離線生產不影響實際時間，但會影響遊戲時間。",
   TotalWorkers: "工人數",
   TowerBridge: "倫敦塔橋",
   TowerBridgeDesc: "建造完成後，每 3600 個周期（遊戲時間為 1 小時）會誕生一位已解鎖時代的此生偉人",
   TowerOfBabel: "巴別塔(Tower of Babel)",
   TowerOfBabelDesc: "為所有至少有一座工作建築毗鄰奇蹟的建築提供 +2 生產乘數",
   TradeFillSound: "交易填充音效",
   TradeValue: "貿易價值",
   TraditionCommerce: "商業",
   TraditionCultivation: "耕作",
   TraditionDescHTML: "從<b>耕作、商業、擴張和榮譽</b>中選擇作為您的帝國傳統。選擇傳統後，您<b>無法切換傳統</b>。您可以在每個傳統中解鎖更多提升",
   TraditionExpansion: "擴張",
   TraditionHonor: "榮譽",
   Train: "火車",
   TranslationPercentage: "%{language}的翻譯進度目前為%{percentage}。可在GitHub上幫助改進此語言翻譯。（可參考簡中相關建議。）",
   TranslatorCredit: "HarchuN、JiaMuC(沐)、WangChenan、Neca、Captain  （企鹅群907371958）",
   Translators: "翻譯人員",
   TransportAllocatedCapacityTooltip: "建造者容量分配用於運送這種資源",
   TransportationWorkers: "運輸工人",
   TransportCapacity: "運送能力",
   TransportCapacityMultiplier: "運送能力乘數",
   TransportManualControlTooltip: "運送這種資源用於建造/升級。",
   TransportPlanCache: "運輸計劃快取",
   TransportPlanCacheDescHTML: "每個週期中，每個建築物都會根據其設置計算最佳運輸計劃——此過程需要高 CPU 功率。啟用此功能將嘗試快取運輸計劃的結果（如果其仍然有效），從而降低 CPU 使用率和幀率下降。<b>實驗性功能</b>",
   TribuneUpgradeDescGreatPeopleWarning: "你此輪擁有偉人。你應該<b>先重生</b>。升至財務官級別，將重置你的當前輪。",
   TribuneUpgradeDescGreatPeopleWarningTitle: "請先重生",
   TribuneUpgradeDescV4:
      "如果您不打算參與<b>可選的</b>在線功能，可以作為軍官完整遊玩遊戲。若要獲得對在線功能的不受限制的訪問，需升級到財務官。<b>這是一種反機器人措施，旨在讓所有人免費遊玩遊戲。</b>然而，<b>升級到財務官時</b>，您可以保留偉人等級：<ul><li>青銅、鐵器和古典時代最高<b>3</b>級</li><li>中世紀、文藝復興和工業時代最高<b>2</b>級</li><li>世界大戰、冷戰和資訊時代最高<b>1</b>級</li></ul>超過等級的偉人碎片和<b>時代智慧</b>等級<b>無法</b>被保留",
   TurnOffFullBuildings: "關閉所有 %{building} 儲存已滿的建築",
   TurnOnTimeWarpDesc: "每秒消耗 %{speed} 扭曲時間，同時加速你的帝國，使其以 %{speed} 倍速度運行。",
   Tutorial: "新手教學",
   TutorialPlayerFlag: "選擇你的玩家旗幟",
   TutorialPlayerHandle: "輸入你的玩家名稱（請勿使用不友善名稱）",
   TV: "電視",
   TVStation: "電視台",
   UnclaimedGreatPersonPermanent: "你有未領取的<b>永恆偉人</b>，點擊這裡以領取。",
   UnclaimedGreatPersonThisRun: "你有未領取的<b>此生偉人</b>，點擊這裡以領取。",
   UnexploredTile: "未探索地塊",
   UNGeneralAssemblyCurrent: "本屆聯合國大會#%{id}",
   UNGeneralAssemblyMultipliers: "<b>+%{count}</b> 生產、工人和儲存能力乘數對於 <b>%{buildings}</b>",
   UNGeneralAssemblyNext: "即將召開的聯合國大會 #%{id}",
   UNGeneralAssemblyVoteEndIn: "您可以在<b>%{time}</b>投票結束之前隨時更改投票",
   UniqueBuildings: "獨特建築",
   UniqueTechMultipliers: "獨特技術乘數",
   UnitedNations: "聯合國",
   UnitedNationsDesc: "所有 IV、V 和 VI 階級建築，生產、工人和儲存能力乘數 +1。 參加聯合國大會並每週投票支持額外獎勵",
   University: "大學",
   UnlockableResearch: "可解鎖研究",
   UnlockBuilding: "解鎖",
   UnlockTechProgress: "進度",
   UnlockXHTML: "解鎖 <b>%{name}</b>",
   Upgrade: "升級",
   UpgradeBuilding: "升級",
   UpgradeBuildingNotProducingDescV2: "這個建築正在升級 - <b>直到升級完成，生產將會停止</b>",
   UpgradeTo: "升級到%{level}級",
   Uranium: "鈾",
   UraniumEnrichmentPlant: "鈾濃縮廠",
   UraniumMine: "鈾礦",
   Urbanization: "城市化",
   UserAgent: "使用者代理： %{driver}",
   View: "查看",
   ViewMenu: "場景",
   ViewTechnology: "查看",
   Vineyard: "葡萄園",
   VirtualReality: "虛擬實境",
   Voltaire: "伏爾泰",
   WallOfBabylon: "巴比倫城牆(Wall of Babylon)",
   WallOfBabylonDesc: "所有建築物都獲得+N儲存倍增。 N = 解鎖時代數/2",
   WallStreet: "華爾街",
   WallStreetDesc: "所有在 2 格範圍內生產硬幣、紙幣、債券、股票和外匯的建築都獲得 +N 生產乘數。 N = 1 到 5 之間的隨機值，每個建築都不同，並且隨著每次市場刷新而變化。約翰·D·洛克菲勒的影響加倍",
   WaltDisney: "華特·迪士尼",
   Warehouse: "倉庫",
   WarehouseAutopilotSettings: "自動運輸設定",
   WarehouseAutopilotSettingsEnable: "啟用自動運輸",
   WarehouseAutopilotSettingsRespectCapSetting: "需要儲存量 < 上限",
   WarehouseAutopilotSettingsRespectCapSettingTooltip: "自動運輸只會運輸儲存量低於上限的資源",
   WarehouseDesc: "運送特定資源並提供額外儲存空間",
   WarehouseExtension: "解鎖倉庫商隊擴充模式。允許商隊附近的倉庫包含在玩家交易中",
   WarehouseSettingsAutopilotDesc: "這座倉庫將會使用它的閒置能力，從無儲存空間的建築中運送資源。當前閒置能力： %{capacity}",
   WarehouseUpgrade: "解鎖倉庫自動模式。倉庫與其相鄰建築之間免費運輸。",
   WarehouseUpgradeDesc: "這座倉庫與其相鄰建築之間免費運輸",
   Warp: "扭曲時間",
   WarpSpeed: "扭曲時間倍速",
   Water: "水",
   WellStockedTooltip: "庫存充足的建築是指擁有足夠生產資源的建築，包括正在生產的建築、儲存已滿的建築或因缺乏工人而無法生產的建築",
   WernherVonBraun: "華納·馮·布朗",
   Westminster: "威斯敏斯特",
   Wheat: "小麥",
   WheatFarm: "小麥農場",
   WildCardGreatPersonDescV2: "成為同時代的任一位偉人",
   WilliamShakespeare: "威廉·莎士比亞",
   Wine: "葡萄酒",
   Winery: "葡萄酒廠",
   WishlistSpaceshipIdle: "閒置太空飛船願望清單",
   Wonder: "奇觀",
   WonderBuilderCapacityDescHTML: "建造奇蹟時的<b>建造者容量</b>受到解鎖奇蹟的<b>時代</b>和<b>科技</b>的影響。",
   WondersBuilt: "已建造的世界奇蹟",
   WondersUnlocked: "世界奇觀已解鎖",
   WonderUpgradeLevel: "奇蹟等級(Wonder Level)",
   Wood: "原木",
   Worker: "工人",
   WorkerCapacityMultiplier: "工人能力乘數",
   WorkerHappinessPercentage: "幸福乘數",
   WorkerMultiplier: "工人能力",
   WorkerPercentagePerHappiness: "每點幸福感提供 %{value}% 乘數",
   Workers: "工人",
   WorkersAvailableAfterHappinessMultiplier: "可獲工人(幸福乘數後)",
   WorkersAvailableBeforeHappinessMultiplier: "可獲工人(幸福乘數前)",
   WorkersBusy: "忙碌工人",
   WorkerScienceProduction: "工人產出科學",
   WorkersRequiredAfterMultiplier: "需求工人",
   WorkersRequiredBeforeMultiplier: "需求勞動力",
   WorkersRequiredForProductionMultiplier: "單位工人生產能力",
   WorkersRequiredForTransportationMultiplier: "單位工人運輸能力",
   WorkersRequiredInput: "運輸",
   WorkersRequiredOutput: "生產",
   WorldWarAge: "世界大戰",
   WorldWideWeb: "全球資訊網",
   WritersGuild: "作家公會",
   Writing: "文字",
   WuZetian: "武則天",
   WuZetianDesc: "運送能力乘數 +%{value}",
   Xuanzang: "玄奘",
   YangtzeRiver: "長江",
   YangtzeRiverDesc: "所有消耗水的建築獲得 +1 生產、工人能力和儲存乘數。鄭和(偉人)效果加倍。武則天(偉人)每升一級為所有建築提供+1儲存倍數。與黃鶴樓(奇觀)具搭配效果",
   YearOfTheSnake: "巳蛇",
   YearOfTheSnakeDesc: "建成後，當進入一個新時代時，將原本每個已解鎖時代獲得一位偉人，改為獲得相同數量的當代偉人。周圍 2 格範圍內的所有建築獲得 +1 的生產加成倍數。此世界奇觀可升級，每次額外升級會為周圍 2 格範圍內的所有建築再提供 +1 的生產加成倍數。此奇觀僅能在農曆新年期間（1 月 20 日至 2 月 10 日）建造。",
   YellowCraneTower: "黃鶴樓",
   YellowCraneTowerDesc: "選擇偉人時 +1 選擇。 1 格範圍內的所有建築能獲得 +1 生產乘數、工人容量和儲存乘數。若建造在長江旁，範圍增加到 2 格",
   YuriGagarin: "尤里·加加林",
   ZagrosMountains: "札格羅斯山脈(Zagros Mountains)",
   ZagrosMountainsDesc: "所有生產乘數低於 5 的鄰近建築物都獲得 +2 生產乘數。尼布甲尼撒二世（偉人）效果加倍",
   ZahaHadid: "札哈·哈蒂",
   ZahaHadidDesc: "+%{value} 建造者能力乘數",
   Zenobia: "澤諾比亞",
   ZenobiaDesc: "+%{value}小時佩特拉古城扭曲時間儲存上限",
   ZhengHe: "鄭和",
   ZigguratOfUr: "烏爾金字形神塔(Ziggurat of Ur)",
   ZigguratOfUrDescV2: "每 10 點幸福感（上限）為所有不生產工人的建築提供 +1 生產乘數，前提是該建築在以前的時代已解鎖（最大值 = 解鎖的時代數 / 2）。奇觀（包括自然奇觀）不再提供 +1 快樂度。該效果可被關閉",
   Zoroaster: "瑣羅亞斯德(查拉圖斯特拉)",
   Zugspitze: "祖格峰",
   ZugspitzeDesc: "每解鎖一個時代，獲得一個點數，可用於為本次運行中誕生的任何偉人提供一個額外等級",
};
