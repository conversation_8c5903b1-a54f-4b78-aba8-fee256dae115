import classNames from "classnames";
import type React from "react";
import { useTransition } from "react";
import { notifyGameOptionsUpdate } from "../../../shared/logic/GameStateLogic";
import { ChatChannels } from "../../../shared/utilities/Database";
import { L, t } from "../../../shared/utilities/i18n";
import { useGameOptions } from "../Global";
import { jsxMapOf } from "../utilities/Helper";
import { playError } from "../visuals/Sound";

export function SelectChatChannelModal({
   show,
   style,
   onClose,
}: {
   show: boolean;
   onClose: () => void;
   style?: React.CSSProperties;
}): React.ReactNode {
   const options = useGameOptions();
   const [isPending, startTransition] = useTransition();
   if (!show) {
      return null;
   }
   return (
      <div className="window" style={style}>
         <div className="title-bar">
            <div className="title-bar-text">{t(<PERSON><PERSON>)}</div>
            <div className="title-bar-controls">
               <button aria-label="Close" onClick={() => onClose()}></button>
            </div>
         </div>
         <div className="window-body">
            <div className="table-view">
               <table>
                  <tbody>
                     <tr>
                        <th>{t(L.ChatChannelLanguage)}</th>
                        <th style={{ width: 0 }}></th>
                     </tr>
                     {jsxMapOf(ChatChannels, (channel, value) => {
                        return (
                           <tr key={channel}>
                              <td className="f1">{value}</td>
                              <td>
                                 <div
                                    className={classNames({
                                       "m-icon pointer": true,
                                       "text-desc": !options.chatChannels.has(channel),
                                       "text-green": options.chatChannels.has(channel),
                                    })}
                                    onClick={() => {
                                       if (options.chatChannels.has(channel)) {
                                          if (options.chatChannels.size <= 1) {
                                             playError();
                                             return;
                                          }
                                          options.chatChannels.delete(channel);
                                       } else {
                                          options.chatChannels.add(channel);
                                       }
                                       startTransition(() => notifyGameOptionsUpdate(options));
                                    }}
                                 >
                                    {options.chatChannels.has(channel) ? "toggle_on" : "toggle_off"}
                                 </div>
                              </td>
                           </tr>
                        );
                     })}
                  </tbody>
               </table>
            </div>
         </div>
      </div>
   );
}
