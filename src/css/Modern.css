@font-face {
   font-family: "Inter Tight";
   font-style: normal;
   font-weight: 400;
   src: url(../fonts/InterTight-Medium.ttf) format("woff2");
}

@font-face {
   font-family: "Inter Tight";
   font-style: normal;
   font-weight: 700;
   src: url(../fonts/InterTight-Bold.ttf) format("woff2");
}

@font-face {
   font-family: "Inter Tight";
   font-style: italic;
   font-weight: 700;
   src: url(../fonts/InterTight-BoldItalic.ttf) format("woff2");
}

@font-face {
   font-family: "Inter Tight";
   font-style: italic;
   font-weight: 400;
   src: url(../fonts/InterTight-MediumItalic.ttf) format("woff2");
}

.modern,
.modern .window,
.modern button,
.modern input,
.modern label,
.modern textarea,
.modern select,
.modern option,
.modern .toast,
.modern ul.tree-view {
   font-family: "Inter Tight", sans-serif;
   font-size: 1.4rem;
}

.modern .tippy-box {
   font-family: "Inter Tight", sans-serif;
   font-size: 1.4rem;
}

.modern ul.tree-view details > summary:before {
   width: 14px;
   height: 15px;
   line-height: 14px;
}

.modern ul.tree-view ul {
   margin-left: 23px;
}

.modern ul.tree-view ul > li:before {
   top: 8px;
}

.modern .menu-button .menu-popover {
   top: 2.5rem;
}

.modern ul.tree-view ul > li:after {
   top: 9px;
}

.modern input[type="text"],
.modern input[type="email"],
.modern input[type="password"] {
   height: 25px;
}

.modern select {
   background-size: 19px 19px;
   height: 23px;
   padding-left: 5px;
}

.modern select.condensed {
   background-size: 17px 17px;
   height: 21px;
   padding-left: 5px;
   font-size: 1.2rem;
}

.modern .title-bar {
   font-size: 1.4rem;
}

.modern .text-small {
   font-size: 90%;
}

.modern .title-bar button {
   min-width: 16px;
   min-height: 14px;
}

.modern button {
   min-height: 25px;
}

.modern .separator.has-title > div {
   top: -10px;
}

.modern .m-icon.small {
   font-size: 1.7rem;
}

.modern #chat-panel .language-switch {
   height: 25px;
   width: 25px;
   line-height: 25px;
}

.modern .player-level {
   height: 17px;
}
