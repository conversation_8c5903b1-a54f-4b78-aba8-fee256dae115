#chat-panel {
   height: 3rem;
   position: absolute;
   bottom: 1rem;
   left: 1rem;
   display: flex;
   align-items: center;
}

#chat-panel .chat-content,
#chat-panel .stat-content {
   width: 35rem;
   height: 60vh;
   position: absolute;
   bottom: 3rem;
   display: flex;
   flex-direction: column;
   left: 0;
}

#chat-panel .stat-content {
   width: 40rem;
}

#chat-panel .chat-content .window-content {
   overflow: auto;
   padding: 0;
}

#chat-panel .chat-message {
   white-space: nowrap;
   overflow: hidden;
   text-overflow: ellipsis;
   flex: 1;
   width: 320px;
   padding: 0 5px 0 0;
}

#chat-panel .chat-message-item {
   padding: 6px 8px;
}

#chat-panel .chat-message-content {
   user-select: text;
   user-select: contain;
   cursor: text;
   overflow-wrap: break-word;
}

#chat-panel .chat-message-item .chat-image {
   width: 100%;
   max-height: 200px;
   object-fit: cover;
   display: block;
   margin: 5px 0;
   border-radius: 5px;
}

#chat-panel .chat-message-item.is-even {
   background: #efefef;
}

#chat-panel .chat-message-item.mentions-me {
   background: #ffc;
   font-weight: bold;
}

#chat-panel .chat-command-item {
   padding: 6px 8px;
   user-select: text;
   user-select: contain;
   cursor: text;
   overflow-wrap: break-word;
   background-color: #333;
   color: #fff;
   font-family: "Roboto Mono", monospace;
   font-weight: 500;
   font-size: 1.3rem;
}

#chat-panel .chat-command-item code {
   white-space: pre;
}

#chat-panel .language-switch {
   background: #00289e;
   color: #fff;
   height: 19px;
   width: 19px;
   line-height: 19px;
   text-align: center;
   margin: 0 2px 0 0;
}

#chat-panel .chat-channel {
   background: #999;
   color: #fff;
   text-transform: uppercase;
   padding: 0 2px;
   margin: 0 0 0 2px;
   border-radius: 2px;
}

#chat-panel input.is-chat-command {
   font-family: "Roboto Mono", monospace;
   font-weight: 500;
   color: #fff;
   background: #333;
}
