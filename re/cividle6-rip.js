!function isNullOrUndefined(e){return null==e}(e.search)){if(l)r.hostname=r.host=f.shift(),(g=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=g.shift(),r.host=r.hostname=g.shift());return r.search=e.search,r.query=e.query,isNull(r.pathname)&&isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.href=r.format(),r}if(!f.length)return r.pathname=null,r.search?r.path="/"+r.search:r.path=null,r.href=r.format(),r;for(var h=f.slice(-1)[0],m=(r.host||e.host)&&("."===h||".."===h)||""===h,w=0,b=f.length;b>=0;b--)"."==(h=f[b])?f.splice(b,1):".."===h?(f.splice(b,1),w++):w&&(f.splice(b,1),w--);if(!u&&!c)for(;w--;w)f.unshift("..");!u||""===f[0]||f[0]&&"/"===f[0].charAt(0)||f.unshift(""),m&&"/"!==f.join("/").substr(-1)&&f.push("");var g,y=""===f[0]||f[0]&&"/"===f[0].charAt(0);l&&(r.hostname=r.host=y?"":f.length?f.shift():"",(g=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=g.shift(),r.host=r.hostname=g.shift()));return(u=u||r.host&&f.length)&&!y&&f.unshift(""),f.length?r.pathname=f.join("/"):(r.pathname=null,r.path=null),isNull(r.pathname)&&isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.auth=e.auth||r.auth,r.slashes=r.slashes||e.slashes,r.href=r.format(),r},Url.prototype.parseHost=function(){var e=this.host,t=o.exec(e);t&&(":"!==(t=t[0])&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)}}},t={};function __webpack_require__(r){var n=t[r];if(void 0!==n)return n.exports;var i=t[r]={id:r,loaded:!1,exports:{}};return e[r].call(i.exports,i,i.exports,__webpack_require__),i.loaded=!0,i.exports}__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),__webpack_require__.nmd=e=>(e.paths=[],e.children||(e.children=[]),e);var r={};(()=>{"use strict";var e=r,t=__webpack_require__("./node_modules/process/browser.js"),n=__webpack_require__("./node_modules/buffer/index.js").Buffer;Object.defineProperty(e,"__esModule",{value:!0});const i=__webpack_require__("./node_modules/events/events.js"),{EventEmitter:o}=i;t._linkedBinding=binding.get;const s=t._linkedBinding("electron_common_v8_util");s.setHiddenValue(__webpack_require__.g,"Buffer",n);for(const e of Object.keys(o.prototype))Object.prototype.hasOwnProperty.call(t,e)&&delete t[e];Object.setPrototypeOf(t,o.prototype);const{ipcRendererInternal:a}=__webpack_require__("./lib/renderer/ipc-renderer-internal.ts"),u=__webpack_require__("./lib/renderer/ipc-renderer-internal-utils.ts"),{preloadScripts:c,process:f}=u.invokeSync("BROWSER_SANDBOX_LOAD"),l=__webpack_require__("./lib/sandboxed_renderer/api/exports/electron.ts"),h=new Map([["electron",l],["electron/common",l],["electron/renderer",l],["events",i]]),d=new Map([["timers",()=>__webpack_require__("./node_modules/timers-browserify/main.js")],["url",()=>__webpack_require__("./node_modules/url/url.js")]]);s.setHiddenValue(__webpack_require__.g,"lifecycle",{onLoaded(){t.emit("loaded")},onExit(){t.emit("exit")},onDocumentStart(){t.emit("document-start")},onDocumentEnd(){t.emit("document-end")}});const p=new o;function preloadRequire(e){if(h.has(e))return h.get(e);if(d.has(e)){const t=d.get(e)();return h.set(e,t),t}throw new Error(`module not found: ${e}`)}Object.assign(p,binding.process),Object.assign(p,f),Object.assign(t,binding.process),Object.assign(t,f),t.getProcessMemoryInfo=p.getProcessMemoryInfo=()=>a.invoke("BROWSER_GET_PROCESS_MEMORY_INFO"),Object.defineProperty(p,"noDeprecation",{get:()=>t.noDeprecation,set(e){t.noDeprecation=e}}),t.on("loaded",(()=>p.emit("loaded"))),t.on("exit",(()=>p.emit("exit"))),t.on("document-start",(()=>p.emit("document-start"))),t.on("document-end",(()=>p.emit("document-end")));const{hasSwitch:m}=t._linkedBinding("electron_common_command_line");function runPreloadScript(e){const t=`(function(require, process, Buffer, global, setImmediate, clearImmediate, exports) {\n  ${e}\n  })`,r=binding.createPreloadScript(t),{setImmediate:i,clearImmediate:o}=__webpack_require__("./node_modules/timers-browserify/main.js");r(preloadRequire,p,n,__webpack_require__.g,i,o,{})}m("unsafely-expose-electron-internals-for-testing")&&(p._linkedBinding=t._linkedBinding),__webpack_require__("./lib/renderer/common-init.ts");for(const{preloadPath:e,preloadSrc:t,preloadError:r}of c)try{if(t)runPreloadScript(t);else if(r)throw r}catch(t){console.error(`Unable to load preload script: ${e}`),console.error(t),a.send("BROWSER_PRELOAD_ERROR",e,t)}})()})()}(globalThis.process||binding.process).argv.includes("--profile-electron-init")?setTimeout(___electron_webpack_init__,0):___electron_webpack_init__()}catch(e){console.error("Electron sandboxed_renderer.bundle.js script failed to run"),console.error(e)}