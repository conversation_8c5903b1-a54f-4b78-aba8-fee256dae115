!function areValidTemplateItems(e){return e.every((e=>null!=e&&"object"==typeof e&&(Object.prototype.hasOwnProperty.call(e,"label")||Object.prototype.hasOwnProperty.call(e,"role")||"separator"===e.type)))}(e))throw new TypeError("Invalid template for MenuItem: must have at least one of label, role or type");const t=function removeExtraSeparators(e){let t=e.filter(((e,t,r)=>!1===e.visible||("separator"!==e.type||0===t||"separator"!==r[t-1].type)));return t=t.filter(((e,t,r)=>!1===e.visible||("separator"!==e.type||0!==t&&t!==r.length-1))),t}(sortTemplate(e)),r=new a;return t.forEach((e=>{e instanceof o.MenuItem?r.append(e):r.append(new o.MenuItem(e))})),r},e.exports=a},"./lib/browser/api/message-channel.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=r("./lib/browser/message-port-main.ts"),{createPair:n}=process._linkedBinding("electron_browser_message_port");t.default=class MessageChannelMain{constructor(){const{port1:e,port2:t}=n();this.port1=new o.MessagePortMain(e),this.port2=new o.MessagePortMain(t)}}},"./lib/browser/api/module-list.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.browserModuleList=void 0,t.browserModuleList=[{name:"app",loader:()=>r("./lib/browser/api/app.ts")},{name:"autoUpdater",loader:()=>r("./lib/browser/api/auto-updater.ts")},{name:"BaseWindow",loader:()=>r("./lib/browser/api/base-window.ts")},{name:"BrowserView",loader:()=>r("./lib/browser/api/browser-view.ts")},{name:"BrowserWindow",loader:()=>r("./lib/browser/api/browser-window.ts")},{name:"contentTracing",loader:()=>r("./lib/browser/api/content-tracing.ts")},{name:"crashReporter",loader:()=>r("./lib/browser/api/crash-reporter.ts")},{name:"dialog",loader:()=>r("./lib/browser/api/dialog.ts")},{name:"globalShortcut",loader:()=>r("./lib/browser/api/global-shortcut.ts")},{name:"ipcMain",loader:()=>r("./lib/browser/api/ipc-main.ts")},{name:"inAppPurchase",loader:()=>r("./lib/browser/api/in-app-purchase.ts")},{name:"Menu",loader:()=>r("./lib/browser/api/menu.ts")},{name:"MenuItem",loader:()=>r("./lib/browser/api/menu-item.ts")},{name:"MessageChannelMain",loader:()=>r("./lib/browser/api/message-channel.ts")},{name:"nativeTheme",loader:()=>r("./lib/browser/api/native-theme.ts")},{name:"net",loader:()=>r("./lib/browser/api/net.ts")},{name:"netLog",loader:()=>r("./lib/browser/api/net-log.ts")},{name:"Notification",loader:()=>r("./lib/browser/api/notification.ts")},{name:"powerMonitor",loader:()=>r("./lib/browser/api/power-monitor.ts")},{name:"powerSaveBlocker",loader:()=>r("./lib/browser/api/power-save-blocker.ts")},{name:"pushNotifications",loader:()=>r("./lib/browser/api/push-notifications.ts")},{name:"protocol",loader:()=>r("./lib/browser/api/protocol.ts")},{name:"safeStorage",loader:()=>r("./lib/browser/api/safe-storage.ts")},{name:"screen",loader:()=>r("./lib/browser/api/screen.ts")},{name:"session",loader:()=>r("./lib/browser/api/session.ts")},{name:"ShareMenu",loader:()=>r("./lib/browser/api/share-menu.ts")},{name:"systemPreferences",loader:()=>r("./lib/browser/api/system-preferences.ts")},{name:"TouchBar",loader:()=>r("./lib/browser/api/touch-bar.ts")},{name:"Tray",loader:()=>r("./lib/browser/api/tray.ts")},{name:"utilityProcess",loader:()=>r("./lib/browser/api/utility-process.ts")},{name:"View",loader:()=>r("./lib/browser/api/view.ts")},{name:"webContents",loader:()=>r("./lib/browser/api/web-contents.ts")},{name:"WebContentsView",loader:()=>r("./lib/browser/api/web-contents-view.ts")},{name:"webFrameMain",loader:()=>r("./lib/browser/api/web-frame-main.ts")}],t.browserModuleList.push({name:"desktopCapturer",loader:()=>r("./lib/browser/api/desktop-capturer.ts")}),t.browserModuleList.push({name:"ImageView",loader:()=>r("./lib/browser/api/views/image-view.ts")})},"./lib/browser/api/native-theme.ts":e=>{const{nativeTheme:t}=process._linkedBinding("electron_browser_native_theme");e.exports=t},"./lib/browser/api/net-log.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=r("./lib/browser/api/exports/electron.ts");t.default={startLogging:async(e,t)=>{if(o.app.isReady())return o.session.defaultSession.netLog.startLogging(e,t)},stopLogging:async()=>{if(o.app.isReady())return o.session.defaultSession.netLog.stopLogging()},get currentlyLogging(){return!!o.app.isReady()&&o.session.defaultSession.netLog.currentlyLogging}}},"./lib/browser/api/net.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.request=t.ClientRequest=void 0;const o=r("url"),n=r("stream"),s=r("./lib/browser/api/exports/electron.ts"),{isOnline:i,isValidHeaderName:a,isValidHeaderValue:l,createURLLoader:c}=process._linkedBinding("electron_browser_net"),p=new Set(["http:","https:"]),d=new Set(["content-type","content-length","user-agent","referer","host","authorization","proxy-authorization","if-modified-since","if-unmodified-since","from","location","max-forwards","retry-after","etag","last-modified","server","age","expires"]);class IncomingMessage extends n.Readable{constructor(e){super(),this._shouldPush=!1,this._data=[],this._resume=null,this._responseHead=e}get statusCode(){return this._responseHead.statusCode}get statusMessage(){return this._responseHead.statusMessage}get headers(){const e={},{rawHeaders:t}=this._responseHead;return t.forEach((t=>{const r=t.key.toLowerCase();Object.prototype.hasOwnProperty.call(e,r)&&d.has(r)||("set-cookie"===r?Object.prototype.hasOwnProperty.call(e,r)?e[r].push(t.value):e[r]=[t.value]:Object.prototype.hasOwnProperty.call(e,r)?e[r]+=`, ${t.value}`:e[r]=t.value)})),e}get rawHeaders(){const e=[],{rawHeaders:t}=this._responseHead;return t.forEach((t=>{e.push(t.key,t.value)})),e}get httpVersion(){return`${this.httpVersionMajor}.${this.httpVersionMinor}`}get httpVersionMajor(){return this._responseHead.httpVersion.major}get httpVersionMinor(){return this._responseHead.httpVersion.minor}get rawTrailers(){throw new Error("HTTP trailers are not supported")}get trailers(){throw new Error("HTTP trailers are not supported")}_storeInternalData(e,t){this._resume=t,this._data.push(e),this._pushInternalData()}_pushInternalData(){for(;this._shouldPush&&this._data.length>0;){const e=this._data.shift();this._shouldPush=this.push(e)}if(this._shouldPush&&this._resume){const e=this._resume;this._resume=null,e()}}_read(){this._shouldPush=!0,this._pushInternalData()}}class SlurpStream extends n.Writable{constructor(){super(),this._data=Buffer.alloc(0)}_write(e,t,r){this._data=Buffer.concat([this._data,e]),r()}data(){return this._data}}class ChunkedBodyStream extends n.Writable{constructor(e){super(),this._clientRequest=e}_write(e,t,r){this._downstream?this._downstream.write(e).then(r,r):(this._pendingChunk=e,this._pendingCallback=r,this._clientRequest._startRequest())}_final(e){this._downstream.done(),e()}startReading(e){if(this._downstream)throw new Error("two startReading calls???");if(this._downstream=e,this._pendingChunk){const doneWriting=e=>{if(this._clientRequest._aborted)return;const t=this._pendingCallback;delete this._pendingCallback,delete this._pendingChunk,t(e||void 0)};this._downstream.write(this._pendingChunk).then(doneWriting,doneWriting)}}}class ClientRequest extends n.Writable{constructor(e,t){if(super({autoDestroy:!0}),this._started=!1,this._firstWrite=!1,this._aborted=!1,!s.app.isReady())throw new Error("net module can only be used after app is ready");t&&this.once("response",t);const{redirectPolicy:r,...n}=function parseOptions(e){const t="string"==typeof e?o.parse(e):{...e};let r=t.url;if(!r){const e={},n=t.protocol||"http:";if(!p.has(n))throw new Error('Protocol "'+n+'" not supported');if(e.protocol=n,t.host?e.host=t.host:(t.hostname?e.hostname=t.hostname:e.hostname="localhost",t.port&&(e.port=t.port)),t.path&&/ /.test(t.path))throw new TypeError("Request path contains unescaped characters");const s=o.parse(t.path||"/");e.pathname=s.pathname,e.search=s.search,e.hash=s.hash,r=o.format(e)}const n=t.redirect||"follow";if(!["follow","error","manual"].includes(n))throw new Error("redirect mode should be one of follow, error or manual");if(null!=t.headers&&"object"!=typeof t.headers)throw new TypeError("headers must be an object");const s={method:(t.method||"GET").toUpperCase(),url:r,redirectPolicy:n,headers:{},body:null,useSessionCookies:t.useSessionCookies,credentials:t.credentials,origin:t.origin},i=t.headers||{};for(const[e,t]of Object.entries(i)){if(!a(e))throw new Error(`Invalid header name: '${e}'`);if(!l(t.toString()))throw new Error(`Invalid value for header '${e}': '${t}'`);const r=e.toLowerCase();s.headers[r]={name:e,value:t}}if(t.session){if(!t.session.constructor||"Session"!==t.session.constructor.name)throw new TypeError("`session` should be an instance of the Session class");s.session=t.session}else if(t.partition){if("string"!=typeof t.partition)throw new TypeError("`partition` should be a string");s.partition=t.partition}return s}(e);this._urlLoaderOptions=n,this._redirectPolicy=r}get chunkedEncoding(){return this._chunkedEncoding||!1}set chunkedEncoding(e){if(this._started)throw new Error("chunkedEncoding can only be set before the request is started");if(void 0!==this._chunkedEncoding)throw new Error("chunkedEncoding can only be set once");this._chunkedEncoding=!!e,this._chunkedEncoding&&(this._body=new ChunkedBodyStream(this),this._urlLoaderOptions.body=e=>{this._body.startReading(e)})}setHeader(e,t){if("string"!=typeof e)throw new TypeError("`name` should be a string in setHeader(name, value)");if(null==t)throw new Error('`value` required in setHeader("'+e+'", value)');if(this._started||this._firstWrite)throw new Error("Can't set headers after they are sent");if(!a(e))throw new Error(`Invalid header name: '${e}'`);if(!l(t.toString()))throw new Error(`Invalid value for header '${e}': '${t}'`);const r=e.toLowerCase();this._urlLoaderOptions.headers[r]={name:e,value:t}}getHeader(e){if(null==e)throw new Error("`name` is required for getHeader(name)");const t=e.toLowerCase(),r=this._urlLoaderOptions.headers[t];return r&&r.value}removeHeader(e){if(null==e)throw new Error("`name` is required for removeHeader(name)");if(this._started||this._firstWrite)throw new Error("Can't remove headers after they are sent");const t=e.toLowerCase();delete this._urlLoaderOptions.headers[t]}_write(e,t,r){this._firstWrite=!0,this._body||(this._body=new SlurpStream,this._body.on("finish",(()=>{this._urlLoaderOptions.body=this._body.data(),this._startRequest()}))),this._body.write(e,t,r)}_final(e){this._body?this._body.end(e):(this._startRequest(),e())}_startRequest(){this._started=!0;this._urlLoaderOptions.referrer=this.getHeader("referer")||"",this._urlLoaderOptions.origin=this._urlLoaderOptions.origin||this.getHeader("origin")||"",this._urlLoaderOptions.hasUserActivation="?1"===this.getHeader("sec-fetch-user"),this._urlLoaderOptions.mode=this.getHeader("sec-fetch-mode")||"",this._urlLoaderOptions.destination=this.getHeader("sec-fetch-dest")||"";const e={...this._urlLoaderOptions,extraHeaders:(e=>{const t={};for(const r of Object.keys(e)){const o=e[r];t[o.name]=o.value.toString()}return t})(this._urlLoaderOptions.headers)};this._urlLoader=c(e),this._urlLoader.on("response-started",((e,t,r)=>{const o=this._response=new IncomingMessage(r);this.emit("response",o)})),this._urlLoader.on("data",((e,t,r)=>{this._response._storeInternalData(Buffer.from(t),r)})),this._urlLoader.on("complete",(()=>{this._response&&this._response._storeInternalData(null,null)})),this._urlLoader.on("error",((e,t)=>{const r=new Error(t);this._response&&this._response.destroy(r),this._die(r)})),this._urlLoader.on("login",((e,t,r)=>{this.emit("login",t,r)||r()})),this._urlLoader.on("redirect",((e,t,r)=>{const{statusCode:o,newMethod:n,newUrl:s}=t;if("error"===this._redirectPolicy)this._die(new Error("Attempted to redirect, but redirect policy was 'error'"));else if("manual"===this._redirectPolicy){let e=!1;this._followRedirectCb=()=>{e=!0};try{this.emit("redirect",o,n,s,r)}finally{this._followRedirectCb=void 0,e||this._aborted||this._die(new Error("Redirect was cancelled"))}}else if("follow"===this._redirectPolicy)try{this._followRedirectCb=()=>{},this.emit("redirect",o,n,s,r)}finally{this._followRedirectCb=void 0}else this._die(new Error(`Unexpected redirect policy '${this._redirectPolicy}'`))})),this._urlLoader.on("upload-progress",((e,t,r)=>{this._uploadProgress={active:!0,started:!0,current:t,total:r},this.emit("upload-progress",t,r)})),this._urlLoader.on("download-progress",((e,t)=>{this._response&&this._response.emit("download-progress",t)}))}followRedirect(){if(!this._followRedirectCb)throw new Error("followRedirect() called, but was not waiting for a redirect");this._followRedirectCb()}abort(){this._aborted||process.nextTick((()=>{this.emit("abort")})),this._aborted=!0,this._die()}_die(e){this._writableState.destroyed&&e&&this.emit("error",e),this.destroy(e),this._urlLoader&&(this._urlLoader.cancel(),this._response&&this._response.destroy(e))}getUploadProgress(){return this._uploadProgress?{...this._uploadProgress}:{active:!1,started:!1,current:0,total:0}}}t.ClientRequest=ClientRequest,t.request=function request(e,t){return new ClientRequest(e,t)},t.isOnline=i,Object.defineProperty(t,"online",{get:()=>i()})},"./lib/browser/api/notification.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});const{Notification:r,isSupported:o}=process._linkedBinding("electron_browser_notification");r.isSupported=o,t.default=r},"./lib/browser/api/power-monitor.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=r("events"),n=r("./lib/browser/api/exports/electron.ts"),{createPowerMonitor:s,getSystemIdleState:i,getSystemIdleTime:a,isOnBatteryPower:l}=process._linkedBinding("electron_browser_power_monitor");class PowerMonitor extends o.EventEmitter{constructor(){super(),this.once("newListener",(()=>{n.app.whenReady().then((()=>{const e=s();e.emit=this.emit.bind(this),"linux"===process.platform&&(e.setListeningForShutdown(this.listenerCount("shutdown")>0),this.on("newListener",(t=>{"shutdown"===t&&e.setListeningForShutdown(this.listenerCount("shutdown")+1>0)})),this.on("removeListener",(t=>{"shutdown"===t&&e.setListeningForShutdown(this.listenerCount("shutdown")>0)})))}))}))}getSystemIdleState(e){return i(e)}getSystemIdleTime(){return a()}isOnBatteryPower(){return l()}get onBatteryPower(){return this.isOnBatteryPower()}}e.exports=new PowerMonitor},"./lib/browser/api/power-save-blocker.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});const{powerSaveBlocker:r}=process._linkedBinding("electron_browser_power_save_blocker");t.default=r},"./lib/browser/api/protocol.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=r("./lib/browser/api/exports/electron.ts"),n=process._linkedBinding("electron_browser_protocol");Object.setPrototypeOf(n,new Proxy({},{get(e,t){if(!o.app.isReady())return;const r=o.session.defaultSession.protocol;return Object.prototype.hasOwnProperty.call(r,t)?(...e)=>r[t](...e):void 0},ownKeys:()=>o.app.isReady()?Reflect.ownKeys(o.session.defaultSession.protocol):[],has:(e,t)=>!!o.app.isReady()&&Reflect.has(o.session.defaultSession.protocol,t),getOwnPropertyDescriptor:()=>({configurable:!0,enumerable:!0})})),t.default=n},"./lib/browser/api/push-notifications.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});const{pushNotifications:r}=process._linkedBinding("electron_browser_push_notifications");t.default=r},"./lib/browser/api/safe-storage.ts":e=>{const t=process._linkedBinding("electron_browser_safe_storage");e.exports=t},"./lib/browser/api/screen.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=r("events"),{createScreen:n}=process._linkedBinding("electron_browser_screen");let s;const createScreenIfNeeded=()=>{void 0===s&&(s=n())};t.default=new Proxy({},{get:(e,t)=>{createScreenIfNeeded();const r=s[t];return"function"==typeof r?r.bind(s):r},set:(e,t,r)=>(createScreenIfNeeded(),Reflect.set(s,t,r)),ownKeys:()=>(createScreenIfNeeded(),Reflect.ownKeys(s)),has:(e,t)=>(createScreenIfNeeded(),t in s),getOwnPropertyDescriptor:(e,t)=>(createScreenIfNeeded(),Reflect.getOwnPropertyDescriptor(s,t)),getPrototypeOf:()=>o.EventEmitter.prototype})},"./lib/browser/api/session.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});const{fromPartition:r}=process._linkedBinding("electron_browser_session");t.default={fromPartition:r,get defaultSession(){return r("")}}},"./lib/browser/api/share-menu.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=r("./lib/browser/api/exports/electron.ts");t.default=class ShareMenu{constructor(e){this.menu=new o.Menu({sharingItem:e})}popup(e){this.menu.popup(e)}closePopup(e){this.menu.closePopup(e)}}},"./lib/browser/api/system-preferences.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});const{systemPreferences:r}=process._linkedBinding("electron_browser_system_preferences");if("getAppLevelAppearance"in r){const e=r.getAppLevelAppearance,t=r.setAppLevelAppearance;Object.defineProperty(r,"appLevelAppearance",{get:()=>e.call(r),set:e=>t.call(r,e)})}if("getEffectiveAppearance"in r){const e=r.getEffectiveAppearance;Object.defineProperty(r,"effectiveAppearance",{get:()=>e.call(r)})}t.default=r},"./lib/browser/api/touch-bar.ts":function(e,t,r){var o,n=this&&this.__decorate||function(e,t,r,o){var n,s=arguments.length,i=s<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,r):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(e,t,r,o);else for(var a=e.length-1;a>=0;a--)(n=e[a])&&(i=(s<3?n(i):s>3?n(t,r,i):n(t,r))||i);return s>3&&i&&Object.defineProperty(t,r,i),i};Object.defineProperty(t,"__esModule",{value:!0});const s=r("events");let i=1;const a=Symbol("hidden touch bar props"),extendConstructHook=(e,t)=>{const r=e._hook;e._hook=function(){r&&r.call(this),t.call(this)}},ImmutableProperty=e=>(t,r)=>{extendConstructHook(t,(function(){this[a][r]=e(this._config,((e,t)=>{this[a][e]=t}))})),Object.defineProperty(t,r,{get:function(){return this[a][r]},set:function(){throw new Error(`Cannot override property ${name}`)},enumerable:!0,configurable:!1})},LiveProperty=(e,t)=>(r,o)=>{extendConstructHook(r,(function(){this[a][o]=e(this._config),t&&t(this,this[a][o])})),Object.defineProperty(r,o,{get:function(){return this[a][o]},set:function(e){t&&t(this,e),this[a][o]=e,this.emit("change",this)},enumerable:!0})};class TouchBarItem extends s.EventEmitter{constructor(e){super(),this._parents=[],this._config=this._config||e||{},this[a]={};const t=this._hook;t&&t.call(this),delete this._hook}_addParent(e){this._parents.some((t=>t.id===e.id))||this._parents.push({id:e.id,type:e.type})}_removeParent(e){this._parents=this._parents.filter((t=>t.id!==e.id))}}n([ImmutableProperty((()=>""+i++))],TouchBarItem.prototype,"id",void 0);class TouchBarButton extends TouchBarItem{}n([ImmutableProperty((()=>"button"))],TouchBarButton.prototype,"type",void 0),n([LiveProperty((e=>e.label))],TouchBarButton.prototype,"label",void 0),n([LiveProperty((e=>e.accessibilityLabel))],TouchBarButton.prototype,"accessibilityLabel",void 0),n([LiveProperty((e=>e.backgroundColor))],TouchBarButton.prototype,"backgroundColor",void 0),n([LiveProperty((e=>e.icon))],TouchBarButton.prototype,"icon",void 0),n([LiveProperty((e=>e.iconPosition))],TouchBarButton.prototype,"iconPosition",void 0),n([LiveProperty((e=>"boolean"!=typeof e.enabled||e.enabled))],TouchBarButton.prototype,"enabled",void 0),n([ImmutableProperty((({click:e})=>"function"==typeof e?()=>e():null))],TouchBarButton.prototype,"onInteraction",void 0);class TouchBarColorPicker extends TouchBarItem{}n([ImmutableProperty((()=>"colorpicker"))],TouchBarColorPicker.prototype,"type",void 0),n([LiveProperty((e=>e.availableColors))],TouchBarColorPicker.prototype,"availableColors",void 0),n([LiveProperty((e=>e.selectedColor))],TouchBarColorPicker.prototype,"selectedColor",void 0),n([ImmutableProperty((({change:e},t)=>"function"==typeof e?r=>{t("selectedColor",r.color),e(r.color)}:null))],TouchBarColorPicker.prototype,"onInteraction",void 0);class TouchBarGroup extends TouchBarItem{constructor(){super(...arguments),this.onInteraction=null}}n([ImmutableProperty((()=>"group"))],TouchBarGroup.prototype,"type",void 0),n([LiveProperty((e=>e.items instanceof TouchBar?e.items:new TouchBar(e.items)),((e,t)=>{if(e.child)for(const t of e.child.orderedItems)t._removeParent(e);for(const r of t.orderedItems)r._addParent(e)}))],TouchBarGroup.prototype,"child",void 0);class TouchBarLabel extends TouchBarItem{constructor(){super(...arguments),this.onInteraction=null}}n([ImmutableProperty((()=>"label"))],TouchBarLabel.prototype,"type",void 0),n([LiveProperty((e=>e.label))],TouchBarLabel.prototype,"label",void 0),n([LiveProperty((e=>e.accessibilityLabel))],TouchBarLabel.prototype,"accessibilityLabel",void 0),n([LiveProperty((e=>e.textColor))],TouchBarLabel.prototype,"textColor",void 0);class TouchBarPopover extends TouchBarItem{constructor(){super(...arguments),this.onInteraction=null}}n([ImmutableProperty((()=>"popover"))],TouchBarPopover.prototype,"type",void 0),n([LiveProperty((e=>e.label))],TouchBarPopover.prototype,"label",void 0),n([LiveProperty((e=>e.icon))],TouchBarPopover.prototype,"icon",void 0),n([LiveProperty((e=>e.showCloseButton))],TouchBarPopover.prototype,"showCloseButton",void 0),n([LiveProperty((e=>e.items instanceof TouchBar?e.items:new TouchBar(e.items)),((e,t)=>{if(e.child)for(const t of e.child.orderedItems)t._removeParent(e);for(const r of t.orderedItems)r._addParent(e)}))],TouchBarPopover.prototype,"child",void 0);class TouchBarSlider extends TouchBarItem{}n([ImmutableProperty((()=>"slider"))],TouchBarSlider.prototype,"type",void 0),n([LiveProperty((e=>e.label))],TouchBarSlider.prototype,"label",void 0),n([LiveProperty((e=>e.minValue))],TouchBarSlider.prototype,"minValue",void 0),n([LiveProperty((e=>e.maxValue))],TouchBarSlider.prototype,"maxValue",void 0),n([LiveProperty((e=>e.value))],TouchBarSlider.prototype,"value",void 0),n([ImmutableProperty((({change:e},t)=>"function"==typeof e?r=>{t("value",r.value),e(r.value)}:null))],TouchBarSlider.prototype,"onInteraction",void 0);class TouchBarSpacer extends TouchBarItem{constructor(){super(...arguments),this.onInteraction=null}}n([ImmutableProperty((()=>"spacer"))],TouchBarSpacer.prototype,"type",void 0),n([ImmutableProperty((e=>e.size))],TouchBarSpacer.prototype,"size",void 0);class TouchBarSegmentedControl extends TouchBarItem{}n([ImmutableProperty((()=>"segmented_control"))],TouchBarSegmentedControl.prototype,"type",void 0),n([LiveProperty((e=>e.segmentStyle))],TouchBarSegmentedControl.prototype,"segmentStyle",void 0),n([LiveProperty((e=>e.segments||[]))],TouchBarSegmentedControl.prototype,"segments",void 0),n([LiveProperty((e=>e.selectedIndex))],TouchBarSegmentedControl.prototype,"selectedIndex",void 0),n([LiveProperty((e=>e.mode))],TouchBarSegmentedControl.prototype,"mode",void 0),n([ImmutableProperty((({change:e},t)=>"function"==typeof e?r=>{t("selectedIndex",r.selectedIndex),e(r.selectedIndex,r.isSelected)}:null))],TouchBarSegmentedControl.prototype,"onInteraction",void 0);class TouchBarScrubber extends TouchBarItem{}n([ImmutableProperty((()=>"scrubber"))],TouchBarScrubber.prototype,"type",void 0),n([LiveProperty((e=>e.items))],TouchBarScrubber.prototype,"items",void 0),n([LiveProperty((e=>e.selectedStyle||null))],TouchBarScrubber.prototype,"selectedStyle",void 0),n([LiveProperty((e=>e.overlayStyle||null))],TouchBarScrubber.prototype,"overlayStyle",void 0),n([LiveProperty((e=>e.showArrowButtons||!1))],TouchBarScrubber.prototype,"showArrowButtons",void 0),n([LiveProperty((e=>e.mode||"free"))],TouchBarScrubber.prototype,"mode",void 0),n([LiveProperty((e=>void 0===e.continuous||e.continuous))],TouchBarScrubber.prototype,"continuous",void 0),n([ImmutableProperty((({select:e,highlight:t})=>"function"==typeof e||"function"==typeof t?r=>{"select"===r.type?e&&e(r.selectedIndex):t&&t(r.highlightedIndex)}:null))],TouchBarScrubber.prototype,"onInteraction",void 0);class TouchBarOtherItemsProxy extends TouchBarItem{constructor(){super(...arguments),this.onInteraction=null}}n([ImmutableProperty((()=>"other_items_proxy"))],TouchBarOtherItemsProxy.prototype,"type",void 0);const l=Symbol("escape item");class TouchBar extends s.EventEmitter{constructor(e){if(super(),this.windowListeners=new Map,this.items=new Map,this.orderedItems=[],this.changeListener=e=>{this.emit("change",e.id,e.type)},this[o]=null,null==e)throw new Error("Must specify options object as first argument");let{items:t,escapeItem:r}=e;Array.isArray(t)||(t=[]),this.escapeItem=r||null;const registerItem=e=>{this.items.set(e.id,e),e.on("change",this.changeListener),e.child instanceof TouchBar&&e.child.orderedItems.forEach(registerItem)};let n=!1;const s=new Set;t.forEach((e=>{if(!(e instanceof TouchBarItem))throw new Error("Each item must be an instance of TouchBarItem");if("other_items_proxy"===e.type){if(n)throw new Error("Must only have one OtherItemsProxy per TouchBar");n=!0}if(s.has(e.id))throw new Error("Cannot add a single instance of TouchBarItem multiple times in a TouchBar");s.add(e.id)}));for(const e of t)this.orderedItems.push(e),registerItem(e)}static _setOnWindow(e,t){null!=t._touchBar&&t._touchBar._removeFromWindow(t),e?(Array.isArray(e)&&(e=new TouchBar({items:e})),e._addToWindow(t)):t._setTouchBarItems([])}set escapeItem(e){if(null!=e&&!(e instanceof TouchBarItem))throw new Error("Escape item must be an instance of TouchBarItem");const t=this.escapeItem;t&&t.removeListener("change",this.changeListener),this[l]=e,null!=this.escapeItem&&this.escapeItem.on("change",this.changeListener),this.emit("escape-item-change",e)}get escapeItem(){return this[l]}_addToWindow(e){const{id:t}=e;if(this.windowListeners.has(t))return;e._touchBar=this;const changeListener=t=>{e._refreshTouchBarItem(t)};this.on("change",changeListener);const escapeItemListener=t=>{e._setEscapeTouchBarItem(t??{})};this.on("escape-item-change",escapeItemListener);const interactionListener=(e,t,r)=>{let o=this.items.get(t);null==o&&null!=this.escapeItem&&this.escapeItem.id===t&&(o=this.escapeItem),null!=o&&null!=o.onInteraction&&o.onInteraction(r)};e.on("-touch-bar-interaction",interactionListener);const removeListeners=()=>{this.removeListener("change",changeListener),this.removeListener("escape-item-change",escapeItemListener),e.removeListener("-touch-bar-interaction",interactionListener),e.removeListener("closed",removeListeners),e._touchBar=null,this.windowListeners.delete(t);const unregisterItems=e=>{for(const t of e)t.removeListener("change",this.changeListener),t.child instanceof TouchBar&&unregisterItems(t.child.orderedItems)};unregisterItems(this.orderedItems),this.escapeItem&&this.escapeItem.removeListener("change",this.changeListener)};e.once("closed",removeListeners),this.windowListeners.set(t,removeListeners),e._setTouchBarItems(this.orderedItems),escapeItemListener(this.escapeItem)}_removeFromWindow(e){const t=this.windowListeners.get(e.id);null!=t&&t()}}o=l,TouchBar.TouchBarButton=TouchBarButton,TouchBar.TouchBarColorPicker=TouchBarColorPicker,TouchBar.TouchBarGroup=TouchBarGroup,TouchBar.TouchBarLabel=TouchBarLabel,TouchBar.TouchBarPopover=TouchBarPopover,TouchBar.TouchBarSlider=TouchBarSlider,TouchBar.TouchBarSpacer=TouchBarSpacer,TouchBar.TouchBarSegmentedControl=TouchBarSegmentedControl,TouchBar.TouchBarScrubber=TouchBarScrubber,TouchBar.TouchBarOtherItemsProxy=TouchBarOtherItemsProxy,t.default=TouchBar},"./lib/browser/api/tray.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});const{Tray:r}=process._linkedBinding("electron_browser_tray");t.default=r},"./lib/browser/api/utility-process.ts":function(e,t,r){var o,n,s,i=this&&this.__classPrivateFieldSet||function(e,t,r,o,n){if("m"===o)throw new TypeError("Private method is not writable");if("a"===o&&!n)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===o?n.call(e,r):n?n.value=r:t.set(e,r),r},a=this&&this.__classPrivateFieldGet||function(e,t,r,o){if("a"===r&&!o)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!o:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?o:"a"===r?o.call(e):o?o.value:t.get(e)};Object.defineProperty(t,"__esModule",{value:!0}),t.fork=void 0;const l=r("events"),c=r("stream"),p=r("net"),d=r("./lib/browser/message-port-main.ts"),{_fork:u}=process._linkedBinding("electron_browser_utility_process");class ForkUtilityProcess extends l.EventEmitter{constructor(e,t,r){if(super(),o.set(this,void 0),n.set(this,null),s.set(this,null),!e)throw new Error("Missing UtilityProcess entry script.");if(null==t?t=[]:"object"!=typeof t||Array.isArray(t)||(r=t,t=[]),!(r=null==r?{}:{...r}))throw new Error("Options cannot be undefined.");if(null!=r.execArgv&&!Array.isArray(r.execArgv))throw new Error("execArgv must be an array of strings.");if(null!=r.serviceName&&"string"!=typeof r.serviceName)throw new Error("serviceName must be a string.");if(null!=r.cwd&&"string"!=typeof r.cwd)throw new Error("cwd path must be a string.");if("string"==typeof r.stdio){const e=[];switch(r.stdio){case"inherit":case"ignore":e.push("ignore",r.stdio,r.stdio);break;case"pipe":i(this,s,new c.PassThrough,"f"),i(this,n,new c.PassThrough,"f"),e.push("ignore",r.stdio,r.stdio);break;default:throw new Error("stdio must be of the following values: inherit, pipe, ignore")}r.stdio=e}else if(Array.isArray(r.stdio)){if(!(r.stdio.length>=3))throw new Error("configuration missing for stdin, stdout or stderr.");if("ignore"!==r.stdio[0])throw new Error("stdin value other than ignore is not supported.");if("pipe"===r.stdio[1])i(this,n,new c.PassThrough,"f");else if("ignore"!==r.stdio[1]&&"inherit"!==r.stdio[1])throw new Error("stdout configuration must be of the following values: inherit, pipe, ignore");if("pipe"===r.stdio[2])i(this,s,new c.PassThrough,"f");else if("ignore"!==r.stdio[2]&&"inherit"!==r.stdio[2])throw new Error("stderr configuration must be of the following values: inherit, pipe, ignore")}i(this,o,u({options:r,modulePath:e,args:t}),"f"),a(this,o,"f").emit=(e,...t)=>{if("exit"===e){try{this.emit("exit",...t)}finally{i(this,o,null,"f"),a(this,n,"f")&&(a(this,n,"f").removeAllListeners(),i(this,n,null,"f")),a(this,s,"f")&&(a(this,s,"f").removeAllListeners(),i(this,s,null,"f"))}return!1}return"stdout"===e&&a(this,n,"f")?(new p.Socket({fd:t[0],readable:!0}).pipe(a(this,n,"f")),!0):"stderr"===e&&a(this,s,"f")?(new p.Socket({fd:t[0],readable:!0}).pipe(a(this,s,"f")),!0):this.emit(e,...t)}}get pid(){return a(this,o,"f")?.pid}get stdout(){return a(this,n,"f")}get stderr(){return a(this,s,"f")}postMessage(e,t){return Array.isArray(t)?(t=t.map((e=>e instanceof d.MessagePortMain?e._internalPort:e)),a(this,o,"f")?.postMessage(e,t)):a(this,o,"f")?.postMessage(e)}kill(){return null!==a(this,o,"f")&&a(this,o,"f").kill()}}o=new WeakMap,n=new WeakMap,s=new WeakMap,t.fork=function fork(e,t,r){return new ForkUtilityProcess(e,t,r)}},"./lib/browser/api/view.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});const{View:r}=process._linkedBinding("electron_browser_view");t.default=r},"./lib/browser/api/views/image-view.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=r("./lib/browser/api/exports/electron.ts"),{ImageView:n}=process._linkedBinding("electron_browser_image_view");Object.setPrototypeOf(n.prototype,o.View.prototype),t.default=n},"./lib/browser/api/web-contents-view.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=r("./lib/browser/api/exports/electron.ts"),{WebContentsView:n}=process._linkedBinding("electron_browser_web_contents_view");Object.setPrototypeOf(n.prototype,o.View.prototype),t.default=n},"./lib/browser/api/web-contents.ts":(e,t,r)=>{var o=r("./lib/common/webpack-globals-provider.ts").Promise;Object.defineProperty(t,"__esModule",{value:!0}),t.getAllWebContents=t.getFocusedWebContents=t.fromDevToolsTargetId=t.fromFrame=t.fromId=t.create=void 0;const n=r("./lib/browser/api/exports/electron.ts"),s=r("url"),i=r("path"),a=r("./lib/browser/guest-window-manager.ts"),l=r("./lib/browser/parse-features-string.ts"),c=r("./lib/browser/ipc-main-internal.ts"),p=r("./lib/browser/ipc-main-internal-utils.ts"),d=r("./lib/browser/message-port-main.ts"),u=r("./lib/browser/ipc-main-impl.ts"),h=r("./lib/common/deprecate.ts");n.session;const m=process._linkedBinding("electron_browser_web_frame_main");let f=0;const b={Letter:{custom_display_name:"Letter",height_microns:279400,name:"NA_LETTER",width_microns:215900},Legal:{custom_display_name:"Legal",height_microns:355600,name:"NA_LEGAL",width_microns:215900},Tabloid:{height_microns:431800,name:"NA_LEDGER",width_microns:279400,custom_display_name:"Tabloid"},A0:{custom_display_name:"A0",height_microns:1189e3,name:"ISO_A0",width_microns:841e3},A1:{custom_display_name:"A1",height_microns:841e3,name:"ISO_A1",width_microns:594e3},A2:{custom_display_name:"A2",height_microns:594e3,name:"ISO_A2",width_microns:42e4},A3:{custom_display_name:"A3",height_microns:42e4,name:"ISO_A3",width_microns:297e3},A4:{custom_display_name:"A4",height_microns:297e3,name:"ISO_A4",is_default:"true",width_microns:21e4},A5:{custom_display_name:"A5",height_microns:21e4,name:"ISO_A5",width_microns:148e3},A6:{custom_display_name:"A6",height_microns:148e3,name:"ISO_A6",width_microns:105e3}},w={letter:{width:8.5,height:11},legal:{width:8.5,height:14},tabloid:{width:11,height:17},ledger:{width:17,height:11},a0:{width:33.1,height:46.8},a1:{width:23.4,height:33.1},a2:{width:16.54,height:23.4},a3:{width:11.7,height:16.54},a4:{width:8.27,height:11.7},a5:{width:5.83,height:8.27},a6:{width:4.13,height:5.83}},g=process._linkedBinding("electron_browser_web_contents"),y=process._linkedBinding("electron_browser_printing"),{WebContents:_}=g;_.prototype.postMessage=function(...e){return this.mainFrame.postMessage(...e)},_.prototype.send=function(e,...t){return this.mainFrame.send(e,...t)},_.prototype._sendInternal=function(e,...t){return this.mainFrame._sendInternal(e,...t)},_.prototype.sendToFrame=function(e,t,...r){const o=function getWebFrame(e,t){if("number"==typeof t)return n.webFrameMain.fromId(e.mainFrame.processId,t);if(Array.isArray(t)&&2===t.length&&t.every((e=>"number"==typeof e)))return n.webFrameMain.fromId(t[0],t[1]);throw new Error("Missing required frame argument (must be number or [processId, frameId])")}(this,e);return!!o&&(o.send(t,...r),!0)};const v=["insertCSS","insertText","removeInsertedCSS","setVisualZoomLevelLimits"];for(const e of v)_.prototype[e]=function(...t){return p.invokeInWebContents(this,"RENDERER_WEB_FRAME_METHOD",e,...t)};const waitTillCanExecuteJavaScript=async e=>{if(!e.getURL()||e.isLoadingMainFrame())return new o((t=>{e.once("did-stop-loading",(()=>{t()}))}))};let S;_.prototype.executeJavaScript=async function(e,t){return await waitTillCanExecuteJavaScript(this),p.invokeInWebContents(this,"RENDERER_WEB_FRAME_METHOD","executeJavaScript",String(e),!!t)},_.prototype.executeJavaScriptInIsolatedWorld=async function(e,t,r){return await waitTillCanExecuteJavaScript(this),p.invokeInWebContents(this,"RENDERER_WEB_FRAME_METHOD","executeJavaScriptInIsolatedWorld",e,t,!!r)},_.prototype.printToPDF=async function(e){const t={requestID:++f,landscape:!1,displayHeaderFooter:!1,headerTemplate:"",footerTemplate:"",printBackground:!1,scale:1,paperWidth:8.5,paperHeight:11,marginTop:.4,marginBottom:.4,marginLeft:.4,marginRight:.4,pageRanges:"",preferCSSPageSize:!1};if(void 0!==e.landscape){if("boolean"!=typeof e.landscape)return o.reject(new Error("landscape must be a Boolean"));t.landscape=e.landscape}if(void 0!==e.displayHeaderFooter){if("boolean"!=typeof e.displayHeaderFooter)return o.reject(new Error("displayHeaderFooter must be a Boolean"));t.displayHeaderFooter=e.displayHeaderFooter}if(void 0!==e.printBackground){if("boolean"!=typeof e.printBackground)return o.reject(new Error("printBackground must be a Boolean"));t.shouldPrintBackgrounds=e.printBackground}if(void 0!==e.scale){if("number"!=typeof e.scale)return o.reject(new Error("scale must be a Number"));t.scale=e.scale}const{pageSize:r}=e;if(void 0!==r)if("string"==typeof r){const e=w[r.toLowerCase()];if(!e)return o.reject(new Error(`Invalid pageSize ${r}`));t.paperWidth=e.width,t.paperHeight=e.height}else{if("object"!=typeof e.pageSize)return o.reject(new Error("pageSize must be a String or Object"));if(!r.height||!r.width)return o.reject(new Error("height and width properties are required for pageSize"));t.paperWidth=r.width,t.paperHeight=r.height}const{margins:n}=e;if(void 0!==n){if("object"!=typeof n)return o.reject(new Error("margins must be an Object"));if(void 0!==n.top){if("number"!=typeof n.top)return o.reject(new Error("margins.top must be a Number"));t.marginTop=n.top}if(void 0!==n.bottom){if("number"!=typeof n.bottom)return o.reject(new Error("margins.bottom must be a Number"));t.marginBottom=n.bottom}if(void 0!==n.left){if("number"!=typeof n.left)return o.reject(new Error("margins.left must be a Number"));t.marginLeft=n.left}if(void 0!==n.right){if("number"!=typeof n.right)return o.reject(new Error("margins.right must be a Number"));t.marginRight=n.right}}if(void 0!==e.pageRanges){if("string"!=typeof e.pageRanges)return o.reject(new Error("pageRanges must be a String"));t.pageRanges=e.pageRanges}if(void 0!==e.headerTemplate){if("string"!=typeof e.headerTemplate)return o.reject(new Error("headerTemplate must be a String"));t.headerTemplate=e.headerTemplate}if(void 0!==e.footerTemplate){if("string"!=typeof e.footerTemplate)return o.reject(new Error("footerTemplate must be a String"));t.footerTemplate=e.footerTemplate}if(void 0!==e.preferCSSPageSize){if("boolean"!=typeof e.preferCSSPageSize)return o.reject(new Error("footerTemplate must be a String"));t.preferCSSPageSize=e.preferCSSPageSize}if(this._printToPDF)return S=S?S.then((()=>this._printToPDF(t))):this._printToPDF(t),S;{const e=new Error("Printing feature is disabled");return o.reject(e)}},_.prototype.print=function(e={},t){if("object"==typeof e&&void 0!==e.pageSize){const t=e.pageSize;if("object"==typeof t){if(!t.height||!t.width)throw new Error("height and width properties are required for pageSize");const r=Math.ceil(t.height),o=Math.ceil(t.width);if(!((e,t)=>[e,t].every((e=>e>352)))(o,r))throw new Error("height and width properties must be minimum 352 microns.");e.mediaSize={name:"CUSTOM",custom_display_name:"Custom",height_microns:r,width_microns:o}}else{if(!b[t])throw new Error(`Unsupported pageSize: ${t}`);e.mediaSize=b[t]}}this._print?t?this._print(e,t):this._print(e):console.error("Error: Printing feature is disabled.")},_.prototype.getPrinters=function(){return y.getPrinterList?y.getPrinterList():(console.error("Error: Printing feature is disabled."),[])},_.prototype.getPrintersAsync=async function(){return y.getPrinterListAsync?y.getPrinterListAsync():(console.error("Error: Printing feature is disabled."),[])},_.prototype.loadFile=function(e,t={}){if("string"!=typeof e)throw new Error("Must pass filePath as a string");const{query:r,search:o,hash:a}=t;return this.loadURL(s.format({protocol:"file",slashes:!0,pathname:i.resolve(n.app.getAppPath(),e),query:r,search:o,hash:a}))},_.prototype.loadURL=function(e,t){t||(t={});const r=new o(((t,r)=>{const rejectAndCleanup=(e,t,o)=>{const n=new Error(`${t} (${e}) loading '${"string"==typeof o?o.substr(0,2048):o}'`);Object.assign(n,{errno:e,code:t,url:o}),removeListeners(),r(n)},finishListener=()=>{removeListeners(),t()},failListener=(e,t,r,o,n)=>{n&&rejectAndCleanup(t,r,o)};let o=!1;const navigationListener=(e,t,r,n)=>{if(n){if(o&&!r)return rejectAndCleanup(-3,"ERR_ABORTED",t);o=!0}},stopLoadingListener=()=>{rejectAndCleanup(-2,"ERR_FAILED",e)},removeListeners=()=>{this.removeListener("did-finish-load",finishListener),this.removeListener("did-fail-load",failListener),this.removeListener("did-navigate-in-page",finishListener),this.removeListener("did-start-navigation",navigationListener),this.removeListener("did-stop-loading",stopLoadingListener),this.removeListener("destroyed",stopLoadingListener)};this.on("did-finish-load",finishListener),this.on("did-fail-load",failListener),this.on("did-navigate-in-page",finishListener),this.on("did-start-navigation",navigationListener),this.on("did-stop-loading",stopLoadingListener),this.on("destroyed",stopLoadingListener)}));return r.catch((()=>{})),this._loadURL(e,t),this.emit("load-url",e,t),r},_.prototype.setWindowOpenHandler=function(e){this._windowOpenHandler=e},_.prototype._callWindowOpenHandler=function(e,t){const r={browserWindowConstructorOptions:null,outlivesOpener:!1};if(!this._windowOpenHandler)return r;const o=this._windowOpenHandler(t);return"object"!=typeof o?(e.preventDefault(),console.error(`The window open handler response must be an object, but was instead of type '${typeof o}'.`),r):null===o?(e.preventDefault(),console.error("The window open handler response must be an object, but was instead null."),r):"deny"===o.action?(e.preventDefault(),r):"allow"===o.action?"object"==typeof o.overrideBrowserWindowOptions&&null!==o.overrideBrowserWindowOptions?{browserWindowConstructorOptions:o.overrideBrowserWindowOptions,outlivesOpener:"boolean"==typeof o.outlivesOpener&&o.outlivesOpener}:{browserWindowConstructorOptions:{},outlivesOpener:"boolean"==typeof o.outlivesOpener&&o.outlivesOpener}:(e.preventDefault(),console.error("The window open handler response must be an object with an 'action' property of 'allow' or 'deny'."),r)};const addReplyToEvent=e=>{const{processId:t,frameId:r}=e;e.reply=(o,...n)=>{e.sender.sendToFrame([t,r],o,...n)}},addSenderFrameToEvent=e=>{const{processId:t,frameId:r}=e;Object.defineProperty(e,"senderFrame",{get:()=>n.webFrameMain.fromId(t,r)})},getWebFrameForEvent=e=>e.processId&&e.frameId?m.fromIdOrNull(e.processId,e.frameId):null,P=process._linkedBinding("electron_common_command_line"),M=process._linkedBinding("electron_common_environment");_.prototype._init=function(){const e=this.getLastWebPreferences()||{};e.nodeIntegration||null==e.preload||null!=e.sandbox||h.log("The default sandbox option for windows without nodeIntegration is changing. Presently, by default, when a window has a preload script, it defaults to being unsandboxed. In Electron 20, this default will be changing, and all windows that have nodeIntegration: false (which is the default) will be sandboxed by default. If your preload script doesn't use Node, no action is needed. If your preload script does use Node, either refactor it to move Node usage to the main process, or specify sandbox: false in your WebPreferences.");const t=this.id;Object.defineProperty(this,"id",{value:t,writable:!1}),this._windowOpenHandler=null;const r=new u.IpcMainImpl;if(Object.defineProperty(this,"ipc",{get:()=>r,enumerable:!0}),this.on("-ipc-message",(function(e,t,o,s){if(addSenderFrameToEvent(e),t)c.ipcMainInternal.emit(o,e,...s);else{addReplyToEvent(e),this.emit("ipc-message",e,o,...s);const t=getWebFrameForEvent(e);t&&t.ipc.emit(o,e,...s),r.emit(o,e,...s),n.ipcMain.emit(o,e,...s)}})),this.on("-ipc-invoke",(function(e,t,o,s){addSenderFrameToEvent(e),e._reply=t=>e.sendReply({result:t}),e._throw=t=>{console.error(`Error occurred in handler for '${o}':`,t),e.sendReply({error:t.toString()})};const i=getWebFrameForEvent(e),a=(t?[c.ipcMainInternal]:[i?.ipc,r,n.ipcMain]).find((e=>e&&e._invokeHandlers.has(o)));a?a._invokeHandlers.get(o)(e,...s):e._throw(`No handler registered for '${o}'`)})),this.on("-ipc-message-sync",(function(e,t,o,s){if(addSenderFrameToEvent(e),(e=>{Object.defineProperty(e,"returnValue",{set:t=>e.sendReply(t),get:()=>{}})})(e),t)c.ipcMainInternal.emit(o,e,...s);else{addReplyToEvent(e);const t=getWebFrameForEvent(e);0!==this.listenerCount("ipc-message-sync")||0!==r.listenerCount(o)||0!==n.ipcMain.listenerCount(o)||t&&0!==t.ipc.listenerCount(o)||console.warn(`WebContents #${this.id} called ipcRenderer.sendSync() with '${o}' channel without listeners.`),this.emit("ipc-message-sync",e,o,...s),t&&t.ipc.emit(o,e,...s),r.emit(o,e,...s),n.ipcMain.emit(o,e,...s)}})),this.on("-ipc-ports",(function(e,t,o,s,i){addSenderFrameToEvent(e),e.ports=i.map((e=>new d.MessagePortMain(e)));const a=getWebFrameForEvent(e);a&&a.ipc.emit(o,e,s),r.emit(o,e,s),n.ipcMain.emit(o,e,s)})),h.event(this,"crashed","render-process-gone",((e,t)=>[e,"killed"===t.reason])),this.on("render-process-gone",((e,t)=>{n.app.emit("render-process-gone",e,this,t),(M.hasVar("ELECTRON_ENABLE_LOGGING")||P.hasSwitch("enable-logging"))&&console.info(`Renderer process ${t.reason} - see https://www.electronjs.org/docs/tutorial/application-debugging for potential debugging information.`)})),this.on("devtools-reload-page",(function(){this.reload()})),"remote"!==this.getType()){this.on("-new-window",((e,t,r,o,n,s,i)=>{const l={url:t,frameName:r,features:n,referrer:s,postBody:i?{data:i,...(0,a.parseContentTypeFormat)(i)}:void 0,disposition:o};let c;try{c=this._callWindowOpenHandler(e,l)}catch(t){throw e.preventDefault(),t}const p=c.browserWindowConstructorOptions;e.defaultPrevented||(0,a.openGuestWindow)({embedder:e.sender,disposition:o,referrer:s,postData:i,overrideBrowserWindowOptions:p||{},windowOpenArgs:l,outlivesOpener:c.outlivesOpener})}));let e=null,t=!1;this.on("-will-add-new-contents",((r,o,n,s,i,c,p)=>{const d={url:o,frameName:n,features:s,disposition:i,referrer:c,postBody:p?{data:p,...(0,a.parseContentTypeFormat)(p)}:void 0};let u;try{u=this._callWindowOpenHandler(r,d)}catch(e){throw r.preventDefault(),e}if(t=u.outlivesOpener,e=u.browserWindowConstructorOptions,!r.defaultPrevented){const t=e?{backgroundColor:e.backgroundColor,transparent:e.transparent,...e.webPreferences}:void 0,{webPreferences:o}=(0,l.parseFeatures)(s),n=(0,a.makeWebPreferences)({embedder:r.sender,insecureParsedWebPreferences:o,secureOverrideWebPreferences:t});e={...e,webPreferences:n},this._setNextChildWebPreferences(n)}})),this.on("-add-new-contents",((r,o,n,s,i,l,c,p,d,u,h,m,f)=>{const b=e||void 0,w=t;e=null,t=!1,"foreground-tab"===n||"new-window"===n||"background-tab"===n?(0,a.openGuestWindow)({embedder:r.sender,guest:o,overrideBrowserWindowOptions:b,disposition:n,referrer:h,postData:f,windowOpenArgs:{url:d,frameName:u,features:m},outlivesOpener:w}):r.preventDefault()}))}this.on("login",((e,...t)=>{n.app.emit("login",e,this,...t)})),this.on("ready-to-show",(()=>{const e=this.getOwnerBrowserWindow();e&&!e.isDestroyed()&&process.nextTick((()=>{e.emit("ready-to-show")}))})),this.on("select-bluetooth-device",((e,t,r)=>{1===this.listenerCount("select-bluetooth-device")&&(e.preventDefault(),r(""))}));const o=process._linkedBinding("electron_browser_event").createEmpty();n.app.emit("web-contents-created",o,this),Object.defineProperty(this,"audioMuted",{get:()=>this.isAudioMuted(),set:e=>this.setAudioMuted(e)}),Object.defineProperty(this,"userAgent",{get:()=>this.getUserAgent(),set:e=>this.setUserAgent(e)}),Object.defineProperty(this,"zoomLevel",{get:()=>this.getZoomLevel(),set:e=>this.setZoomLevel(e)}),Object.defineProperty(this,"zoomFactor",{get:()=>this.getZoomFactor(),set:e=>this.setZoomFactor(e)}),Object.defineProperty(this,"frameRate",{get:()=>this.getFrameRate(),set:e=>this.setFrameRate(e)}),Object.defineProperty(this,"backgroundThrottling",{get:()=>this.getBackgroundThrottling(),set:e=>this.setBackgroundThrottling(e)})},t.create=function create(e={}){return new _(e)},t.fromId=function fromId(e){return g.fromId(e)},t.fromFrame=function fromFrame(e){return g.fromFrame(e)},t.fromDevToolsTargetId=function fromDevToolsTargetId(e){return g.fromDevToolsTargetId(e)},t.getFocusedWebContents=function getFocusedWebContents(){let e=null;for(const t of g.getAllWebContents())if(t.isFocused()&&(null==e&&(e=t),"webview"===t.getType()))return t;return e},t.getAllWebContents=function getAllWebContents(){return g.getAllWebContents()}},"./lib/browser/api/web-frame-main.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=r("./lib/browser/message-port-main.ts"),n=r("./lib/browser/ipc-main-impl.ts"),{WebFrameMain:s,fromId:i}=process._linkedBinding("electron_browser_web_frame_main");Object.defineProperty(s.prototype,"ipc",{get(){const e=new n.IpcMainImpl;return Object.defineProperty(this,"ipc",{value:e}),e}}),s.prototype.send=function(e,...t){if("string"!=typeof e)throw new Error("Missing required channel argument");try{return this._send(!1,e,t)}catch(e){console.error("Error sending from webFrameMain: ",e)}},s.prototype._sendInternal=function(e,...t){if("string"!=typeof e)throw new Error("Missing required channel argument");try{return this._send(!0,e,t)}catch(e){console.error("Error sending from webFrameMain: ",e)}},s.prototype.postMessage=function(...e){Array.isArray(e[2])&&(e[2]=e[2].map((e=>e instanceof o.MessagePortMain?e._internalPort:e))),this._postMessage(...e)},t.default={fromId:i}},"./lib/browser/default-menu.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.setDefaultApplicationMenu=t.setApplicationMenuWasSet=void 0;const o=r("./lib/browser/api/exports/electron.ts"),n=r("./lib/browser/api/exports/electron.ts"),s="darwin"===process.platform;let i=!1;t.setApplicationMenuWasSet=()=>{i=!0};t.setDefaultApplicationMenu=()=>{if(i)return;const e={role:"help",submenu:o.app.isPackaged?[]:[{label:"Learn More",click:async()=>{await n.shell.openExternal("https://electronjs.org")}},{label:"Documentation",click:async()=>{const e=process.versions.electron;await n.shell.openExternal(`https://github.com/electron/electron/tree/v${e}/docs#readme`)}},{label:"Community Discussions",click:async()=>{await n.shell.openExternal("https://discord.gg/electronjs")}},{label:"Search Issues",click:async()=>{await n.shell.openExternal("https://github.com/electron/electron/issues")}}]},t=[...s?[{role:"appMenu"}]:[],{role:"fileMenu"},{role:"editMenu"},{role:"viewMenu"},{role:"windowMenu"},e],r=o.Menu.buildFromTemplate(t);o.Menu.setApplicationMenu(r)}},"./lib/browser/devtools.ts":(e,t,r)=>{var o=r("./lib/common/webpack-globals-provider.ts").Promise;Object.defineProperty(t,"__esModule",{value:!0});const n=r("./lib/browser/api/exports/electron.ts"),s=r("fs"),i=r("url"),a=r("./lib/browser/ipc-main-internal.ts"),l=r("./lib/browser/ipc-main-internal-utils.ts"),convertToMenuTemplate=function(e,t){return e.map((function(e){const r="subMenu"===e.type?{type:"submenu",label:e.label,enabled:e.enabled,submenu:convertToMenuTemplate(e.subItems,t)}:"separator"===e.type?{type:"separator"}:"checkbox"===e.type?{type:"checkbox",label:e.label,enabled:e.enabled,checked:e.checked}:{type:"normal",label:e.label,enabled:e.enabled};return null!=e.id&&(r.click=()=>t(e.id)),r}))},assertChromeDevTools=function(e,t){const r=e.getURL();if(!function(e){const{protocol:t}=i.parse(e);return"devtools:"===t}(r))throw console.error(`Blocked ${r} from calling ${t}`),new Error(`Blocked ${t}`)};a.ipcMainInternal.handle("INSPECTOR_CONTEXT_MENU",(function(e,t,r){return new o((o=>{assertChromeDevTools(e.sender,"window.InspectorFrontendHost.showContextMenuAtPoint()");const s=r?[{role:"undo"},{role:"redo"},{type:"separator"},{role:"cut"},{role:"copy"},{role:"paste"},{role:"pasteAndMatchStyle"},{role:"delete"},{role:"selectAll"}]:convertToMenuTemplate(t,o),i=n.Menu.buildFromTemplate(s),a=e.sender.getOwnerBrowserWindow();i.popup({window:a,callback:()=>o()})}))})),a.ipcMainInternal.handle("INSPECTOR_SELECT_FILE",(async function(e){assertChromeDevTools(e.sender,"window.UI.createFileSelectorElement()");const t=await n.dialog.showOpenDialog({});if(t.canceled)return[];const r=t.filePaths[0];return[r,await s.promises.readFile(r)]})),l.handleSync("INSPECTOR_CONFIRM",(async function(e,t="",r=""){assertChromeDevTools(e.sender,"window.confirm()");const o={message:String(t),title:String(r),buttons:["OK","Cancel"],cancelId:1},s=e.sender.getOwnerBrowserWindow(),{response:i}=await n.dialog.showMessageBox(s,o);return 0===i}))},"./lib/browser/guest-view-manager.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=r("./lib/browser/api/exports/electron.ts"),n=r("./lib/browser/ipc-main-internal.ts"),s=r("./lib/browser/ipc-main-internal-utils.ts"),i=r("./lib/browser/parse-features-string.ts"),a=r("./lib/common/web-view-methods.ts"),l=r("./lib/browser/web-view-events.ts"),c=process._linkedBinding("electron_browser_web_view_manager"),p=process._linkedBinding("electron_browser_event"),d=process._linkedBinding("electron_browser_net"),u=Object.keys(l.webViewEvents),h=new Map,m=new Map;const createGuest=function(e,t,r,n){const s=function makeWebPreferences(e,t){const r="string"==typeof t.webpreferences?(0,i.parseWebViewWebPreferences)(t.webpreferences):null,o={nodeIntegration:t.nodeintegration??!1,nodeIntegrationInSubFrames:t.nodeintegrationinsubframes??!1,plugins:t.plugins,zoomFactor:e.zoomFactor,disablePopups:!t.allowpopups,webSecurity:!t.disablewebsecurity,enableBlinkFeatures:t.blinkfeatures,disableBlinkFeatures:t.disableblinkfeatures,partition:t.partition,...r};t.preload&&(o.preload=d.fileURLToFilePath(t.preload));const n=new Map([["contextIsolation",!0],["javascript",!1],["nodeIntegration",!1],["sandbox",!0],["nodeIntegrationInSubFrames",!1],["enableWebSQL",!1]]),s=e.getLastWebPreferences();for(const[e,t]of n)s[e]===t&&(o[e]=t);return o}(e,n),a=p.createWithSender(e),{instanceId:f}=n;if(e.emit("will-attach-webview",a,s,n),a.defaultPrevented)return-1;const b=o.webContents.create({...s,type:"webview",embedder:e}),w=b.id;h.set(w,{elementInstanceId:r,guest:b,embedder:e}),b.once("destroyed",(()=>{h.has(w)&&detachGuest(e,w)})),b.once("did-attach",(function(t){const r=null!=this.viewInstanceId;this.viewInstanceId=f,r||(n.src&&this.loadURL(n.src,function makeLoadURLOptions(e){const t={};return e.httpreferrer&&(t.httpReferrer=e.httpreferrer),e.useragent&&(t.userAgent=e.useragent),t}(n)),e.emit("did-attach-webview",t,b))}));const sendToEmbedder=(t,...r)=>{e.isDestroyed()||e._sendInternal(`${t}-${b.viewInstanceId}`,...r)},makeProps=(e,t)=>{const r={};return l.webViewEvents[e].forEach(((e,o)=>{r[e]=t[o]})),r};for(const e of u)b.on(e,(function(t,...r){sendToEmbedder("GUEST_VIEW_INTERNAL_DISPATCH_EVENT",e,makeProps(e,r))}));b.on("ipc-message-host",(function(e,t,r){sendToEmbedder("GUEST_VIEW_INTERNAL_DISPATCH_EVENT","ipc-message",{frameId:[e.processId,e.frameId],channel:t,args:r})})),b.on("dom-ready",(function(){const e=h.get(w);null!=e&&null!=e.visibilityState&&b._sendInternal("GUEST_INSTANCE_VISIBILITY_CHANGE",e.visibilityState)}));const g=`${e.id}-${r}`,y=m.get(g);if(null!=y){const e=h.get(y);e&&e.guest.detachFromOuterFrame()}return m.set(g,w),b.setEmbedder(e),watchEmbedder(e),c.addGuest(w,e,b,s),b.attachToIframe(e,t),w},detachGuest=function(e,t){const r=h.get(t);if(!r)return;if(e!==r.embedder)return;c.removeGuest(e,t),h.delete(t);const o=`${e.id}-${r.elementInstanceId}`;m.delete(o)},f=new Set,watchEmbedder=function(e){if(f.has(e))return;f.add(e);const onVisibilityChange=function(t){for(const r of h.values())r.visibilityState=t,r.embedder===e&&r.guest._sendInternal("GUEST_INSTANCE_VISIBILITY_CHANGE",t)};e.on("-window-visibility-change",onVisibilityChange),e.once("will-destroy",(()=>{for(const[t,r]of h)r.embedder===e&&detachGuest(e,t);e.removeListener("-window-visibility-change",onVisibilityChange),f.delete(e)}))},b=new WeakMap,makeSafeHandler=function(e,t){return(r,...o)=>{if(function(e){if(!b.has(e)){const t=e.getLastWebPreferences()||{};b.set(e,!!t.webviewTag)}return b.get(e)}(r.sender))return t(r,...o);throw console.error(`<webview> IPC message ${e} sent by WebContents with <webview> disabled (${r.sender.id})`),new Error("<webview> disabled")}},handleMessage=function(e,t){n.ipcMainInternal.handle(e,makeSafeHandler(e,t))},handleMessageSync=function(e,t){s.handleSync(e,makeSafeHandler(e,t))};handleMessage("GUEST_VIEW_MANAGER_CREATE_AND_ATTACH_GUEST",(function(e,t,r,o){return createGuest(e.sender,t,r,o)})),handleMessageSync("GUEST_VIEW_MANAGER_DETACH_GUEST",(function(e,t){return detachGuest(e.sender,t)})),n.ipcMainInternal.on("GUEST_VIEW_MANAGER_FOCUS_CHANGE",(function(e,t){e.sender.emit("-focus-change",{},t)})),handleMessage("GUEST_VIEW_MANAGER_CALL",(function(e,t,r,o){const n=getGuestForWebContents(t,e.sender);if(!a.asyncMethods.has(r))throw new Error(`Invalid method: ${r}`);return n[r](...o)})),handleMessageSync("GUEST_VIEW_MANAGER_CALL",(function(e,t,r,o){const n=getGuestForWebContents(t,e.sender);if(!a.syncMethods.has(r))throw new Error(`Invalid method: ${r}`);return n[r](...o)})),handleMessageSync("GUEST_VIEW_MANAGER_PROPERTY_GET",(function(e,t,r){const o=getGuestForWebContents(t,e.sender);if(!a.properties.has(r))throw new Error(`Invalid property: ${r}`);return o[r]})),handleMessageSync("GUEST_VIEW_MANAGER_PROPERTY_SET",(function(e,t,r,o){const n=getGuestForWebContents(t,e.sender);if(!a.properties.has(r))throw new Error(`Invalid property: ${r}`);n[r]=o}));const getGuestForWebContents=function(e,t){const r=h.get(e);if(!r)throw new Error(`Invalid guestInstanceId: ${e}`);if(r.guest.hostWebContents!==t)throw new Error(`Access denied to guestInstanceId: ${e}`);return r.guest}},"./lib/browser/guest-window-manager.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseContentTypeFormat=t.makeWebPreferences=t.openGuestWindow=void 0;const o=r("./lib/browser/api/exports/electron.ts"),n=r("./lib/browser/parse-features-string.ts"),s=new Map,unregisterFrameName=e=>s.delete(e);t.openGuestWindow=function openGuestWindow({embedder:e,guest:t,referrer:r,disposition:i,postData:a,overrideBrowserWindowOptions:l,windowOpenArgs:c,outlivesOpener:p}){const{url:d,frameName:u,features:h}=c,{options:m}=(0,n.parseFeatures)(h),f={show:!0,width:800,height:600,...m,...l},b=(e=>s.get(e))(u);if(b){if(!b.isDestroyed()&&!b.webContents.isDestroyed())return b.loadURL(d),b;unregisterFrameName(u)}const w=new o.BrowserWindow({webContents:t,...f});return t||w.loadURL(d,{httpReferrer:r,...a&&{postData:a,extraHeaders:formatPostDataHeaders(a)}}),handleWindowLifecycleEvents({embedder:e,frameName:u,guest:w,outlivesOpener:p}),e.emit("did-create-window",w,{url:d,frameName:u,options:f,disposition:i,referrer:r,postData:a}),w};const handleWindowLifecycleEvents=function({embedder:e,guest:t,frameName:r,outlivesOpener:o}){const closedByEmbedder=function(){t.removeListener("closed",closedByUser),t.destroy()},closedByUser=function(){e.isDestroyed()||o||e.removeListener("current-render-view-deleted",closedByEmbedder)};o||e.once("current-render-view-deleted",closedByEmbedder),t.once("closed",closedByUser),r&&(((e,t)=>{s.set(e,t)})(r,t),t.once("closed",(function(){unregisterFrameName(r)})))},i={contextIsolation:!0,javascript:!1,nodeIntegration:!1,sandbox:!0,webviewTag:!1,nodeIntegrationInSubFrames:!1,enableWebSQL:!1};function formatPostDataHeaders(e){if(!e)return;const{contentType:r,boundary:o}=(0,t.parseContentTypeFormat)(e);return null!=o?`content-type: ${r}; boundary=${o}`:`content-type: ${r}`}t.makeWebPreferences=function makeWebPreferences({embedder:e,secureOverrideWebPreferences:t={},insecureParsedWebPreferences:r={}}){const o=e.getLastWebPreferences();return{...r,...Object.keys(i).reduce(((e,t)=>(i[t]===o[t]&&(e[t]=o[t]),e)),{}),...t}};t.parseContentTypeFormat=function(e){if(e.length&&"rawData"===e[0].type){const t=e[0].bytes.toString(),r=/^--.*[^-\r\n]/.exec(t);if(r)return{boundary:r[0].substr(2),contentType:"multipart/form-data"}}return{contentType:"application/x-www-form-urlencoded"}}},"./lib/browser/ipc-main-impl.ts":(e,t,r)=>{var o=r("./lib/common/webpack-globals-provider.ts").Promise;Object.defineProperty(t,"__esModule",{value:!0}),t.IpcMainImpl=void 0;const n=r("events");class IpcMainImpl extends n.EventEmitter{constructor(){super(),this._invokeHandlers=new Map,this.handle=(e,t)=>{if(this._invokeHandlers.has(e))throw new Error(`Attempted to register a second handler for '${e}'`);if("function"!=typeof t)throw new Error(`Expected handler to be a function, but found type '${typeof t}'`);this._invokeHandlers.set(e,(async(e,...r)=>{try{e._reply(await o.resolve(t(e,...r)))}catch(t){e._throw(t)}}))},this.handleOnce=(e,t)=>{this.handle(e,((r,...o)=>(this.removeHandler(e),t(r,...o))))},this.on("error",(()=>{}))}removeHandler(e){this._invokeHandlers.delete(e)}}t.IpcMainImpl=IpcMainImpl},"./lib/browser/ipc-main-internal-utils.ts":(e,t,r)=>{var o=r("./lib/common/webpack-globals-provider.ts").Promise;Object.defineProperty(t,"__esModule",{value:!0}),t.invokeInWebContents=t.handleSync=void 0;const n=r("./lib/browser/ipc-main-internal.ts");t.handleSync=function(e,t){n.ipcMainInternal.on(e,(async(e,...r)=>{try{e.returnValue=[null,await t(e,...r)]}catch(t){e.returnValue=[t]}}))};let s=0;t.invokeInWebContents=function invokeInWebContents(e,t,...r){return new o(((o,i)=>{const a=++s,l=`${t}_RESPONSE_${a}`;n.ipcMainInternal.on(l,(function handler(r,s,a){r.sender===e?(n.ipcMainInternal.removeListener(l,handler),s?i(s):o(a)):console.error(`Reply to ${t} sent by unexpected WebContents (${r.sender.id})`)})),e._sendInternal(t,a,...r)}))}},"./lib/browser/ipc-main-internal.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ipcMainInternal=void 0;const o=r("./lib/browser/ipc-main-impl.ts");t.ipcMainInternal=new o.IpcMainImpl},"./lib/browser/message-port-main.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MessagePortMain=void 0;const o=r("events");class MessagePortMain extends o.EventEmitter{constructor(e){super(),this._internalPort=e,this._internalPort.emit=(e,t)=>{"message"===e&&(t={...t,ports:t.ports.map((e=>new MessagePortMain(e)))}),this.emit(e,t)}}start(){return this._internalPort.start()}close(){return this._internalPort.close()}postMessage(...e){return Array.isArray(e[1])&&(e[1]=e[1].map((e=>e instanceof MessagePortMain?e._internalPort:e))),this._internalPort.postMessage(...e)}}t.MessagePortMain=MessagePortMain},"./lib/browser/parse-features-string.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseFeatures=t.parseWebViewWebPreferences=t.parseCommaSeparatedKeyValue=void 0;const r=["top","left",...Object.keys({x:!0,y:!0,width:!0,height:!0,minWidth:!0,maxWidth:!0,minHeight:!0,maxHeight:!0,opacity:!0})];function coerce(e,t){if(r.includes(e))return parseInt(t,10);switch(t){case"true":case"1":case"yes":case void 0:return!0;case"false":case"0":case"no":return!1;default:return t}}function parseCommaSeparatedKeyValue(e){const t={};for(const r of e.split(",")){const[e,o]=r.split("=").map((e=>e.trim()));e&&(t[e]=coerce(e,o))}return t}t.parseCommaSeparatedKeyValue=parseCommaSeparatedKeyValue,t.parseWebViewWebPreferences=function parseWebViewWebPreferences(e){return parseCommaSeparatedKeyValue(e)};const o=["zoomFactor","nodeIntegration","javascript","contextIsolation","webviewTag"];t.parseFeatures=function parseFeatures(e){const t=parseCommaSeparatedKeyValue(e),r={};return o.forEach((e=>{void 0!==t[e]&&(r[e]=t[e],delete t[e])})),void 0!==t.left&&(t.x=t.left),void 0!==t.top&&(t.y=t.top),{options:t,webPreferences:r}}},"./lib/browser/rpc-server.ts":(e,t,r)=>{var o=r("./lib/common/webpack-globals-provider.ts").Promise;Object.defineProperty(t,"__esModule",{value:!0});const n=r("./lib/browser/api/exports/electron.ts"),s=r("fs"),i=r("./lib/browser/ipc-main-internal.ts"),a=r("./lib/browser/ipc-main-internal-utils.ts");i.ipcMainInternal.on("BROWSER_WINDOW_CLOSE",(function(e){const t=e.sender.getOwnerBrowserWindow();t&&t.close(),e.returnValue=null})),i.ipcMainInternal.handle("BROWSER_GET_LAST_WEB_PREFERENCES",(function(e){return e.sender.getLastWebPreferences()})),i.ipcMainInternal.handle("BROWSER_GET_PROCESS_MEMORY_INFO",(function(e){return e.sender._getProcessMemoryInfo()}));const l=(()=>{switch(process.platform){case"darwin":return new Set(["readFindText","writeFindText"]);case"linux":return new Set(Object.keys(n.clipboard));default:return new Set}})();a.handleSync("BROWSER_CLIPBOARD_SYNC",(function(e,t,...r){if(!l.has(t))throw new Error(`Invalid method: ${t}`);return n.clipboard[t](...r)}));a.handleSync("BROWSER_SANDBOX_LOAD",(async function(e){const t=e.sender._getPreloadPaths();return{preloadScripts:await o.all(t.map((e=>async function(e){let t=null,r=null;try{t=await s.promises.readFile(e,"utf8")}catch(e){r=e}return{preloadPath:e,preloadSrc:t,preloadError:r}}(e)))),process:{arch:process.arch,platform:process.platform,env:{...process.env},version:process.version,versions:process.versions,execPath:process.helperExecPath}}})),a.handleSync("BROWSER_NONSANDBOX_LOAD",(function(e){return{preloadPaths:e.sender._getPreloadPaths()}})),i.ipcMainInternal.on("BROWSER_PRELOAD_ERROR",(function(e,t,r){e.sender.emit("preload-error",e,t,r)}))},"./lib/browser/web-view-events.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.webViewEvents=void 0,t.webViewEvents={"load-commit":["url","isMainFrame"],"did-attach":[],"did-finish-load":[],"did-fail-load":["errorCode","errorDescription","validatedURL","isMainFrame","frameProcessId","frameRoutingId"],"did-frame-finish-load":["isMainFrame","frameProcessId","frameRoutingId"],"did-start-loading":[],"did-stop-loading":[],"dom-ready":[],"console-message":["level","message","line","sourceId"],"context-menu":["params"],"devtools-opened":[],"devtools-closed":[],"devtools-focused":[],"will-navigate":["url"],"did-start-navigation":["url","isInPlace","isMainFrame","frameProcessId","frameRoutingId"],"did-redirect-navigation":["url","isInPlace","isMainFrame","frameProcessId","frameRoutingId"],"did-navigate":["url","httpResponseCode","httpStatusText"],"did-frame-navigate":["url","httpResponseCode","httpStatusText","isMainFrame","frameProcessId","frameRoutingId"],"did-navigate-in-page":["url","isMainFrame","frameProcessId","frameRoutingId"],"-focus-change":["focus"],close:[],crashed:[],"render-process-gone":["details"],"plugin-crashed":["name","version"],destroyed:[],"page-title-updated":["title","explicitSet"],"page-favicon-updated":["favicons"],"enter-html-full-screen":[],"leave-html-full-screen":[],"media-started-playing":[],"media-paused":[],"found-in-page":["result"],"did-change-theme-color":["themeColor"],"update-target-url":["url"]}},"./lib/common/api/clipboard.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=process._linkedBinding("electron_common_clipboard");if("renderer"===process.type){const e=r("./lib/renderer/ipc-renderer-internal-utils.ts"),makeRemoteMethod=function(t){return(...r)=>e.invokeSync("BROWSER_CLIPBOARD_SYNC",t,...r)};if("linux"===process.platform)for(const e of Object.keys(o))o[e]=makeRemoteMethod(e);else"darwin"===process.platform&&(o.readFindText=makeRemoteMethod("readFindText"),o.writeFindText=makeRemoteMethod("writeFindText"))}t.default=o},"./lib/common/api/module-list.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.commonModuleList=void 0,t.commonModuleList=[{name:"clipboard",loader:()=>r("./lib/common/api/clipboard.ts")},{name:"nativeImage",loader:()=>r("./lib/common/api/native-image.ts")},{name:"shell",loader:()=>r("./lib/common/api/shell.ts")}]},"./lib/common/api/native-image.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});const{nativeImage:r}=process._linkedBinding("electron_common_native_image");t.default=r},"./lib/common/api/shell.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});const r=process._linkedBinding("electron_common_shell");t.default=r},"./lib/common/define-properties.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defineProperties=void 0;const handleESModule=e=>()=>{const t=e();return t.__esModule&&t.default?t.default:t};t.defineProperties=function defineProperties(e,t){const r={};for(const e of t)r[e.name]={enumerable:!0,get:handleESModule(e.loader)};return Object.defineProperties(e,r)}},"./lib/common/deprecate.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.moveAPI=t.renameProperty=t.removeProperty=t.event=t.renameFunction=t.removeFunction=t.log=t.warn=t.getHandler=t.setHandler=t.warnOnceMessage=t.warnOnce=void 0;let r=null;function warnOnce(e,t){return warnOnceMessage(t?`'${e}' is deprecated and will be removed. Please use '${t}' instead.`:`'${e}' is deprecated and will be removed.`)}function warnOnceMessage(e){let t=!1;return()=>{t||process.noDeprecation||(t=!0,log(e))}}function log(e){if("function"!=typeof r){if(process.throwDeprecation)throw new Error(e);return process.traceDeprecation?console.trace(e):console.warn(`(electron) ${e}`)}r(e)}t.warnOnce=warnOnce,t.warnOnceMessage=warnOnceMessage,t.setHandler=function setHandler(e){r=e},t.getHandler=function getHandler(){return r},t.warn=function warn(e,t){process.noDeprecation||log(`'${e}' is deprecated. Use '${t}' instead.`)},t.log=log,t.removeFunction=function removeFunction(e,t){if(!e)throw Error(`'${t} function' is invalid or does not exist.`);const r=warnOnce(`${e.name} function`);return function(){r(),e.apply(this,arguments)}},t.renameFunction=function renameFunction(e,t){const r=warnOnce(`${e.name} function`,`${t} function`);return function(){return r(),e.apply(this,arguments)}},t.event=function event(e,t,r,o=((...e)=>e)){const n=r.startsWith("-")?warnOnce(`${t} event`):warnOnce(`${t} event`,`${r} event`);return e.on(r,(function(...e){if(0!==this.listenerCount(t)){n();const r=o(...e);r&&this.emit(t,...r)}}))},t.removeProperty=function removeProperty(e,t,r){const o=Object.getOwnPropertyDescriptor(e.__proto__,t);if(!o)return log(`Unable to remove property '${t}' from an object that lacks it.`),e;if(!o.get||!o.set)return log(`Unable to remove property '${t}' from an object does not have a getter / setter`),e;const n=warnOnce(t);return Object.defineProperty(e,t,{configurable:!0,get:()=>(n(),o.get.call(e)),set:t=>(r&&!r.includes(t)||n(),o.set.call(e,t))})},t.renameProperty=function renameProperty(e,t,r){const o=warnOnce(t,r);return t in e&&!(r in e)&&(o(),e[r]=e[t]),Object.defineProperty(e,t,{get:()=>(o(),e[r]),set:t=>{o(),e[r]=t}})},t.moveAPI=function moveAPI(e,t,r){const o=warnOnce(t,r);return function(){return o(),e.apply(this,arguments)}}},"./lib/common/init.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=r("util"),n=r("timers"),wrapWithActivateUvLoop=function(e){return function wrap(e,t){const r=t(e);e[o.promisify.custom]&&(r[o.promisify.custom]=t(e[o.promisify.custom]));return r}(e,(function(e){return function(...t){return process.activateUvLoop(),e.apply(this,t)}}))};if(process.nextTick=wrapWithActivateUvLoop(process.nextTick),global.setImmediate=n.setImmediate=wrapWithActivateUvLoop(n.setImmediate),global.clearImmediate=n.clearImmediate,n.setTimeout=wrapWithActivateUvLoop(n.setTimeout),n.setInterval=wrapWithActivateUvLoop(n.setInterval),"browser"!==process.type&&"utility"!==process.type||(global.setTimeout=n.setTimeout,global.setInterval=n.setInterval),"win32"===process.platform){const{Readable:e}=r("stream"),t=new e;t.push(null),Object.defineProperty(process,"stdin",{configurable:!1,enumerable:!0,get:()=>t})}},"./lib/common/reset-search-paths.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=r("path"),n=r("module");if("renderer"===process.type){const e=n._load;n._load=function(t){return"vm"===t&&console.warn("The vm module of Node.js is deprecated in the renderer process and will be removed."),e.apply(this,arguments)}}const s=process.resourcesPath+o.sep,i=n._nodeModulePaths;n._nodeModulePaths=function(e){const t=i(e);return(o.resolve(e)+o.sep).startsWith(s)?t.filter((function(e){return e.startsWith(s)})):t};const makeElectronModule=e=>{const t=new n("electron",null);t.id="electron",t.loaded=!0,t.filename=e,Object.defineProperty(t,"exports",{get:()=>r("./lib/browser/api/exports/electron.ts")}),n._cache[e]=t};makeElectronModule("electron"),makeElectronModule("electron/common"),"browser"===process.type&&makeElectronModule("electron/main"),"renderer"===process.type&&makeElectronModule("electron/renderer");const a=n._resolveFilename;n._resolveFilename=function(e,t,r,o){return"electron"===e||e.startsWith("electron/")?"electron":a(e,t,r,o)}},"./lib/common/web-view-methods.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.asyncMethods=t.properties=t.syncMethods=void 0,t.syncMethods=new Set(["getURL","getTitle","isLoading","isLoadingMainFrame","isWaitingForResponse","stop","reload","reloadIgnoringCache","canGoBack","canGoForward","canGoToOffset","clearHistory","goBack","goForward","goToIndex","goToOffset","isCrashed","setUserAgent","getUserAgent","openDevTools","closeDevTools","isDevToolsOpened","isDevToolsFocused","inspectElement","setAudioMuted","isAudioMuted","isCurrentlyAudible","undo","redo","cut","copy","paste","pasteAndMatchStyle","delete","selectAll","unselect","replace","replaceMisspelling","findInPage","stopFindInPage","downloadURL","inspectSharedWorker","inspectServiceWorker","showDefinitionForSelection","getZoomFactor","getZoomLevel","setZoomFactor","setZoomLevel"]),t.properties=new Set(["audioMuted","userAgent","zoomLevel","zoomFactor","frameRate"]),t.asyncMethods=new Set(["capturePage","loadURL","executeJavaScript","insertCSS","insertText","removeInsertedCSS","send","sendToFrame","sendInputEvent","setLayoutZoomLevelLimits","setVisualZoomLevelLimits","print","printToPDF"])},"./lib/common/webpack-globals-provider.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Promise=void 0,t.Promise=global.Promise},"./lib/renderer/ipc-renderer-internal-utils.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.invokeSync=t.handle=void 0;const o=r("./lib/renderer/ipc-renderer-internal.ts");t.handle=function(e,t){o.ipcRendererInternal.on(e,(async(r,o,...n)=>{const s=`${e}_RESPONSE_${o}`;try{r.sender.send(s,null,await t(r,...n))}catch(e){r.sender.send(s,e)}}))},t.invokeSync=function invokeSync(e,...t){const[r,n]=o.ipcRendererInternal.sendSync(e,...t);if(r)throw r;return n}},"./lib/renderer/ipc-renderer-internal.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ipcRendererInternal=void 0;const o=r("events"),{ipc:n}=process._linkedBinding("electron_renderer_ipc"),s=!0;t.ipcRendererInternal=new o.EventEmitter,t.ipcRendererInternal.send=function(e,...t){return n.send(s,e,t)},t.ipcRendererInternal.sendSync=function(e,...t){return n.sendSync(s,e,t)},t.ipcRendererInternal.invoke=async function(e,...t){const{error:r,result:o}=await n.invoke(s,e,t);if(r)throw new Error(`Error invoking remote method '${e}': ${r}`);return o}},child_process:e=>{e.exports=require("child_process")},events:e=>{e.exports=require("events")},fs:e=>{e.exports=require("fs")},module:e=>{e.exports=require("module")},net:e=>{e.exports=require("net")},path:e=>{e.exports=require("path")},stream:e=>{e.exports=require("stream")},timers:e=>{e.exports=require("timers")},url:e=>{e.exports=require("url")},util:e=>{e.exports=require("util")},v8:e=>{e.exports=require("v8")}},t={};function __webpack_require__(r){var o=t[r];if(void 0!==o)return o.exports;var n=t[r]={exports:{}};return e[r].call(n.exports,n,n.exports,__webpack_require__),n.exports}var r={};(()=>{var e=r,t=__webpack_require__("./lib/common/webpack-globals-provider.ts").Promise;Object.defineProperty(e,"__esModule",{value:!0});const o=__webpack_require__("events"),n=__webpack_require__("fs"),s=__webpack_require__("path"),i=__webpack_require__("module");process.argv.splice(1,1),__webpack_require__("./lib/common/reset-search-paths.ts"),__webpack_require__("./lib/common/init.ts"),process._linkedBinding("electron_browser_event_emitter").setEventEmitterPrototype(o.EventEmitter.prototype),process.on("uncaughtException",(function(e){process.listenerCount("uncaughtException")>1||t.resolve().then((()=>__webpack_require__("./lib/browser/api/exports/electron.ts"))).then((({dialog:t})=>{const r="Uncaught Exception:\n"+(e.stack?e.stack:`${e.name}: ${e.message}`);t.showErrorBox("A JavaScript error occurred in the main process",r)}))}));const{app:a}=__webpack_require__("./lib/browser/api/exports/electron.ts");if(a.on("quit",((e,t)=>{process.emit("exit",t)})),"win32"===process.platform){const e=s.join(s.dirname(process.execPath),"..","update.exe");if(n.existsSync(e)){const t=s.dirname(s.resolve(e)),r=s.basename(t).replace(/\s/g,""),o=s.basename(process.execPath).replace(/\.exe$/i,"").replace(/\s/g,"");a.setAppUserModelId(`com.squirrel.${r}.${o}`)}}process.exit=a.exit,__webpack_require__("./lib/browser/rpc-server.ts"),__webpack_require__("./lib/browser/guest-view-manager.ts");const l=process._linkedBinding("electron_common_v8_util");let c=null,p=null;const d=l.getHiddenValue(global,"appSearchPaths"),u=l.getHiddenValue(global,"appSearchPathsOnlyLoadASAR"),h=process._getOrCreateArchive;if(delete process._getOrCreateArchive,process.resourcesPath)for(c of d)try{if(c=s.join(process.resourcesPath,c),u&&!h?.(c))continue;p=i._load(s.join(c,"package.json"));break}catch{continue}if(null==p)throw process.nextTick((function(){return process.exit(1)})),new Error("Unable to find a valid app");null!=p.version&&a.setVersion(p.version),null!=p.productName?a.name=`${p.productName}`.trim():null!=p.name&&(a.name=`${p.name}`.trim()),null!=p.desktopName?a.setDesktopName(p.desktopName):a.setDesktopName(`${a.name}.desktop`),null!=p.v8Flags&&__webpack_require__("v8").setFlagsFromString(p.v8Flags),a.setAppPath(c),__webpack_require__("./lib/browser/devtools.ts"),__webpack_require__("./lib/browser/api/protocol.ts"),__webpack_require__("./lib/browser/api/web-contents.ts"),__webpack_require__("./lib/browser/api/web-frame-main.ts");const m=p.main||"index.js",f=["Pantheon","Unity:Unity7","pop:GNOME"];process.env.ORIGINAL_XDG_CURRENT_DESKTOP=process.env.XDG_CURRENT_DESKTOP,function currentPlatformSupportsAppIndicator(){if("linux"!==process.platform)return!1;const e=process.env.XDG_CURRENT_DESKTOP;return!!e&&(!!f.includes(e)||!!/ubuntu/gi.test(e))}()&&(process.env.XDG_CURRENT_DESKTOP="Unity"),a.on("window-all-closed",(()=>{1===a.listenerCount("window-all-closed")&&a.quit()}));const{setDefaultApplicationMenu:b}=__webpack_require__("./lib/browser/default-menu.ts");a.once("will-finish-launching",b),c?(process._firstFileName=i._resolveFilename(s.join(c,m),null,!1),i._load(s.join(c,m),i,!0)):(console.error("Failed to locate a valid package to load (app, app.asar or default_app.asar)"),console.error("This normally means you've damaged the Electron package somehow"))})()})();electron/js2c/browser_init