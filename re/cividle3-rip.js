!function(e){const{protocol:t}=i.parse(e);return"devtools:"===t}(r))throw console.error(`Blocked ${r} from calling ${t}`),new Error(`Blocked ${t}`)};a.ipcMainInternal.handle("INSPECTOR_CONTEXT_MENU",(function(e,t,r){return new o((o=>{assertChromeDevTools(e.sender,"window.InspectorFrontendHost.showContextMenuAtPoint()");const s=r?[{role:"undo"},{role:"redo"},{type:"separator"},{role:"cut"},{role:"copy"},{role:"paste"},{role:"pasteAndMatchStyle"},{role:"delete"},{role:"selectAll"}]:convertToMenuTemplate(t,o),i=n.Menu.buildFromTemplate(s),a=e.sender.getOwnerBrowserWindow();i.popup({window:a,callback:()=>o()})}))})),a.ipcMainInternal.handle("INSPECTOR_SELECT_FILE",(async function(e){assertChromeDevTools(e.sender,"window.UI.createFileSelectorElement()");const t=await n.dialog.showOpenDialog({});if(t.canceled)return[];const r=t.filePaths[0];return[r,await s.promises.readFile(r)]})),l.handleSync("INSPECTOR_CONFIRM",(async function(e,t="",r=""){assertChromeDevTools(e.sender,"window.confirm()");const o={message:String(t),title:String(r),buttons:["OK","Cancel"],cancelId:1},s=e.sender.getOwnerBrowserWindow(),{response:i}=await n.dialog.showMessageBox(s,o);return 0===i}))},"./lib/browser/guest-view-manager.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=r("./lib/browser/api/exports/electron.ts"),n=r("./lib/browser/ipc-main-internal.ts"),s=r("./lib/browser/ipc-main-internal-utils.ts"),i=r("./lib/browser/parse-features-string.ts"),a=r("./lib/common/web-view-methods.ts"),l=r("./lib/browser/web-view-events.ts"),c=process._linkedBinding("electron_browser_web_view_manager"),p=process._linkedBinding("electron_browser_event"),d=process._linkedBinding("electron_browser_net"),u=Object.keys(l.webViewEvents),h=new Map,m=new Map;const createGuest=function(e,t,r,n){const s=function makeWebPreferences(e,t){const r="string"==typeof t.webpreferences?(0,i.parseWebViewWebPreferences)(t.webpreferences):null,o={nodeIntegration:t.nodeintegration??!1,nodeIntegrationInSubFrames:t.nodeintegrationinsubframes??!1,plugins:t.plugins,zoomFactor:e.zoomFactor,disablePopups:!t.allowpopups,webSecurity:!t.disablewebsecurity,enableBlinkFeatures:t.blinkfeatures,disableBlinkFeatures:t.disableblinkfeatures,partition:t.partition,...r};t.preload&&(o.preload=d.fileURLToFilePath(t.preload));const n=new Map([["contextIsolation",!0],["javascript",!1],["nodeIntegration",!1],["sandbox",!0],["nodeIntegrationInSubFrames",!1],["enableWebSQL",!1]]),s=e.getLastWebPreferences();for(const[e,t]of n)s[e]===t&&(o[e]=t);return o}(e,n),a=p.createWithSender(e),{instanceId:f}=n;if(e.emit("will-attach-webview",a,s,n),a.defaultPrevented)return-1;const b=o.webContents.create({...s,type:"webview",embedder:e}),w=b.id;h.set(w,{elementInstanceId:r,guest:b,embedder:e}),b.once("destroyed",(()=>{h.has(w)&&detachGuest(e,w)})),b.once("did-attach",(function(t){const r=null!=this.viewInstanceId;this.viewInstanceId=f,r||(n.src&&this.loadURL(n.src,function makeLoadURLOptions(e){const t={};return e.httpreferrer&&(t.httpReferrer=e.httpreferrer),e.useragent&&(t.userAgent=e.useragent),t}(n)),e.emit("did-attach-webview",t,b))}));const sendToEmbedder=(t,...r)=>{e.isDestroyed()||e._sendInternal(`${t}-${b.viewInstanceId}`,...r)},makeProps=(e,t)=>{const r={};return l.webViewEvents[e].forEach(((e,o)=>{r[e]=t[o]})),r};for(const e of u)b.on(e,(function(t,...r){sendToEmbedder("GUEST_VIEW_INTERNAL_DISPATCH_EVENT",e,makeProps(e,r))}));b.on("ipc-message-host",(function(e,t,r){sendToEmbedder("GUEST_VIEW_INTERNAL_DISPATCH_EVENT","ipc-message",{frameId:[e.processId,e.frameId],channel:t,args:r})})),b.on("dom-ready",(function(){const e=h.get(w);null!=e&&null!=e.visibilityState&&b._sendInternal("GUEST_INSTANCE_VISIBILITY_CHANGE",e.visibilityState)}));const g=`${e.id}-${r}`,y=m.get(g);if(null!=y){const e=h.get(y);e&&e.guest.detachFromOuterFrame()}return m.set(g,w),b.setEmbedder(e),watchEmbedder(e),c.addGuest(w,e,b,s),b.attachToIframe(e,t),w},detachGuest=function(e,t){const r=h.get(t);if(!r)return;if(e!==r.embedder)return;c.removeGuest(e,t),h.delete(t);const o=`${e.id}-${r.elementInstanceId}`;m.delete(o)},f=new Set,watchEmbedder=function(e){if(f.has(e))return;f.add(e);const onVisibilityChange=function(t){for(const r of h.values())r.visibilityState=t,r.embedder===e&&r.guest._sendInternal("GUEST_INSTANCE_VISIBILITY_CHANGE",t)};e.on("-window-visibility-change",onVisibilityChange),e.once("will-destroy",(()=>{for(const[t,r]of h)r.embedder===e&&detachGuest(e,t);e.removeListener("-window-visibility-change",onVisibilityChange),f.delete(e)}))},b=new WeakMap,makeSafeHandler=function(e,t){return(r,...o)=>{if(function(e){if(!b.has(e)){const t=e.getLastWebPreferences()||{};b.set(e,!!t.webviewTag)}return b.get(e)}(r.sender))return t(r,...o);throw console.error(`<webview> IPC message ${e} sent by WebContents with <webview> disabled (${r.sender.id})`),new Error("<webview> disabled")}},handleMessage=function(e,t){n.ipcMainInternal.handle(e,makeSafeHandler(e,t))},handleMessageSync=function(e,t){s.handleSync(e,makeSafeHandler(e,t))};handleMessage("GUEST_VIEW_MANAGER_CREATE_AND_ATTACH_GUEST",(function(e,t,r,o){return createGuest(e.sender,t,r,o)})),handleMessageSync("GUEST_VIEW_MANAGER_DETACH_GUEST",(function(e,t){return detachGuest(e.sender,t)})),n.ipcMainInternal.on("GUEST_VIEW_MANAGER_FOCUS_CHANGE",(function(e,t){e.sender.emit("-focus-change",{},t)})),handleMessage("GUEST_VIEW_MANAGER_CALL",(function(e,t,r,o){const n=getGuestForWebContents(t,e.sender);if(!a.asyncMethods.has(r))throw new Error(`Invalid method: ${r}`);return n[r](...o)})),handleMessageSync("GUEST_VIEW_MANAGER_CALL",(function(e,t,r,o){const n=getGuestForWebContents(t,e.sender);if(!a.syncMethods.has(r))throw new Error(`Invalid method: ${r}`);return n[r](...o)})),handleMessageSync("GUEST_VIEW_MANAGER_PROPERTY_GET",(function(e,t,r){const o=getGuestForWebContents(t,e.sender);if(!a.properties.has(r))throw new Error(`Invalid property: ${r}`);return o[r]})),handleMessageSync("GUEST_VIEW_MANAGER_PROPERTY_SET",(function(e,t,r,o){const n=getGuestForWebContents(t,e.sender);if(!a.properties.has(r))throw new Error(`Invalid property: ${r}`);n[r]=o}));const getGuestForWebContents=function(e,t){const r=h.get(e);if(!r)throw new Error(`Invalid guestInstanceId: ${e}`);if(r.guest.hostWebContents!==t)throw new Error(`Access denied to guestInstanceId: ${e}`);return r.guest}},"./lib/browser/guest-window-manager.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseContentTypeFormat=t.makeWebPreferences=t.openGuestWindow=void 0;const o=r("./lib/browser/api/exports/electron.ts"),n=r("./lib/browser/parse-features-string.ts"),s=new Map,unregisterFrameName=e=>s.delete(e);t.openGuestWindow=function openGuestWindow({embedder:e,guest:t,referrer:r,disposition:i,postData:a,overrideBrowserWindowOptions:l,windowOpenArgs:c,outlivesOpener:p}){const{url:d,frameName:u,features:h}=c,{options:m}=(0,n.parseFeatures)(h),f={show:!0,width:800,height:600,...m,...l},b=(e=>s.get(e))(u);if(b){if(!b.isDestroyed()&&!b.webContents.isDestroyed())return b.loadURL(d),b;unregisterFrameName(u)}const w=new o.BrowserWindow({webContents:t,...f});return t||w.loadURL(d,{httpReferrer:r,...a&&{postData:a,extraHeaders:formatPostDataHeaders(a)}}),handleWindowLifecycleEvents({embedder:e,frameName:u,guest:w,outlivesOpener:p}),e.emit("did-create-window",w,{url:d,frameName:u,options:f,disposition:i,referrer:r,postData:a}),w};const handleWindowLifecycleEvents=function({embedder:e,guest:t,frameName:r,outlivesOpener:o}){const closedByEmbedder=function(){t.removeListener("closed",closedByUser),t.destroy()},closedByUser=function(){e.isDestroyed()||o||e.removeListener("current-render-view-deleted",closedByEmbedder)};o||e.once("current-render-view-deleted",closedByEmbedder),t.once("closed",closedByUser),r&&(((e,t)=>{s.set(e,t)})(r,t),t.once("closed",(function(){unregisterFrameName(r)})))},i={contextIsolation:!0,javascript:!1,nodeIntegration:!1,sandbox:!0,webviewTag:!1,nodeIntegrationInSubFrames:!1,enableWebSQL:!1};function formatPostDataHeaders(e){if(!e)return;const{contentType:r,boundary:o}=(0,t.parseContentTypeFormat)(e);return null!=o?`content-type: ${r}; boundary=${o}`:`content-type: ${r}`}t.makeWebPreferences=function makeWebPreferences({embedder:e,secureOverrideWebPreferences:t={},insecureParsedWebPreferences:r={}}){const o=e.getLastWebPreferences();return{...r,...Object.keys(i).reduce(((e,t)=>(i[t]===o[t]&&(e[t]=o[t]),e)),{}),...t}};t.parseContentTypeFormat=function(e){if(e.length&&"rawData"===e[0].type){const t=e[0].bytes.toString(),r=/^--.*[^-\r\n]/.exec(t);if(r)return{boundary:r[0].substr(2),contentType:"multipart/form-data"}}return{contentType:"application/x-www-form-urlencoded"}}},"./lib/browser/ipc-main-impl.ts":(e,t,r)=>{var o=r("./lib/common/webpack-globals-provider.ts").Promise;Object.defineProperty(t,"__esModule",{value:!0}),t.IpcMainImpl=void 0;const n=r("events");class IpcMainImpl extends n.EventEmitter{constructor(){super(),this._invokeHandlers=new Map,this.handle=(e,t)=>{if(this._invokeHandlers.has(e))throw new Error(`Attempted to register a second handler for '${e}'`);if("function"!=typeof t)throw new Error(`Expected handler to be a function, but found type '${typeof t}'`);this._invokeHandlers.set(e,(async(e,...r)=>{try{e._reply(await o.resolve(t(e,...r)))}catch(t){e._throw(t)}}))},this.handleOnce=(e,t)=>{this.handle(e,((r,...o)=>(this.removeHandler(e),t(r,...o))))},this.on("error",(()=>{}))}removeHandler(e){this._invokeHandlers.delete(e)}}t.IpcMainImpl=IpcMainImpl},"./lib/browser/ipc-main-internal-utils.ts":(e,t,r)=>{var o=r("./lib/common/webpack-globals-provider.ts").Promise;Object.defineProperty(t,"__esModule",{value:!0}),t.invokeInWebContents=t.handleSync=void 0;const n=r("./lib/browser/ipc-main-internal.ts");t.handleSync=function(e,t){n.ipcMainInternal.on(e,(async(e,...r)=>{try{e.returnValue=[null,await t(e,...r)]}catch(t){e.returnValue=[t]}}))};let s=0;t.invokeInWebContents=function invokeInWebContents(e,t,...r){return new o(((o,i)=>{const a=++s,l=`${t}_RESPONSE_${a}`;n.ipcMainInternal.on(l,(function handler(r,s,a){r.sender===e?(n.ipcMainInternal.removeListener(l,handler),s?i(s):o(a)):console.error(`Reply to ${t} sent by unexpected WebContents (${r.sender.id})`)})),e._sendInternal(t,a,...r)}))}},"./lib/browser/ipc-main-internal.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ipcMainInternal=void 0;const o=r("./lib/browser/ipc-main-impl.ts");t.ipcMainInternal=new o.IpcMainImpl},"./lib/browser/message-port-main.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MessagePortMain=void 0;const o=r("events");class MessagePortMain extends o.EventEmitter{constructor(e){super(),this._internalPort=e,this._internalPort.emit=(e,t)=>{"message"===e&&(t={...t,ports:t.ports.map((e=>new MessagePortMain(e)))}),this.emit(e,t)}}start(){return this._internalPort.start()}close(){return this._internalPort.close()}postMessage(...e){return Array.isArray(e[1])&&(e[1]=e[1].map((e=>e instanceof MessagePortMain?e._internalPort:e))),this._internalPort.postMessage(...e)}}t.MessagePortMain=MessagePortMain},"./lib/browser/parse-features-string.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseFeatures=t.parseWebViewWebPreferences=t.parseCommaSeparatedKeyValue=void 0;const r=["top","left",...Object.keys({x:!0,y:!0,width:!0,height:!0,minWidth:!0,maxWidth:!0,minHeight:!0,maxHeight:!0,opacity:!0})];function coerce(e,t){if(r.includes(e))return parseInt(t,10);switch(t){case"true":case"1":case"yes":case void 0:return!0;case"false":case"0":case"no":return!1;default:return t}}function parseCommaSeparatedKeyValue(e){const t={};for(const r of e.split(",")){const[e,o]=r.split("=").map((e=>e.trim()));e&&(t[e]=coerce(e,o))}return t}t.parseCommaSeparatedKeyValue=parseCommaSeparatedKeyValue,t.parseWebViewWebPreferences=function parseWebViewWebPreferences(e){return parseCommaSeparatedKeyValue(e)};const o=["zoomFactor","nodeIntegration","javascript","contextIsolation","webviewTag"];t.parseFeatures=function parseFeatures(e){const t=parseCommaSeparatedKeyValue(e),r={};return o.forEach((e=>{void 0!==t[e]&&(r[e]=t[e],delete t[e])})),void 0!==t.left&&(t.x=t.left),void 0!==t.top&&(t.y=t.top),{options:t,webPreferences:r}}},"./lib/browser/rpc-server.ts":(e,t,r)=>{var o=r("./lib/common/webpack-globals-provider.ts").Promise;Object.defineProperty(t,"__esModule",{value:!0});const n=r("./lib/browser/api/exports/electron.ts"),s=r("fs"),i=r("./lib/browser/ipc-main-internal.ts"),a=r("./lib/browser/ipc-main-internal-utils.ts");i.ipcMainInternal.on("BROWSER_WINDOW_CLOSE",(function(e){const t=e.sender.getOwnerBrowserWindow();t&&t.close(),e.returnValue=null})),i.ipcMainInternal.handle("BROWSER_GET_LAST_WEB_PREFERENCES",(function(e){return e.sender.getLastWebPreferences()})),i.ipcMainInternal.handle("BROWSER_GET_PROCESS_MEMORY_INFO",(function(e){return e.sender._getProcessMemoryInfo()}));const l=(()=>{switch(process.platform){case"darwin":return new Set(["readFindText","writeFindText"]);case"linux":return new Set(Object.keys(n.clipboard));default:return new Set}})();a.handleSync("BROWSER_CLIPBOARD_SYNC",(function(e,t,...r){if(!l.has(t))throw new Error(`Invalid method: ${t}`);return n.clipboard[t](...r)}));a.handleSync("BROWSER_SANDBOX_LOAD",(async function(e){const t=e.sender._getPreloadPaths();return{preloadScripts:await o.all(t.map((e=>async function(e){let t=null,r=null;try{t=await s.promises.readFile(e,"utf8")}catch(e){r=e}return{preloadPath:e,preloadSrc:t,preloadError:r}}(e)))),process:{arch:process.arch,platform:process.platform,env:{...process.env},version:process.version,versions:process.versions,execPath:process.helperExecPath}}})),a.handleSync("BROWSER_NONSANDBOX_LOAD",(function(e){return{preloadPaths:e.sender._getPreloadPaths()}})),i.ipcMainInternal.on("BROWSER_PRELOAD_ERROR",(function(e,t,r){e.sender.emit("preload-error",e,t,r)}))},"./lib/browser/web-view-events.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.webViewEvents=void 0,t.webViewEvents={"load-commit":["url","isMainFrame"],"did-attach":[],"did-finish-load":[],"did-fail-load":["errorCode","errorDescription","validatedURL","isMainFrame","frameProcessId","frameRoutingId"],"did-frame-finish-load":["isMainFrame","frameProcessId","frameRoutingId"],"did-start-loading":[],"did-stop-loading":[],"dom-ready":[],"console-message":["level","message","line","sourceId"],"context-menu":["params"],"devtools-opened":[],"devtools-closed":[],"devtools-focused":[],"will-navigate":["url"],"did-start-navigation":["url","isInPlace","isMainFrame","frameProcessId","frameRoutingId"],"did-redirect-navigation":["url","isInPlace","isMainFrame","frameProcessId","frameRoutingId"],"did-navigate":["url","httpResponseCode","httpStatusText"],"did-frame-navigate":["url","httpResponseCode","httpStatusText","isMainFrame","frameProcessId","frameRoutingId"],"did-navigate-in-page":["url","isMainFrame","frameProcessId","frameRoutingId"],"-focus-change":["focus"],close:[],crashed:[],"render-process-gone":["details"],"plugin-crashed":["name","version"],destroyed:[],"page-title-updated":["title","explicitSet"],"page-favicon-updated":["favicons"],"enter-html-full-screen":[],"leave-html-full-screen":[],"media-started-playing":[],"media-paused":[],"found-in-page":["result"],"did-change-theme-color":["themeColor"],"update-target-url":["url"]}},"./lib/common/api/clipboard.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=process._linkedBinding("electron_common_clipboard");if("renderer"===process.type){const e=r("./lib/renderer/ipc-renderer-internal-utils.ts"),makeRemoteMethod=function(t){return(...r)=>e.invokeSync("BROWSER_CLIPBOARD_SYNC",t,...r)};if("linux"===process.platform)for(const e of Object.keys(o))o[e]=makeRemoteMethod(e);else"darwin"===process.platform&&(o.readFindText=makeRemoteMethod("readFindText"),o.writeFindText=makeRemoteMethod("writeFindText"))}t.default=o},"./lib/common/api/module-list.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.commonModuleList=void 0,t.commonModuleList=[{name:"clipboard",loader:()=>r("./lib/common/api/clipboard.ts")},{name:"nativeImage",loader:()=>r("./lib/common/api/native-image.ts")},{name:"shell",loader:()=>r("./lib/common/api/shell.ts")}]},"./lib/common/api/native-image.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});const{nativeImage:r}=process._linkedBinding("electron_common_native_image");t.default=r},"./lib/common/api/shell.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});const r=process._linkedBinding("electron_common_shell");t.default=r},"./lib/common/define-properties.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defineProperties=void 0;const handleESModule=e=>()=>{const t=e();return t.__esModule&&t.default?t.default:t};t.defineProperties=function defineProperties(e,t){const r={};for(const e of t)r[e.name]={enumerable:!0,get:handleESModule(e.loader)};return Object.defineProperties(e,r)}},"./lib/common/deprecate.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.moveAPI=t.renameProperty=t.removeProperty=t.event=t.renameFunction=t.removeFunction=t.log=t.warn=t.getHandler=t.setHandler=t.warnOnceMessage=t.warnOnce=void 0;let r=null;function warnOnce(e,t){return warnOnceMessage(t?`'${e}' is deprecated and will be removed. Please use '${t}' instead.`:`'${e}' is deprecated and will be removed.`)}function warnOnceMessage(e){let t=!1;return()=>{t||process.noDeprecation||(t=!0,log(e))}}function log(e){if("function"!=typeof r){if(process.throwDeprecation)throw new Error(e);return process.traceDeprecation?console.trace(e):console.warn(`(electron) ${e}`)}r(e)}t.warnOnce=warnOnce,t.warnOnceMessage=warnOnceMessage,t.setHandler=function setHandler(e){r=e},t.getHandler=function getHandler(){return r},t.warn=function warn(e,t){process.noDeprecation||log(`'${e}' is deprecated. Use '${t}' instead.`)},t.log=log,t.removeFunction=function removeFunction(e,t){if(!e)throw Error(`'${t} function' is invalid or does not exist.`);const r=warnOnce(`${e.name} function`);return function(){r(),e.apply(this,arguments)}},t.renameFunction=function renameFunction(e,t){const r=warnOnce(`${e.name} function`,`${t} function`);return function(){return r(),e.apply(this,arguments)}},t.event=function event(e,t,r,o=((...e)=>e)){const n=r.startsWith("-")?warnOnce(`${t} event`):warnOnce(`${t} event`,`${r} event`);return e.on(r,(function(...e){if(0!==this.listenerCount(t)){n();const r=o(...e);r&&this.emit(t,...r)}}))},t.removeProperty=function removeProperty(e,t,r){const o=Object.getOwnPropertyDescriptor(e.__proto__,t);if(!o)return log(`Unable to remove property '${t}' from an object that lacks it.`),e;if(!o.get||!o.set)return log(`Unable to remove property '${t}' from an object does not have a getter / setter`),e;const n=warnOnce(t);return Object.defineProperty(e,t,{configurable:!0,get:()=>(n(),o.get.call(e)),set:t=>(r&&!r.includes(t)||n(),o.set.call(e,t))})},t.renameProperty=function renameProperty(e,t,r){const o=warnOnce(t,r);return t in e&&!(r in e)&&(o(),e[r]=e[t]),Object.defineProperty(e,t,{get:()=>(o(),e[r]),set:t=>{o(),e[r]=t}})},t.moveAPI=function moveAPI(e,t,r){const o=warnOnce(t,r);return function(){return o(),e.apply(this,arguments)}}},"./lib/common/init.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=r("util"),n=r("timers"),wrapWithActivateUvLoop=function(e){return function wrap(e,t){const r=t(e);e[o.promisify.custom]&&(r[o.promisify.custom]=t(e[o.promisify.custom]));return r}(e,(function(e){return function(...t){return process.activateUvLoop(),e.apply(this,t)}}))};if(process.nextTick=wrapWithActivateUvLoop(process.nextTick),global.setImmediate=n.setImmediate=wrapWithActivateUvLoop(n.setImmediate),global.clearImmediate=n.clearImmediate,n.setTimeout=wrapWithActivateUvLoop(n.setTimeout),n.setInterval=wrapWithActivateUvLoop(n.setInterval),"browser"!==process.type&&"utility"!==process.type||(global.setTimeout=n.setTimeout,global.setInterval=n.setInterval),"win32"===process.platform){const{Readable:e}=r("stream"),t=new e;t.push(null),Object.defineProperty(process,"stdin",{configurable:!1,enumerable:!0,get:()=>t})}},"./lib/common/reset-search-paths.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=r("path"),n=r("module");if("renderer"===process.type){const e=n._load;n._load=function(t){return"vm"===t&&console.warn("The vm module of Node.js is deprecated in the renderer process and will be removed."),e.apply(this,arguments)}}const s=process.resourcesPath+o.sep,i=n._nodeModulePaths;n._nodeModulePaths=function(e){const t=i(e);return(o.resolve(e)+o.sep).startsWith(s)?t.filter((function(e){return e.startsWith(s)})):t};const makeElectronModule=e=>{const t=new n("electron",null);t.id="electron",t.loaded=!0,t.filename=e,Object.defineProperty(t,"exports",{get:()=>r("./lib/browser/api/exports/electron.ts")}),n._cache[e]=t};makeElectronModule("electron"),makeElectronModule("electron/common"),"browser"===process.type&&makeElectronModule("electron/main"),"renderer"===process.type&&makeElectronModule("electron/renderer");const a=n._resolveFilename;n._resolveFilename=function(e,t,r,o){return"electron"===e||e.startsWith("electron/")?"electron":a(e,t,r,o)}},"./lib/common/web-view-methods.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.asyncMethods=t.properties=t.syncMethods=void 0,t.syncMethods=new Set(["getURL","getTitle","isLoading","isLoadingMainFrame","isWaitingForResponse","stop","reload","reloadIgnoringCache","canGoBack","canGoForward","canGoToOffset","clearHistory","goBack","goForward","goToIndex","goToOffset","isCrashed","setUserAgent","getUserAgent","openDevTools","closeDevTools","isDevToolsOpened","isDevToolsFocused","inspectElement","setAudioMuted","isAudioMuted","isCurrentlyAudible","undo","redo","cut","copy","paste","pasteAndMatchStyle","delete","selectAll","unselect","replace","replaceMisspelling","findInPage","stopFindInPage","downloadURL","inspectSharedWorker","inspectServiceWorker","showDefinitionForSelection","getZoomFactor","getZoomLevel","setZoomFactor","setZoomLevel"]),t.properties=new Set(["audioMuted","userAgent","zoomLevel","zoomFactor","frameRate"]),t.asyncMethods=new Set(["capturePage","loadURL","executeJavaScript","insertCSS","insertText","removeInsertedCSS","send","sendToFrame","sendInputEvent","setLayoutZoomLevelLimits","setVisualZoomLevelLimits","print","printToPDF"])},"./lib/common/webpack-globals-provider.ts":(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Promise=void 0,t.Promise=global.Promise},"./lib/renderer/ipc-renderer-internal-utils.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.invokeSync=t.handle=void 0;const o=r("./lib/renderer/ipc-renderer-internal.ts");t.handle=function(e,t){o.ipcRendererInternal.on(e,(async(r,o,...n)=>{const s=`${e}_RESPONSE_${o}`;try{r.sender.send(s,null,await t(r,...n))}catch(e){r.sender.send(s,e)}}))},t.invokeSync=function invokeSync(e,...t){const[r,n]=o.ipcRendererInternal.sendSync(e,...t);if(r)throw r;return n}},"./lib/renderer/ipc-renderer-internal.ts":(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ipcRendererInternal=void 0;const o=r("events"),{ipc:n}=process._linkedBinding("electron_renderer_ipc"),s=!0;t.ipcRendererInternal=new o.EventEmitter,t.ipcRendererInternal.send=function(e,...t){return n.send(s,e,t)},t.ipcRendererInternal.sendSync=function(e,...t){return n.sendSync(s,e,t)},t.ipcRendererInternal.invoke=async function(e,...t){const{error:r,result:o}=await n.invoke(s,e,t);if(r)throw new Error(`Error invoking remote method '${e}': ${r}`);return o}},child_process:e=>{e.exports=require("child_process")},events:e=>{e.exports=require("events")},fs:e=>{e.exports=require("fs")},module:e=>{e.exports=require("module")},net:e=>{e.exports=require("net")},path:e=>{e.exports=require("path")},stream:e=>{e.exports=require("stream")},timers:e=>{e.exports=require("timers")},url:e=>{e.exports=require("url")},util:e=>{e.exports=require("util")},v8:e=>{e.exports=require("v8")}},t={};function __webpack_require__(r){var o=t[r];if(void 0!==o)return o.exports;var n=t[r]={exports:{}};return e[r].call(n.exports,n,n.exports,__webpack_require__),n.exports}var r={};(()=>{var e=r,t=__webpack_require__("./lib/common/webpack-globals-provider.ts").Promise;Object.defineProperty(e,"__esModule",{value:!0});const o=__webpack_require__("events"),n=__webpack_require__("fs"),s=__webpack_require__("path"),i=__webpack_require__("module");process.argv.splice(1,1),__webpack_require__("./lib/common/reset-search-paths.ts"),__webpack_require__("./lib/common/init.ts"),process._linkedBinding("electron_browser_event_emitter").setEventEmitterPrototype(o.EventEmitter.prototype),process.on("uncaughtException",(function(e){process.listenerCount("uncaughtException")>1||t.resolve().then((()=>__webpack_require__("./lib/browser/api/exports/electron.ts"))).then((({dialog:t})=>{const r="Uncaught Exception:\n"+(e.stack?e.stack:`${e.name}: ${e.message}`);t.showErrorBox("A JavaScript error occurred in the main process",r)}))}));const{app:a}=__webpack_require__("./lib/browser/api/exports/electron.ts");if(a.on("quit",((e,t)=>{process.emit("exit",t)})),"win32"===process.platform){const e=s.join(s.dirname(process.execPath),"..","update.exe");if(n.existsSync(e)){const t=s.dirname(s.resolve(e)),r=s.basename(t).replace(/\s/g,""),o=s.basename(process.execPath).replace(/\.exe$/i,"").replace(/\s/g,"");a.setAppUserModelId(`com.squirrel.${r}.${o}`)}}process.exit=a.exit,__webpack_require__("./lib/browser/rpc-server.ts"),__webpack_require__("./lib/browser/guest-view-manager.ts");const l=process._linkedBinding("electron_common_v8_util");let c=null,p=null;const d=l.getHiddenValue(global,"appSearchPaths"),u=l.getHiddenValue(global,"appSearchPathsOnlyLoadASAR"),h=process._getOrCreateArchive;if(delete process._getOrCreateArchive,process.resourcesPath)for(c of d)try{if(c=s.join(process.resourcesPath,c),u&&!h?.(c))continue;p=i._load(s.join(c,"package.json"));break}catch{continue}if(null==p)throw process.nextTick((function(){return process.exit(1)})),new Error("Unable to find a valid app");null!=p.version&&a.setVersion(p.version),null!=p.productName?a.name=`${p.productName}`.trim():null!=p.name&&(a.name=`${p.name}`.trim()),null!=p.desktopName?a.setDesktopName(p.desktopName):a.setDesktopName(`${a.name}.desktop`),null!=p.v8Flags&&__webpack_require__("v8").setFlagsFromString(p.v8Flags),a.setAppPath(c),__webpack_require__("./lib/browser/devtools.ts"),__webpack_require__("./lib/browser/api/protocol.ts"),__webpack_require__("./lib/browser/api/web-contents.ts"),__webpack_require__("./lib/browser/api/web-frame-main.ts");const m=p.main||"index.js",f=["Pantheon","Unity:Unity7","pop:GNOME"];process.env.ORIGINAL_XDG_CURRENT_DESKTOP=process.env.XDG_CURRENT_DESKTOP,function currentPlatformSupportsAppIndicator(){if("linux"!==process.platform)return!1;const e=process.env.XDG_CURRENT_DESKTOP;return!!e&&(!!f.includes(e)||!!/ubuntu/gi.test(e))}()&&(process.env.XDG_CURRENT_DESKTOP="Unity"),a.on("window-all-closed",(()=>{1===a.listenerCount("window-all-closed")&&a.quit()}));const{setDefaultApplicationMenu:b}=__webpack_require__("./lib/browser/default-menu.ts");a.once("will-finish-launching",b),c?(process._firstFileName=i._resolveFilename(s.join(c,m),null,!1),i._load(s.join(c,m),i,!0)):(console.error("Failed to locate a valid package to load (app, app.asar or default_app.asar)"),console.error("This normally means you've damaged the Electron package somehow"))})()})();electron/js2c/browser_init