!function(){try{t="function"==typeof setTimeout?setTimeout:defaultSetTimout}catch(e){t=defaultSetTimout}try{r="function"==typeof clearTimeout?clearTimeout:defaultClearTimeout}catch(e){r=defaultClearTimeout}}();var i,o=[],s=!1,a=-1;function cleanUpNextTick(){s&&i&&(s=!1,i.length?o=i.concat(o):a=-1,o.length&&drainQueue())}function drainQueue(){if(!s){var e=runTimeout(cleanUpNextTick);s=!0;for(var t=o.length;t;){for(i=o,o=[];++a<t;)i&&i[a].run();a=-1,t=o.length}i=null,s=!1,function runClearTimeout(e){if(r===clearTimeout)return clearTimeout(e);if((r===defaultClearTimeout||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{return r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function Item(e,t){this.fun=e,this.array=t}function noop(){}n.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];o.push(new Item(e,t)),1!==o.length||s||runTimeout(drainQueue)},Item.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.browser=!0,n.env={},n.argv=[],n.version="",n.versions={},n.on=noop,n.addListener=noop,n.once=noop,n.off=noop,n.removeListener=noop,n.removeAllListeners=noop,n.emit=noop,n.prependListener=noop,n.prependOnceListener=noop,n.listeners=function(e){return[]},n.binding=function(e){throw new Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(e){throw new Error("process.chdir is not supported")},n.umask=function(){return 0}},"./node_modules/querystring/decode.js":e=>{"use strict";function hasOwnProperty(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.exports=function(e,t,r,n){t=t||"&",r=r||"=";var i={};if("string"!=typeof e||0===e.length)return i;var o=/\+/g;e=e.split(t);var s=1e3;n&&"number"==typeof n.maxKeys&&(s=n.maxKeys);var a=e.length;s>0&&a>s&&(a=s);for(var u=0;u<a;++u){var c,f,l,h,d=e[u].replace(o,"%20"),p=d.indexOf(r);p>=0?(c=d.substr(0,p),f=d.substr(p+1)):(c=d,f=""),l=decodeURIComponent(c),h=decodeURIComponent(f),hasOwnProperty(i,l)?Array.isArray(i[l])?i[l].push(h):i[l]=[i[l],h]:i[l]=h}return i}},"./node_modules/querystring/encode.js":e=>{"use strict";var stringifyPrimitive=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};e.exports=function(e,t,r,n){return t=t||"&",r=r||"=",null===e&&(e=void 0),"object"==typeof e?Object.keys(e).map((function(n){var i=encodeURIComponent(stringifyPrimitive(n))+r;return Array.isArray(e[n])?e[n].map((function(e){return i+encodeURIComponent(stringifyPrimitive(e))})).join(t):i+encodeURIComponent(stringifyPrimitive(e[n]))})).join(t):n?encodeURIComponent(stringifyPrimitive(n))+r+encodeURIComponent(stringifyPrimitive(e)):""}},"./node_modules/querystring/index.js":(e,t,r)=>{"use strict";t.decode=t.parse=r("./node_modules/querystring/decode.js"),t.encode=t.stringify=r("./node_modules/querystring/encode.js")},"./node_modules/timers-browserify/main.js":(e,t,r)=>{var n=r("./node_modules/process/browser.js").nextTick,i=Function.prototype.apply,o=Array.prototype.slice,s={},a=0;function Timeout(e,t){this._id=e,this._clearFn=t}t.setTimeout=function(){return new Timeout(i.call(setTimeout,window,arguments),clearTimeout)},t.setInterval=function(){return new Timeout(i.call(setInterval,window,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e.close()},Timeout.prototype.unref=Timeout.prototype.ref=function(){},Timeout.prototype.close=function(){this._clearFn.call(window,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout((function onTimeout(){e._onTimeout&&e._onTimeout()}),t))},t.setImmediate="function"==typeof setImmediate?setImmediate:function(e){var r=a++,i=!(arguments.length<2)&&o.call(arguments,1);return s[r]=!0,n((function onNextTick(){s[r]&&(i?e.apply(null,i):e.call(null),t.clearImmediate(r))})),r},t.clearImmediate="function"==typeof clearImmediate?clearImmediate:function(e){delete s[e]}},"./lib/common/api/native-image.ts":(e,t,r)=>{"use strict";var n=r("./node_modules/process/browser.js");Object.defineProperty(t,"__esModule",{value:!0});const{nativeImage:i}=n._linkedBinding("electron_common_native_image");t.default=i},"./lib/common/define-properties.ts":(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.defineProperties=void 0;const handleESModule=e=>()=>{const t=e();return t.__esModule&&t.default?t.default:t};t.defineProperties=function defineProperties(e,t){const r={};for(const e of t)r[e.name]={enumerable:!0,get:handleESModule(e.loader)};return Object.defineProperties(e,r)}},"./lib/common/web-view-methods.ts":(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.asyncMethods=t.properties=t.syncMethods=void 0,t.syncMethods=new Set(["getURL","getTitle","isLoading","isLoadingMainFrame","isWaitingForResponse","stop","reload","reloadIgnoringCache","canGoBack","canGoForward","canGoToOffset","clearHistory","goBack","goForward","goToIndex","goToOffset","isCrashed","setUserAgent","getUserAgent","openDevTools","closeDevTools","isDevToolsOpened","isDevToolsFocused","inspectElement","setAudioMuted","isAudioMuted","isCurrentlyAudible","undo","redo","cut","copy","paste","pasteAndMatchStyle","delete","selectAll","unselect","replace","replaceMisspelling","findInPage","stopFindInPage","downloadURL","inspectSharedWorker","inspectServiceWorker","showDefinitionForSelection","getZoomFactor","getZoomLevel","setZoomFactor","setZoomLevel"]),t.properties=new Set(["audioMuted","userAgent","zoomLevel","zoomFactor","frameRate"]),t.asyncMethods=new Set(["capturePage","loadURL","executeJavaScript","insertCSS","insertText","removeInsertedCSS","send","sendToFrame","sendInputEvent","setLayoutZoomLevelLimits","setVisualZoomLevelLimits","print","printToPDF"])},"./lib/common/webpack-globals-provider.ts":(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Promise=void 0,t.Promise=r.g.Promise},"./lib/renderer/api/context-bridge.ts":(e,t,r)=>{"use strict";var n=r("./node_modules/process/browser.js");Object.defineProperty(t,"__esModule",{value:!0}),t.internalContextBridge=void 0;const i=n._linkedBinding("electron_renderer_context_bridge"),checkContextIsolationEnabled=()=>{if(!n.contextIsolated)throw new Error("contextBridge API can only be used when contextIsolation is enabled")},o={exposeInMainWorld:(e,t)=>(checkContextIsolationEnabled(),i.exposeAPIInWorld(0,e,t)),exposeInIsolatedWorld:(e,t,r)=>(checkContextIsolationEnabled(),i.exposeAPIInWorld(e,t,r))};t.default=o,t.internalContextBridge={contextIsolationEnabled:n.contextIsolated,overrideGlobalValueFromIsolatedWorld:(e,t)=>i._overrideGlobalValueFromIsolatedWorld(e,t,!1),overrideGlobalValueWithDynamicPropsFromIsolatedWorld:(e,t)=>i._overrideGlobalValueFromIsolatedWorld(e,t,!0),overrideGlobalPropertyFromIsolatedWorld:(e,t,r)=>i._overrideGlobalPropertyFromIsolatedWorld(e,t,r||null),isInMainWorld:()=>i._isCalledFromMainWorld()},i._isDebug&&(o.internalContextBridge=t.internalContextBridge)},"./lib/renderer/api/crash-reporter.ts":(e,t,r)=>{"use strict";var n=r("./node_modules/process/browser.js");Object.defineProperty(t,"__esModule",{value:!0});const i=n._linkedBinding("electron_renderer_crash_reporter");t.default={addExtraParameter(e,t){i.addExtraParameter(e,t)},removeExtraParameter(e){i.removeExtraParameter(e)},getParameters:()=>i.getParameters()}},"./lib/renderer/api/ipc-renderer.ts":(e,t,r)=>{"use strict";var n=r("./node_modules/process/browser.js");Object.defineProperty(t,"__esModule",{value:!0});const i=r("./node_modules/events/events.js"),{ipc:o}=n._linkedBinding("electron_renderer_ipc"),s=!1,a=new i.EventEmitter;a.send=function(e,...t){return o.send(s,e,t)},a.sendSync=function(e,...t){return o.sendSync(s,e,t)},a.sendToHost=function(e,...t){return o.sendToHost(e,t)},a.sendTo=function(e,t,...r){return o.sendTo(e,t,r)},a.invoke=async function(e,...t){const{error:r,result:n}=await o.invoke(s,e,t);if(r)throw new Error(`Error invoking remote method '${e}': ${r}`);return n},a.postMessage=function(e,t,r){return o.postMessage(e,t,r)},t.default=a},"./lib/renderer/api/web-frame.ts":(e,t,r)=>{"use strict";var n=r("./node_modules/process/browser.js");Object.defineProperty(t,"__esModule",{value:!0});const{mainFrame:i}=n._linkedBinding("electron_renderer_web_frame");t.default=i},"./lib/renderer/common-init.ts":(e,t,r)=>{"use strict";var n=r("./node_modules/process/browser.js");Object.defineProperty(t,"__esModule",{value:!0});const i=r("./lib/sandboxed_renderer/api/exports/electron.ts"),o=r("./lib/renderer/ipc-renderer-internal.ts"),{mainFrame:s}=n._linkedBinding("electron_renderer_web_frame"),a=n._linkedBinding("electron_common_v8_util"),u=s.getWebPreference("nodeIntegration"),c=s.getWebPreference("webviewTag"),f=s.getWebPreference("hiddenPage"),l=s.getWebPreference("isWebView");switch(a.setHiddenValue(r.g,"ipcNative",{onMessage(e,t,r,n,s){if(e&&0!==s)return void console.error(`Message ${t} sent by unexpected WebContents (${s})`);const a=e?o.ipcRendererInternal:i.ipcRenderer;a.emit(t,{sender:a,senderId:s,ports:r},...n)}}),window.location.protocol){case"devtools:":r("./lib/renderer/inspector.ts");break;case"chrome-extension:":case"chrome:":break;default:{const{windowSetup:e}=r("./lib/renderer/window-setup.ts");e(l,f)}}if(n.isMainFrame){const{webViewInit:e}=r("./lib/renderer/web-view/web-view-init.ts");e(c,l)}const{webFrameInit:h}=r("./lib/renderer/web-frame-init.ts");if(h(),n.isMainFrame){const{securityWarnings:e}=r("./lib/renderer/security-warnings.ts");e(u)}},"./lib/renderer/inspector.ts":(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r("./lib/renderer/api/context-bridge.ts"),i=r("./lib/renderer/ipc-renderer-internal.ts"),o=r("./lib/renderer/ipc-renderer-internal-utils.ts"),s=r("./lib/sandboxed_renderer/api/exports/electron.ts"),{contextIsolationEnabled:a}=n.internalContextBridge;window.onload=function(){a?n.internalContextBridge.overrideGlobalValueFromIsolatedWorld(["InspectorFrontendHost","showContextMenuAtPoint"],createMenu):window.InspectorFrontendHost.showContextMenuAtPoint=createMenu},window.confirm=function(e,t){return o.invokeSync("INSPECTOR_CONFIRM",e,t)};const createMenu=function(e,t,r){const n=function(e,t,r){return 0===r.length&&document.elementsFromPoint(e,t).some((e=>"INPUT"===e.nodeName||"TEXTAREA"===e.nodeName||e.isContentEditable))}(e,t,r);i.ipcRendererInternal.invoke("INSPECTOR_CONTEXT_MENU",r,n).then((e=>{"number"==typeof e&&s.webFrame.executeJavaScript(`window.DevToolsAPI.contextMenuItemSelected(${JSON.stringify(e)})`),s.webFrame.executeJavaScript("window.DevToolsAPI.contextMenuCleared()")}))}},"./lib/renderer/ipc-renderer-internal-utils.ts":(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.invokeSync=t.handle=void 0;const n=r("./lib/renderer/ipc-renderer-internal.ts");t.handle=function(e,t){n.ipcRendererInternal.on(e,(async(r,n,...i)=>{const o=`${e}_RESPONSE_${n}`;try{r.sender.send(o,null,await t(r,...i))}catch(e){r.sender.send(o,e)}}))},t.invokeSync=function invokeSync(e,...t){const[r,i]=n.ipcRendererInternal.sendSync(e,...t);if(r)throw r;return i}},"./lib/renderer/ipc-renderer-internal.ts":(e,t,r)=>{"use strict";var n=r("./node_modules/process/browser.js");Object.defineProperty(t,"__esModule",{value:!0}),t.ipcRendererInternal=void 0;const i=r("./node_modules/events/events.js"),{ipc:o}=n._linkedBinding("electron_renderer_ipc"),s=!0;t.ipcRendererInternal=new i.EventEmitter,t.ipcRendererInternal.send=function(e,...t){return o.send(s,e,t)},t.ipcRendererInternal.sendSync=function(e,...t){return o.sendSync(s,e,t)},t.ipcRendererInternal.invoke=async function(e,...t){const{error:r,result:n}=await o.invoke(s,e,t);if(r)throw new Error(`Error invoking remote method '${e}': ${r}`);return n}},"./lib/renderer/security-warnings.ts":(e,t,r)=>{"use strict";var n=r("./node_modules/process/browser.js");Object.defineProperty(t,"__esModule",{value:!0}),t.securityWarnings=void 0;const i=r("./lib/renderer/ipc-renderer-internal.ts"),{mainFrame:o}=n._linkedBinding("electron_renderer_web_frame");let s=null;const{platform:a,execPath:u,env:c}=n,f="\nFor more information and help, consult\nhttps://electronjs.org/docs/tutorial/security.\nThis warning will not show up\nonce the app is packaged.",warnAboutNodeWithRemoteContent=function(e){if(!(!e||window&&window.location&&"localhost"===window.location.hostname)&&function(){if(window&&window.location&&window.location.protocol)return/^(http|ftp)s?/gi.test(window.location.protocol)}()){const e=`This renderer process has Node.js integration enabled\n    and attempted to load remote content from '${window.location}'. This\n    exposes users of this app to severe security risks.\n${f}`;console.warn("%cElectron Security Warning (Node.js Integration with Remote Content)","font-weight: bold;",e)}},warnAboutInsecureCSP=function(){if(!o._isEvalAllowed())return;const e=`This renderer process has either no Content Security\n  Policy set or a policy with "unsafe-eval" enabled. This exposes users of\n  this app to unnecessary security risks.\n${f}`;console.warn("%cElectron Security Warning (Insecure Content-Security-Policy)","font-weight: bold;",e)},logSecurityWarnings=function(e,t){warnAboutNodeWithRemoteContent(t),function(e){if(!e||!1!==e.webSecurity)return;const t=`This renderer process has "webSecurity" disabled. This\n  exposes users of this app to severe security risks.\n${f}`;console.warn("%cElectron Security Warning (Disabled webSecurity)","font-weight: bold;",t)}(e),function(){if(!window||!window.performance||!window.performance.getEntriesByType)return;const e=window.performance.getEntriesByType("resource").filter((({name:e})=>{return t=new URL(e),["http:","ftp:"].includes(t.protocol)&&!(e=>["localhost","127.0.0.1","[::1]",""].includes(e.hostname))(t);var t})).map((({name:e})=>`- ${e}`)).join("\n");if(!e||0===e.length)return;const t=`This renderer process loads resources using insecure\n  protocols. This exposes users of this app to unnecessary security risks.\n  Consider loading the following resources over HTTPS or FTPS. \n${e}\n  \n${f}`;console.warn("%cElectron Security Warning (Insecure Resources)","font-weight: bold;",t)}(),function(e){if(!e||!e.allowRunningInsecureContent)return;const t=`This renderer process has "allowRunningInsecureContent"\n  enabled. This exposes users of this app to severe security risks.\n\n  ${f}`;console.warn("%cElectron Security Warning (allowRunningInsecureContent)","font-weight: bold;",t)}(e),function(e){if(!e||!e.experimentalFeatures)return;const t=`This renderer process has "experimentalFeatures" enabled.\n  This exposes users of this app to some security risk. If you do not need\n  this feature, you should disable it.\n${f}`;console.warn("%cElectron Security Warning (experimentalFeatures)","font-weight: bold;",t)}(e),function(e){if(!e||!Object.prototype.hasOwnProperty.call(e,"enableBlinkFeatures")||null!=e.enableBlinkFeatures&&0===e.enableBlinkFeatures.length)return;const t=`This renderer process has additional "enableBlinkFeatures"\n  enabled. This exposes users of this app to some security risk. If you do not\n  need this feature, you should disable it.\n${f}`;console.warn("%cElectron Security Warning (enableBlinkFeatures)","font-weight: bold;",t)}(e),warnAboutInsecureCSP(),function(){if(document&&document.querySelectorAll){const e=document.querySelectorAll("[allowpopups]");if(!e||0===e.length)return;const t=`A <webview> has "allowpopups" set to true. This exposes\n    users of this app to some security risk, since popups are just\n    BrowserWindows. If you do not need this feature, you should disable it.\n\n    ${f}`;console.warn("%cElectron Security Warning (allowpopups)","font-weight: bold;",t)}}()};t.securityWarnings=function securityWarnings(e){window.addEventListener("load",(async function(){if(function(){if(null!==s)return s;switch(a){case"darwin":s=u.endsWith("MacOS/Electron")||u.includes("Electron.app/Contents/Frameworks/");break;case"freebsd":case"linux":s=u.endsWith("/electron");break;case"win32":s=u.endsWith("\\electron.exe");break;default:s=!1}return(c&&c.ELECTRON_DISABLE_SECURITY_WARNINGS||window&&window.ELECTRON_DISABLE_SECURITY_WARNINGS)&&(s=!1),(c&&c.ELECTRON_ENABLE_SECURITY_WARNINGS||window&&window.ELECTRON_ENABLE_SECURITY_WARNINGS)&&(s=!0),s}()){const t=await async function(){try{return i.ipcRendererInternal.invoke("BROWSER_GET_LAST_WEB_PREFERENCES")}catch(e){console.warn(`getLastWebPreferences() failed: ${e}`)}}();logSecurityWarnings(t,e)}}),{once:!0})}},"./lib/renderer/web-frame-init.ts":(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.webFrameInit=void 0;const n=r("./lib/sandboxed_renderer/api/exports/electron.ts"),i=r("./lib/renderer/ipc-renderer-internal-utils.ts");t.webFrameInit=()=>{i.handle("RENDERER_WEB_FRAME_METHOD",((e,t,...r)=>n.webFrame[t](...r)))}},"./lib/renderer/web-view/guest-view-internal.ts":(e,t,r)=>{"use strict";var n=r("./node_modules/process/browser.js");Object.defineProperty(t,"__esModule",{value:!0}),t.propertySet=t.propertyGet=t.invokeSync=t.invoke=t.detachGuest=t.createGuest=t.deregisterEvents=t.registerEvents=void 0;const i=r("./lib/renderer/ipc-renderer-internal.ts"),o=r("./lib/renderer/ipc-renderer-internal-utils.ts"),{mainFrame:s}=n._linkedBinding("electron_renderer_web_frame");t.registerEvents=function registerEvents(e,t){i.ipcRendererInternal.on(`GUEST_VIEW_INTERNAL_DISPATCH_EVENT-${e}`,(function(e,r,n){t.dispatchEvent(r,n)}))},t.deregisterEvents=function deregisterEvents(e){i.ipcRendererInternal.removeAllListeners(`GUEST_VIEW_INTERNAL_DISPATCH_EVENT-${e}`)},t.createGuest=function createGuest(e,t,r){if(!(e instanceof HTMLIFrameElement))throw new Error("Invalid embedder frame");const n=s.getWebFrameId(e.contentWindow);if(n<0)throw new Error("Invalid embedder frame");return i.ipcRendererInternal.invoke("GUEST_VIEW_MANAGER_CREATE_AND_ATTACH_GUEST",n,t,r)},t.detachGuest=function detachGuest(e){return o.invokeSync("GUEST_VIEW_MANAGER_DETACH_GUEST",e)},t.invoke=function invoke(e,t,r){return i.ipcRendererInternal.invoke("GUEST_VIEW_MANAGER_CALL",e,t,r)},t.invokeSync=function invokeSync(e,t,r){return o.invokeSync("GUEST_VIEW_MANAGER_CALL",e,t,r)},t.propertyGet=function propertyGet(e,t){return o.invokeSync("GUEST_VIEW_MANAGER_PROPERTY_GET",e,t)},t.propertySet=function propertySet(e,t,r){return o.invokeSync("GUEST_VIEW_MANAGER_PROPERTY_SET",e,t,r)}},"./lib/renderer/web-view/web-view-attributes.ts":(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setupWebViewAttributes=t.SrcAttribute=t.PartitionAttribute=t.WebViewAttribute=void 0;const resolveURL=function(e){return e?new URL(e,location.href).href:""};class WebViewAttribute{constructor(e,t){this.name=e,this.webViewImpl=t,this.ignoreMutation=!1,this.handleMutation=()=>{},this.name=e,this.value=t.webviewNode[e]||"",this.webViewImpl=t,this.defineProperty()}getValue(){return this.webViewImpl.webviewNode.getAttribute(this.name)||this.value}setValue(e){this.webViewImpl.webviewNode.setAttribute(this.name,e||"")}setValueIgnoreMutation(e){this.ignoreMutation=!0,this.setValue(e),this.ignoreMutation=!1}defineProperty(){return Object.defineProperty(this.webViewImpl.webviewNode,this.name,{get:()=>this.getValue(),set:e=>this.setValue(e),enumerable:!0})}}t.WebViewAttribute=WebViewAttribute;class BooleanAttribute extends WebViewAttribute{getValue(){return this.webViewImpl.webviewNode.hasAttribute(this.name)}setValue(e){e?this.webViewImpl.webviewNode.setAttribute(this.name,""):this.webViewImpl.webviewNode.removeAttribute(this.name)}}class PartitionAttribute extends WebViewAttribute{constructor(e){super("partition",e),this.webViewImpl=e,this.validPartitionId=!0,this.handleMutation=(e,t)=>{if(t=t||"",!this.webViewImpl.beforeFirstNavigation)return console.error("The object has already navigated, so its partition cannot be changed."),void this.setValueIgnoreMutation(e);"persist:"===t&&(this.validPartitionId=!1,console.error("Invalid partition attribute."))}}}t.PartitionAttribute=PartitionAttribute;class SrcAttribute extends WebViewAttribute{constructor(e){super("src",e),this.webViewImpl=e,this.handleMutation=(e,t)=>{t||!e?this.parse():this.setValueIgnoreMutation(e)},this.setupMutationObserver()}getValue(){return this.webViewImpl.webviewNode.hasAttribute(this.name)?resolveURL(this.webViewImpl.webviewNode.getAttribute(this.name)):this.value}setValueIgnoreMutation(e){super.setValueIgnoreMutation(e),this.observer.takeRecords()}setupMutationObserver(){this.observer=new MutationObserver((e=>{for(const t of e){const{oldValue:e}=t,r=this.getValue();if(e!==r)return;this.handleMutation(e,r)}}));const e={attributes:!0,attributeOldValue:!0,attributeFilter:[this.name]};this.observer.observe(this.webViewImpl.webviewNode,e)}parse(){if(!this.webViewImpl.elementAttached||!this.webViewImpl.attributes.get("partition").validPartitionId||!this.getValue())return;if(null==this.webViewImpl.guestInstanceId)return void(this.webViewImpl.beforeFirstNavigation&&(this.webViewImpl.beforeFirstNavigation=!1,this.webViewImpl.createGuest()));const e={},t=this.webViewImpl.attributes.get("httpreferrer").getValue();t&&(e.httpReferrer=t);const r=this.webViewImpl.attributes.get("useragent").getValue();r&&(e.userAgent=r),this.webViewImpl.webviewNode.loadURL(this.getValue(),e).catch((e=>{console.error("Unexpected error while loading URL",e)}))}}t.SrcAttribute=SrcAttribute;class HttpReferrerAttribute extends WebViewAttribute{constructor(e){super("httpreferrer",e)}}class UserAgentAttribute extends WebViewAttribute{constructor(e){super("useragent",e)}}class PreloadAttribute extends WebViewAttribute{constructor(e){super("preload",e)}getValue(){if(!this.webViewImpl.webviewNode.hasAttribute(this.name))return this.value;let e=resolveURL(this.webViewImpl.webviewNode.getAttribute(this.name));return"file:"!==e.substr(0,5)&&(console.error('Only "file:" protocol is supported in "preload" attribute.'),e=""),e}}class BlinkFeaturesAttribute extends WebViewAttribute{constructor(e){super("blinkfeatures",e)}}class DisableBlinkFeaturesAttribute extends WebViewAttribute{constructor(e){super("disableblinkfeatures",e)}}class WebPreferencesAttribute extends WebViewAttribute{constructor(e){super("webpreferences",e)}}t.setupWebViewAttributes=function setupWebViewAttributes(e){return new Map([["partition",new PartitionAttribute(e)],["src",new SrcAttribute(e)],["httpreferrer",new HttpReferrerAttribute(e)],["useragent",new UserAgentAttribute(e)],["nodeintegration",new BooleanAttribute("nodeintegration",e)],["nodeintegrationinsubframes",new BooleanAttribute("nodeintegrationinsubframes",e)],["plugins",new BooleanAttribute("plugins",e)],["disablewebsecurity",new BooleanAttribute("disablewebsecurity",e)],["allowpopups",new BooleanAttribute("allowpopups",e)],["preload",new PreloadAttribute(e)],["blinkfeatures",new BlinkFeaturesAttribute(e)],["disableblinkfeatures",new DisableBlinkFeaturesAttribute(e)],["webpreferences",new WebPreferencesAttribute(e)]])}},"./lib/renderer/web-view/web-view-element.ts":(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setupWebView=void 0;const n=r("./lib/renderer/web-view/web-view-impl.ts"),i=new WeakMap,defineWebViewElement=e=>class WebViewElement extends HTMLElement{static get observedAttributes(){return["partition","src","httpreferrer","useragent","nodeintegration","nodeintegrationinsubframes","plugins","disablewebsecurity","allowpopups","preload","blinkfeatures","disableblinkfeatures","webpreferences"]}constructor(){super(),i.set(this,new n.WebViewImpl(this,e))}getWebContentsId(){const e=i.get(this);if(!e||!e.guestInstanceId)throw new Error("The WebView must be attached to the DOM and the dom-ready event emitted before this method can be called.");return e.guestInstanceId}connectedCallback(){const t=i.get(this);t&&(t.elementAttached||(e.guestViewInternal.registerEvents(t.viewInstanceId,{dispatchEvent:t.dispatchEvent.bind(t)}),t.elementAttached=!0,t.attributes.get("src").parse()))}attributeChangedCallback(e,t,r){const n=i.get(this);n&&n.handleWebviewAttributeMutation(e,t,r)}disconnectedCallback(){const t=i.get(this);t&&(e.guestViewInternal.deregisterEvents(t.viewInstanceId),t.guestInstanceId&&e.guestViewInternal.detachGuest(t.guestInstanceId),t.elementAttached=!1,t.reset())}};t.setupWebView=e=>{const listener=t=>{"loading"!==document.readyState&&((e=>{const t=defineWebViewElement(e);(0,n.setupMethods)(t,e),e.allowGuestViewElementDefinition(window,(()=>{window.customElements.define("webview",t),window.WebView=t,delete t.prototype.connectedCallback,delete t.prototype.disconnectedCallback,delete t.prototype.attributeChangedCallback,delete t.observedAttributes}))})(e),window.removeEventListener(t.type,listener,true))};window.addEventListener("readystatechange",listener,true)}},"./lib/renderer/web-view/web-view-impl.ts":(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setupMethods=t.WebViewImpl=void 0;const n=r("./lib/common/web-view-methods.ts"),i=r("./lib/renderer/web-view/web-view-attributes.ts");let o=0;const getNextId=function(){return++o};t.WebViewImpl=class WebViewImpl{constructor(e,t){this.webviewNode=e,this.hooks=t,this.beforeFirstNavigation=!0,this.elementAttached=!1,this.hasFocus=!1,this.on={},this.internalElement=this.createInternalElement();const r=this.webviewNode.attachShadow({mode:"open"}),n=r.ownerDocument.createElement("style");n.textContent=":host { display: flex; }",r.appendChild(n),this.attributes=(0,i.setupWebViewAttributes)(this),this.viewInstanceId=getNextId(),r.appendChild(this.internalElement),Object.defineProperty(this.webviewNode,"contentWindow",{get:()=>this.internalElement.contentWindow,enumerable:!0})}createInternalElement(){const e=document.createElement("iframe");return e.style.flex="1 1 auto",e.style.width="100%",e.style.border="0",this.hooks.setIsWebView(e),e}reset(){this.guestInstanceId&&(this.guestInstanceId=void 0),this.beforeFirstNavigation=!0,this.attributes.get("partition").validPartitionId=!0;const e=this.createInternalElement(),t=this.internalElement;this.internalElement=e,t&&t.parentNode&&t.parentNode.replaceChild(e,t)}handleWebviewAttributeMutation(e,t,r){this.attributes.has(e)&&!this.attributes.get(e).ignoreMutation&&this.attributes.get(e).handleMutation(t,r)}onElementResize(){const e={newWidth:this.webviewNode.clientWidth,newHeight:this.webviewNode.clientHeight};this.dispatchEvent("resize",e)}createGuest(){this.internalInstanceId=getNextId(),this.hooks.guestViewInternal.createGuest(this.internalElement,this.internalInstanceId,this.buildParams()).then((e=>{this.attachGuestInstance(e)}))}dispatchEvent(e,t={}){const r=new Event(e);Object.assign(r,t),this.webviewNode.dispatchEvent(r),"load-commit"===e?this.onLoadCommit(t):"-focus-change"===e&&this.onFocusChange()}setupEventProperty(e){const t=`on${e.toLowerCase()}`;return Object.defineProperty(this.webviewNode,t,{get:()=>this.on[t],set:r=>{if(this.on[t]&&this.webviewNode.removeEventListener(e,this.on[t]),this.on[t]=r,r)return this.webviewNode.addEventListener(e,r)},enumerable:!0})}onLoadCommit(e){const t=this.webviewNode.getAttribute("src"),r=e.url;e.isMainFrame&&t!==r&&this.attributes.get("src").setValueIgnoreMutation(r)}onFocusChange(){const e=this.webviewNode.ownerDocument.activeElement===this.webviewNode;e!==this.hasFocus&&(this.hasFocus=e,this.dispatchEvent(e?"focus":"blur"))}onAttach(e){return this.attributes.get("partition").setValue(e)}buildParams(){const e={instanceId:this.viewInstanceId};for(const[t,r]of this.attributes)e[t]=r.getValue();return e}attachGuestInstance(e){-1!==e?this.elementAttached?(this.guestInstanceId=e,this.resizeObserver=new ResizeObserver(this.onElementResize.bind(this)),this.resizeObserver.observe(this.internalElement)):this.hooks.guestViewInternal.detachGuest(e):this.dispatchEvent("destroyed")}};t.setupMethods=(e,t)=>{e.prototype.focus=function(){this.contentWindow.focus()};for(const r of n.syncMethods)e.prototype[r]=function(...e){return t.guestViewInternal.invokeSync(this.getWebContentsId(),r,e)};for(const r of n.asyncMethods)e.prototype[r]=function(...e){return t.guestViewInternal.invoke(this.getWebContentsId(),r,e)};const createPropertyGetter=function(e){return function(){return t.guestViewInternal.propertyGet(this.getWebContentsId(),e)}},createPropertySetter=function(e){return function(r){return t.guestViewInternal.propertySet(this.getWebContentsId(),e,r)}};for(const t of n.properties)Object.defineProperty(e.prototype,t,{get:createPropertyGetter(t),set:createPropertySetter(t)})}},"./lib/renderer/web-view/web-view-init.ts":(e,t,r)=>{"use strict";var n=r("./node_modules/process/browser.js");Object.defineProperty(t,"__esModule",{value:!0}),t.webViewInit=void 0;const i=r("./lib/renderer/ipc-renderer-internal.ts"),o=n._linkedBinding("electron_common_v8_util"),{mainFrame:s}=n._linkedBinding("electron_renderer_web_frame");t.webViewInit=function webViewInit(e,t){if(e&&!t){const e=r("./lib/renderer/web-view/guest-view-internal.ts");if(n.contextIsolated)o.setHiddenValue(window,"guestViewInternal",e);else{const{setupWebView:t}=r("./lib/renderer/web-view/web-view-element.ts");t({guestViewInternal:e,allowGuestViewElementDefinition:s.allowGuestViewElementDefinition,setIsWebView:e=>o.setHiddenValue(e,"isWebView",!0)})}}t&&function handleFocusBlur(){window.addEventListener("focus",(()=>{i.ipcRendererInternal.send("GUEST_VIEW_MANAGER_FOCUS_CHANGE",!0)})),window.addEventListener("blur",(()=>{i.ipcRendererInternal.send("GUEST_VIEW_MANAGER_FOCUS_CHANGE",!1)}))}()}},"./lib/renderer/window-setup.ts":(e,t,r)=>{"use strict";var n=r("./node_modules/process/browser.js");Object.defineProperty(t,"__esModule",{value:!0}),t.windowSetup=void 0;const i=r("./lib/renderer/ipc-renderer-internal.ts"),o=r("./lib/renderer/api/context-bridge.ts"),{contextIsolationEnabled:s}=o.internalContextBridge;t.windowSetup=(e,t)=>{if(n.sandboxed||e||(window.close=function(){i.ipcRendererInternal.send("BROWSER_WINDOW_CLOSE")},s&&o.internalContextBridge.overrideGlobalValueFromIsolatedWorld(["close"],window.close)),window.prompt=function(){throw new Error("prompt() is and will not be supported.")},s&&o.internalContextBridge.overrideGlobalValueFromIsolatedWorld(["prompt"],window.prompt),e){let e=t?"hidden":"visible";i.ipcRendererInternal.on("GUEST_INSTANCE_VISIBILITY_CHANGE",(function(t,r){e!==r&&(e=r,document.dispatchEvent(new Event("visibilitychange")))}));const getDocumentHidden=()=>"visible"!==e;Object.defineProperty(document,"hidden",{get:getDocumentHidden}),s&&o.internalContextBridge.overrideGlobalPropertyFromIsolatedWorld(["document","hidden"],getDocumentHidden);const getDocumentVisibilityState=()=>e;Object.defineProperty(document,"visibilityState",{get:getDocumentVisibilityState}),s&&o.internalContextBridge.overrideGlobalPropertyFromIsolatedWorld(["document","visibilityState"],getDocumentVisibilityState)}}},"./lib/sandboxed_renderer/api/exports/electron.ts":(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r("./lib/common/define-properties.ts"),i=r("./lib/sandboxed_renderer/api/module-list.ts");e.exports={},(0,n.defineProperties)(e.exports,i.moduleList)},"./lib/sandboxed_renderer/api/module-list.ts":(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.moduleList=void 0,t.moduleList=[{name:"contextBridge",loader:()=>r("./lib/renderer/api/context-bridge.ts")},{name:"crashReporter",loader:()=>r("./lib/renderer/api/crash-reporter.ts")},{name:"ipcRenderer",loader:()=>r("./lib/renderer/api/ipc-renderer.ts")},{name:"nativeImage",loader:()=>r("./lib/common/api/native-image.ts")},{name:"webFrame",loader:()=>r("./lib/renderer/api/web-frame.ts")}]},"./node_modules/url/node_modules/punycode/punycode.js":function(e,t,r){var n;e=r.nmd(e),function(i){t&&t.nodeType,e&&e.nodeType;var o="object"==typeof r.g&&r.g;o.global!==o&&o.window!==o&&o.self;var s,a=2147483647,u=36,c=/^xn--/,f=/[^\x20-\x7E]/,l=/[\x2E\u3002\uFF0E\uFF61]/g,h={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},d=Math.floor,p=String.fromCharCode;function error(e){throw RangeError(h[e])}function map(e,t){for(var r=e.length,n=[];r--;)n[r]=t(e[r]);return n}function mapDomain(e,t){var r=e.split("@"),n="";return r.length>1&&(n=r[0]+"@",e=r[1]),n+map((e=e.replace(l,".")).split("."),t).join(".")}function ucs2decode(e){for(var t,r,n=[],i=0,o=e.length;i<o;)(t=e.charCodeAt(i++))>=55296&&t<=56319&&i<o?56320==(64512&(r=e.charCodeAt(i++)))?n.push(((1023&t)<<10)+(1023&r)+65536):(n.push(t),i--):n.push(t);return n}function ucs2encode(e){return map(e,(function(e){var t="";return e>65535&&(t+=p((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+=p(e)})).join("")}function digitToBasic(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function adapt(e,t,r){var n=0;for(e=r?d(e/700):e>>1,e+=d(e/t);e>455;n+=u)e=d(e/35);return d(n+36*e/(e+38))}function decode(e){var t,r,n,i,o,s,c,f,l,h,p,m=[],w=e.length,b=0,g=128,y=72;for((r=e.lastIndexOf("-"))<0&&(r=0),n=0;n<r;++n)e.charCodeAt(n)>=128&&error("not-basic"),m.push(e.charCodeAt(n));for(i=r>0?r+1:0;i<w;){for(o=b,s=1,c=u;i>=w&&error("invalid-input"),((f=(p=e.charCodeAt(i++))-48<10?p-22:p-65<26?p-65:p-97<26?p-97:u)>=u||f>d((a-b)/s))&&error("overflow"),b+=f*s,!(f<(l=c<=y?1:c>=y+26?26:c-y));c+=u)s>d(a/(h=u-l))&&error("overflow"),s*=h;y=adapt(b-o,t=m.length+1,0==o),d(b/t)>a-g&&error("overflow"),g+=d(b/t),b%=t,m.splice(b++,0,g)}return ucs2encode(m)}function encode(e){var t,r,n,i,o,s,c,f,l,h,m,w,b,g,y,v=[];for(w=(e=ucs2decode(e)).length,t=128,r=0,o=72,s=0;s<w;++s)(m=e[s])<128&&v.push(p(m));for(n=i=v.length,i&&v.push("-");n<w;){for(c=a,s=0;s<w;++s)(m=e[s])>=t&&m<c&&(c=m);for(c-t>d((a-r)/(b=n+1))&&error("overflow"),r+=(c-t)*b,t=c,s=0;s<w;++s)if((m=e[s])<t&&++r>a&&error("overflow"),m==t){for(f=r,l=u;!(f<(h=l<=o?1:l>=o+26?26:l-o));l+=u)y=f-h,g=u-h,v.push(p(digitToBasic(h+y%g,0))),f=d(y/g);v.push(p(digitToBasic(f,0))),o=adapt(r,b,n==i),r=0,++n}++r,++t}return v.join("")}s={version:"1.3.2",ucs2:{decode:ucs2decode,encode:ucs2encode},decode,encode,toASCII:function toASCII(e){return mapDomain(e,(function(e){return f.test(e)?"xn--"+encode(e):e}))},toUnicode:function toUnicode(e){return mapDomain(e,(function(e){return c.test(e)?decode(e.slice(4).toLowerCase()):e}))}},void 0===(n=function(){return s}.call(t,r,t,e))||(e.exports=n)}()},"./node_modules/url/url.js":(e,t,r)=>{var n=r("./node_modules/url/node_modules/punycode/punycode.js");function Url(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}t.parse=urlParse,t.resolve=function urlResolve(e,t){return urlParse(e,!1,!0).resolve(t)},t.resolveObject=function urlResolveObject(e,t){return e?urlParse(e,!1,!0).resolveObject(t):t},t.format=function urlFormat(e){isString(e)&&(e=urlParse(e));return e instanceof Url?e.format():Url.prototype.format.call(e)},t.Url=Url;var i=/^([a-z0-9.+-]+:)/i,o=/:[0-9]*$/,s=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),a=["'"].concat(s),u=["%","/","?",";","#"].concat(a),c=["/","?","#"],f=/^[a-z0-9A-Z_-]{0,63}$/,l=/^([a-z0-9A-Z_-]{0,63})(.*)$/,h={javascript:!0,"javascript:":!0},d={javascript:!0,"javascript:":!0},p={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},m=r("./node_modules/querystring/index.js");function urlParse(e,t,r){if(e&&isObject(e)&&e instanceof Url)return e;var n=new Url;return n.parse(e,t,r),n}function isString(e){return"string"==typeof e}function isObject(e){return"object"==typeof e&&null!==e}function isNull(e){return null===e}Url.prototype.parse=function(e,t,r){if(!isString(e))throw new TypeError("Parameter 'url' must be a string, not "+typeof e);var o=e;o=o.trim();var s=i.exec(o);if(s){var w=(s=s[0]).toLowerCase();this.protocol=w,o=o.substr(s.length)}if(r||s||o.match(/^\/\/[^@\/]+@[^@\/]+/)){var b="//"===o.substr(0,2);!b||s&&d[s]||(o=o.substr(2),this.slashes=!0)}if(!d[s]&&(b||s&&!p[s])){for(var g,y,v=-1,_=0;_<c.length;_++){-1!==(I=o.indexOf(c[_]))&&(-1===v||I<v)&&(v=I)}-1!==(y=-1===v?o.lastIndexOf("@"):o.lastIndexOf("@",v))&&(g=o.slice(0,y),o=o.slice(y+1),this.auth=decodeURIComponent(g)),v=-1;for(_=0;_<u.length;_++){var I;-1!==(I=o.indexOf(u[_]))&&(-1===v||I<v)&&(v=I)}-1===v&&(v=o.length),this.host=o.slice(0,v),o=o.slice(v),this.parseHost(),this.hostname=this.hostname||"";var B="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!B)for(var A=this.hostname.split(/\./),T=(_=0,A.length);_<T;_++){var O=A[_];if(O&&!O.match(f)){for(var L="",k=0,x=O.length;k<x;k++)O.charCodeAt(k)>127?L+="x":L+=O[k];if(!L.match(f)){var S=A.slice(0,_),R=A.slice(_+1),U=O.match(l);U&&(S.push(U[1]),R.unshift(U[2])),R.length&&(o="/"+R.join(".")+o),this.hostname=S.join(".");break}}}if(this.hostname.length>255?this.hostname="":this.hostname=this.hostname.toLowerCase(),!B){var C=this.hostname.split("."),P=[];for(_=0;_<C.length;++_){var j=C[_];P.push(j.match(/[^A-Za-z0-9_-]/)?"xn--"+n.encode(j):j)}this.hostname=P.join(".")}var N=this.port?":"+this.port:"",M=this.hostname||"";this.host=M+N,this.href+=this.host,B&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==o[0]&&(o="/"+o))}if(!h[w])for(_=0,T=a.length;_<T;_++){var W=a[_],V=encodeURIComponent(W);V===W&&(V=escape(W)),o=o.split(W).join(V)}var F=o.indexOf("#");-1!==F&&(this.hash=o.substr(F),o=o.slice(0,F));var G=o.indexOf("?");if(-1!==G?(this.search=o.substr(G),this.query=o.substr(G+1),t&&(this.query=m.parse(this.query)),o=o.slice(0,G)):t&&(this.search="",this.query={}),o&&(this.pathname=o),p[w]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){N=this.pathname||"",j=this.search||"";this.path=N+j}return this.href=this.format(),this},Url.prototype.format=function(){var e=this.auth||"";e&&(e=(e=encodeURIComponent(e)).replace(/%3A/i,":"),e+="@");var t=this.protocol||"",r=this.pathname||"",n=this.hash||"",i=!1,o="";this.host?i=e+this.host:this.hostname&&(i=e+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(i+=":"+this.port)),this.query&&isObject(this.query)&&Object.keys(this.query).length&&(o=m.stringify(this.query));var s=this.search||o&&"?"+o||"";return t&&":"!==t.substr(-1)&&(t+=":"),this.slashes||(!t||p[t])&&!1!==i?(i="//"+(i||""),r&&"/"!==r.charAt(0)&&(r="/"+r)):i||(i=""),n&&"#"!==n.charAt(0)&&(n="#"+n),s&&"?"!==s.charAt(0)&&(s="?"+s),t+i+(r=r.replace(/[?#]/g,(function(e){return encodeURIComponent(e)})))+(s=s.replace("#","%23"))+n},Url.prototype.resolve=function(e){return this.resolveObject(urlParse(e,!1,!0)).format()},Url.prototype.resolveObject=function(e){if(isString(e)){var t=new Url;t.parse(e,!1,!0),e=t}var r=new Url;if(Object.keys(this).forEach((function(e){r[e]=this[e]}),this),r.hash=e.hash,""===e.href)return r.href=r.format(),r;if(e.slashes&&!e.protocol)return Object.keys(e).forEach((function(t){"protocol"!==t&&(r[t]=e[t])})),p[r.protocol]&&r.hostname&&!r.pathname&&(r.path=r.pathname="/"),r.href=r.format(),r;if(e.protocol&&e.protocol!==r.protocol){if(!p[e.protocol])return Object.keys(e).forEach((function(t){r[t]=e[t]})),r.href=r.format(),r;if(r.protocol=e.protocol,e.host||d[e.protocol])r.pathname=e.pathname;else{for(var n=(e.pathname||"").split("/");n.length&&!(e.host=n.shift()););e.host||(e.host=""),e.hostname||(e.hostname=""),""!==n[0]&&n.unshift(""),n.length<2&&n.unshift(""),r.pathname=n.join("/")}if(r.search=e.search,r.query=e.query,r.host=e.host||"",r.auth=e.auth,r.hostname=e.hostname||e.host,r.port=e.port,r.pathname||r.search){var i=r.pathname||"",o=r.search||"";r.path=i+o}return r.slashes=r.slashes||e.slashes,r.href=r.format(),r}var s=r.pathname&&"/"===r.pathname.charAt(0),a=e.host||e.pathname&&"/"===e.pathname.charAt(0),u=a||s||r.host&&e.pathname,c=u,f=r.pathname&&r.pathname.split("/")||[],l=(n=e.pathname&&e.pathname.split("/")||[],r.protocol&&!p[r.protocol]);if(l&&(r.hostname="",r.port=null,r.host&&(""===f[0]?f[0]=r.host:f.unshift(r.host)),r.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===n[0]?n[0]=e.host:n.unshift(e.host)),e.host=null),u=u&&(""===n[0]||""===f[0])),a)r.host=e.host||""===e.host?e.host:r.host,r.hostname=e.hostname||""===e.hostname?e.hostname:r.hostname,r.search=e.search,r.query=e.query,f=n;else if(n.length)f||(f=[]),f.pop(),f=f.concat(n),r.search=e.search,r.query=e.query;else if(!function isNullOrUndefined(e){return null==e}(e.search)){if(l)r.hostname=r.host=f.shift(),(g=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=g.shift(),r.host=r.hostname=g.shift());return r.search=e.search,r.query=e.query,isNull(r.pathname)&&isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.href=r.format(),r}if(!f.length)return r.pathname=null,r.search?r.path="/"+r.search:r.path=null,r.href=r.format(),r;for(var h=f.slice(-1)[0],m=(r.host||e.host)&&("."===h||".."===h)||""===h,w=0,b=f.length;b>=0;b--)"."==(h=f[b])?f.splice(b,1):".."===h?(f.splice(b,1),w++):w&&(f.splice(b,1),w--);if(!u&&!c)for(;w--;w)f.unshift("..");!u||""===f[0]||f[0]&&"/"===f[0].charAt(0)||f.unshift(""),m&&"/"!==f.join("/").substr(-1)&&f.push("");var g,y=""===f[0]||f[0]&&"/"===f[0].charAt(0);l&&(r.hostname=r.host=y?"":f.length?f.shift():"",(g=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=g.shift(),r.host=r.hostname=g.shift()));return(u=u||r.host&&f.length)&&!y&&f.unshift(""),f.length?r.pathname=f.join("/"):(r.pathname=null,r.path=null),isNull(r.pathname)&&isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.auth=e.auth||r.auth,r.slashes=r.slashes||e.slashes,r.href=r.format(),r},Url.prototype.parseHost=function(){var e=this.host,t=o.exec(e);t&&(":"!==(t=t[0])&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)}}},t={};function __webpack_require__(r){var n=t[r];if(void 0!==n)return n.exports;var i=t[r]={id:r,loaded:!1,exports:{}};return e[r].call(i.exports,i,i.exports,__webpack_require__),i.loaded=!0,i.exports}__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),__webpack_require__.nmd=e=>(e.paths=[],e.children||(e.children=[]),e);var r={};(()=>{"use strict";var e=r,t=__webpack_require__("./node_modules/process/browser.js"),n=__webpack_require__("./node_modules/buffer/index.js").Buffer;Object.defineProperty(e,"__esModule",{value:!0});const i=__webpack_require__("./node_modules/events/events.js"),{EventEmitter:o}=i;t._linkedBinding=binding.get;const s=t._linkedBinding("electron_common_v8_util");s.setHiddenValue(__webpack_require__.g,"Buffer",n);for(const e of Object.keys(o.prototype))Object.prototype.hasOwnProperty.call(t,e)&&delete t[e];Object.setPrototypeOf(t,o.prototype);const{ipcRendererInternal:a}=__webpack_require__("./lib/renderer/ipc-renderer-internal.ts"),u=__webpack_require__("./lib/renderer/ipc-renderer-internal-utils.ts"),{preloadScripts:c,process:f}=u.invokeSync("BROWSER_SANDBOX_LOAD"),l=__webpack_require__("./lib/sandboxed_renderer/api/exports/electron.ts"),h=new Map([["electron",l],["electron/common",l],["electron/renderer",l],["events",i]]),d=new Map([["timers",()=>__webpack_require__("./node_modules/timers-browserify/main.js")],["url",()=>__webpack_require__("./node_modules/url/url.js")]]);s.setHiddenValue(__webpack_require__.g,"lifecycle",{onLoaded(){t.emit("loaded")},onExit(){t.emit("exit")},onDocumentStart(){t.emit("document-start")},onDocumentEnd(){t.emit("document-end")}});const p=new o;function preloadRequire(e){if(h.has(e))return h.get(e);if(d.has(e)){const t=d.get(e)();return h.set(e,t),t}throw new Error(`module not found: ${e}`)}Object.assign(p,binding.process),Object.assign(p,f),Object.assign(t,binding.process),Object.assign(t,f),t.getProcessMemoryInfo=p.getProcessMemoryInfo=()=>a.invoke("BROWSER_GET_PROCESS_MEMORY_INFO"),Object.defineProperty(p,"noDeprecation",{get:()=>t.noDeprecation,set(e){t.noDeprecation=e}}),t.on("loaded",(()=>p.emit("loaded"))),t.on("exit",(()=>p.emit("exit"))),t.on("document-start",(()=>p.emit("document-start"))),t.on("document-end",(()=>p.emit("document-end")));const{hasSwitch:m}=t._linkedBinding("electron_common_command_line");function runPreloadScript(e){const t=`(function(require, process, Buffer, global, setImmediate, clearImmediate, exports) {\n  ${e}\n  })`,r=binding.createPreloadScript(t),{setImmediate:i,clearImmediate:o}=__webpack_require__("./node_modules/timers-browserify/main.js");r(preloadRequire,p,n,__webpack_require__.g,i,o,{})}m("unsafely-expose-electron-internals-for-testing")&&(p._linkedBinding=t._linkedBinding),__webpack_require__("./lib/renderer/common-init.ts");for(const{preloadPath:e,preloadSrc:t,preloadError:r}of c)try{if(t)runPreloadScript(t);else if(r)throw r}catch(t){console.error(`Unable to load preload script: ${e}`),console.error(t),a.send("BROWSER_PRELOAD_ERROR",e,t)}})()})()}(globalThis.process||binding.process).argv.includes("--profile-electron-init")?setTimeout(___electron_webpack_init__,0):___electron_webpack_init__()}catch(e){console.error("Electron sandboxed_renderer.bundle.js script failed to run"),console.error(e)}