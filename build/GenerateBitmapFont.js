const generateBMFont = require("msdf-bmfont-xml");
const glob = require("glob");
const { writeFileSync, renameSync } = require("fs");
const path = require("path");
const { execSync } = require("child_process");

glob("./assets/*.ttf", (err, files) => {
   if (err) {
      return;
   }
   const preferredNames = files.map((file) => {
      file = path.normalize(file);
      const preferredName = sanitize(path.basename(file, "ttf"));
      const preferredPath = path.join(path.dirname(file), `${preferredName}.ttf`);
      if (file !== preferredPath) {
         renameSync(file, preferredPath);
      }

      generateBMFont(preferredPath, { filename: preferredName, smartSize: true }, (err, textures, font) => {
         if (err) {
            return;
         }
         textures.forEach((t) => writeFileSync(`./public/fonts/${path.basename(t.filename)}.png`, t.texture));
         writeFileSync(`./public/fonts/${path.basename(font.filename)}`, font.data);
      });
      return preferredName;
   });
   const scriptContent = ["// This file is generated by running `npm run font`. Do not modify directly"];
   scriptContent.push("export const Fonts = {");
   preferredNames.forEach((f) => {
      scriptContent.push(`${f}:'${f}',`);
   });
   scriptContent.push("};");
   scriptContent.push(
      `export const fontBundle = { ${preferredNames.map((f) => `${f}:"./fonts/${f}.fnt"`).join(",")} };`,
   );
   writeFileSync("./src/scripts/generated/FontBundle.ts", scriptContent.join("\n"));
   execSync("npx prettier --config .prettierrc.json --write ./src/scripts/generated/FontBundle.ts");
});

function sanitize(str) {
   return str.replace(/[^0-9a-z]/gi, "");
}
