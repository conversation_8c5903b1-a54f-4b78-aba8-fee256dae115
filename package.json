{"name": "CivIdle", "private": true, "version": "0.0.0", "packageManager": "pnpm@8.14.2", "scripts": {"dev": "vite", "watch": "tsc --build --incremental --watch tsconfig.watch.json", "compile": "tsc --build --force tsconfig.json", "build": "pnpm run compile && vite build", "server": "cd server && npm run start", "electron": "cd electron && npm run start", "optimize": "node ./build/OptimizePNG.js", "preview": "vite preview", "font": "node ./build/GenerateBitmapFont.js", "release": "node ./build/ReleaseElectron.js", "translate": "node ./build/Translate.js", "achievement": "node ./build/Achievement.js", "releaseAndroid": "pnpm run build && pnpm run optimize && npx cap sync android && cd ./android && gradlew :app:bundleRelease", "releaseIOS": "pnpm run build && pnpm run optimize && npx cap sync ios", "ci": "biome ci ./src/scripts/ ./shared/ ./server/ --diagnostic-level=error --config-path=./"}, "dependencies": {"@capacitor/android": "^5.6.0", "@capacitor/app": "^5.0.8", "@capacitor/core": "^5.6.0", "@capacitor/ios": "^5.6.0", "@capacitor/preferences": "^5.0.0", "@msgpack/msgpack": "^3.0.0-beta2", "@pixi/filter-outline": "^5.2.0", "@pixi/graphics-smooth": "^1.1.0", "@pixi/sound": "^5.2.0", "@sentry/browser": "^8.47.0", "@tippyjs/react": "^4.2.6", "classnames": "^2.3.1", "fflate": "^0.8.2", "install": "^0.13.0", "npm": "^10.9.0", "pixi.js": "7.4.2", "randomcolor": "^0.6.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-modal": "^3.15.1", "react-virtuoso": "^4.7.11", "tippy.js": "^6.3.7", "uplot": "^1.6.30", "xp.css": "^0.3.0"}, "devDependencies": {"7zip-bin": "^5.2.0", "@biomejs/biome": "1.9.4", "@capacitor/assets": "^3.0.5", "@capacitor/cli": "^5.6.0", "@types/gaze": "^1.1.2", "@types/node": "^18.7.23", "@types/pako": "^2.0.3", "@types/randomcolor": "^0.5.9", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.8", "@types/react-modal": "^3.13.1", "@types/uuid": "^9.0.0", "@vitejs/plugin-react-swc": "^3.6.0", "fs-extra": "^11.1.0", "gaze": "^1.1.3", "glob": "^8.0.3", "madge": "^6.1.0", "node-7z": "^3.0.0", "rimraf": "^5.0.7", "typescript": "5.4.3", "vite": "^4.5.3", "vite-plugin-spritesmith": "github:fishpondstudio/vite-plugin-spritesmith"}}