name: Build Win/Linux Steam (Beta)
on:
  push:
    branches:
      - main
  workflow_dispatch:
jobs:
  run-script:
    runs-on: [self-hosted]
    if: contains(github.event.head_commit.message, '[build]') || github.event_name == 'workflow_dispatch'
    steps:
      - name: GitHub context
        env:
          GITHUB_CONTEXT: ${{ toJson(github) }}
        run: echo "$GITHUB_CONTEXT"
      - name: Job context
        env:
          JOB_CONTEXT: ${{ toJson(job) }}
        run: echo "$JOB_CONTEXT"
      - run: |
          git checkout .
          git pull
          git submodule update --recursive --remote --init
          pnpm install
        working-directory: /home/<USER>/CivIdle
      - run: |
          npm install
        working-directory: /home/<USER>/CivIdle/electron
      - run: |
          pnpm install
          pnpm run ci
        working-directory: /home/<USER>/CivIdle/test
      - run: |
          npm install
        working-directory: /home/<USER>/CivIdle/server
      - run: |
          npm run clean
        working-directory: /home/<USER>/CivIdle/shared
      - run: npm run release
        working-directory: /home/<USER>/CivIdle
        env:
          STEAMWORKS_PATH: /home/<USER>/steamworks/tools/ContentBuilder/
      - run: npx cap sync android
        working-directory: /home/<USER>/CivIdle
      - run: fastlane deploy
        working-directory: /home/<USER>/CivIdle/android
        env:
          ANDROID_HOME: /home/<USER>/android-sdk
      - run: |
          git add src/scripts/Version.json
          git add android/version.properties
          git commit -m "[skip ci] New build" || echo "There's nothing to commit"
          git push
        working-directory: /home/<USER>/CivIdle
